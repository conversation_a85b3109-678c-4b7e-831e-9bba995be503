package com.goclouds.crm.platform.call.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goclouds.crm.platform.call.domain.dto.AssessmentBuildDataDTO;
import com.goclouds.crm.platform.call.domain.vo.AssessmentDetailVO;
import com.goclouds.crm.platform.call.service.CrmTicketAssessmentRecordService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.AIGCModule;
import com.goclouds.crm.platform.common.enums.ResultCodeEnum;
import com.goclouds.crm.platform.common.message.ReceviceRedisMessageListener;
import com.goclouds.crm.platform.common.utils.MessageI18NUtils;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.utils.AIGCRequestCheckUtil;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.ConcurrentWebSocketSessionDecorator;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 自定义WebSocket，集成基础ws事件，在对应事件做出对应操作。
 */
@Slf4j
public class MyWebSocketHandler extends TextWebSocketHandler implements ReceviceRedisMessageListener {

    // 存储所有连接的客户端
    private static final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    @Resource
    private CrmTicketAssessmentRecordService recordService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 配置参数
    private static final int SEND_TIME_LIMIT = 10000; // 发送超时时间(毫秒)
    private static final int BUFFER_SIZE_LIMIT = 512 * 1024; // 缓冲区大小(字节)

    @Override
    public void onMessage(Message message, byte[] pattern) {
        final byte[] messageBody = message.getBody();
        if (messageBody == null || messageBody.length == 0) {
            log.warn("收到空消息体");
            return;
        }

        try {
            // 1. 获取消息体并处理多层转义JSON
            String rawMessage = new String(messageBody, StandardCharsets.UTF_8);

            // 处理可能的多层JSON转义
            String normalizedJson;
            try {
                normalizedJson = objectMapper.readValue(rawMessage, String.class);
            } catch (JsonProcessingException e) {
                log.error("消息体不是有效的转义JSON格式: {}", rawMessage);
                return;
            }

            // 2. 解析外层JSON
            JSONObject jsonObject;
            try {
                jsonObject = JSONObject.parseObject(normalizedJson);
            } catch (Exception e) {
                log.error("解析外层JSON失败: {}", normalizedJson, e);
                return;
            }

            // 3. 验证必要字段
            if (!jsonObject.containsKey("receiverId") || !jsonObject.containsKey("content")) {
                log.error("消息缺少必要字段(receiverId或content): {}", jsonObject.toJSONString());
                return;
            }

            String receiverId = jsonObject.getString("receiverId");
            String contentStr = jsonObject.getString("content");

            // 4. 解析content字段
            JSONObject contentObject;
            try {
                contentObject = JSONObject.parseObject(contentStr);
            } catch (Exception e) {
                log.error("解析content字段失败: {}", contentStr, e);
                return;
            }

            // 5. 验证content字段结构
            if (!contentObject.containsKey("type") || !contentObject.containsKey("data")) {
                log.error("content字段缺少必要字段(type或data): {}", contentObject.toJSONString());
                return;
            }

            // 6. 获取WebSocket会话
            WebSocketSession session = sessions.get(receiverId);
            if (session == null) {
                log.warn("接收者{}没有对应的WebSocket会话", receiverId);
                return;
            }
            if (!session.isOpen()) {
                log.warn("接收者{}的WebSocket会话已关闭", receiverId);
                sessions.remove(receiverId); // 清理无效会话
                return;
            }

            // 7. 发送消息
            try {
                String type = contentObject.getString("type");
                JSONObject data = contentObject.getJSONObject("data");
                sendMessage(session, type, data);
            } catch (Exception e) {
                log.error("发送WebSocket消息失败，接收者: {}", receiverId, e);
            }
        } catch (Exception e) {
            log.error("处理Redis消息时发生未预期异常", e);
        }
    }

    @Resource
    private AIGCRequestCheckUtil aigcRequestCheckUtil;
    /**
     * WEBSocket连接事件。
     *
     * @param session ws信息
     * @throws Exception 异常信息
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        // 获取连接参数
        String companyId = (String) session.getAttributes().get("companyId"); // 公司ID
        String userId = (String) session.getAttributes().get("userId"); // 评估人ID

        String ticketId = (String) session.getAttributes().get("ticketId"); // 评估工单ID
        String assessmentId = (String) session.getAttributes().get("assessmentId"); // 评估表ID
        String assessmentVersionId = (String) session.getAttributes().get("assessmentVersionId"); // 评估表版本ID

        String channelTypeId = (String) session.getAttributes().get("channelTypeId"); // 工单渠道类型ID
//        String ticketType = (String) session.getAttributes().get("ticketType"); // 评估工单类型
//        String ticketTypeName = (String) session.getAttributes().get("ticketTypeName"); // 评估工单类型名称
//        String assessedAgentId = (String) session.getAttributes().get("assessedAgentId");// 评估工单接待的坐席ID（被评估人ID）

        String assessmentRecordId = (String) session.getAttributes().get("assessmentRecordId"); // 评估记录ID，可以为空

        // 生成唯一标识
        String sessionId = assessmentId + "_" + assessmentVersionId + "_" + userId;
//        sessions.put(sessionId, session);
        WebSocketSession concurrentSession = new ConcurrentWebSocketSessionDecorator(
                session, SEND_TIME_LIMIT, BUFFER_SIZE_LIMIT);
        sessions.put(sessionId, concurrentSession);

        AssessmentBuildDataDTO assessmentBuildDataDTO = new AssessmentBuildDataDTO();
        assessmentBuildDataDTO.setCompanyId(companyId);
        assessmentBuildDataDTO.setTicketId(ticketId);
//        assessmentBuildDataDTO.setTicketType(ticketType);
//        assessmentBuildDataDTO.setTicketTypeName(ticketTypeName);
//        assessmentBuildDataDTO.setAssessedAgentId(assessedAgentId);
        assessmentBuildDataDTO.setChannelTypeId(channelTypeId);
        assessmentBuildDataDTO.setUserId(userId);
        assessmentBuildDataDTO.setAssessmentId(assessmentId);
        assessmentBuildDataDTO.setVersionId(assessmentVersionId);
        // 评估记录ID
        assessmentBuildDataDTO.setRecordId(assessmentRecordId);

        // 调用评估记录获取评估数据
        AssessmentDetailVO assessmentDetailVO = recordService.generateAssessmentRecord(assessmentBuildDataDTO);
        if (null == assessmentDetailVO) {
            JSONObject msg = new JSONObject();
            msg.put("msg", MessageI18NUtils.get(ResultCodeEnum.CALL_CENTER_TICKET_NOT_CHAT_HISTORY.getMessage()));
            sendMessage(session, "ASSESSMENT_TASK_FAILED", msg);
            return;
        }
        // todo 调用AIGC判断是否计费次数不足。
        String failMessage = null;
        if(!aigcRequestCheckUtil.requestCheck(companyId, AIGCModule.AI_Agent_OUTER.getCode())){
            log.info("调用 aiAgent AIGC计费校验失败 不可调用{}",companyId);
            failMessage =MessageUtils.get("ai.agent.usage.limit");
        }
        sendMessage(session, "ASSESSMENT_DATA", assessmentDetailVO, failMessage);

        // 如果是进行中则查询下是否评估完成了。
        if (assessmentDetailVO.getAssessmentStatus() == 1) {
            Integer unAssessmentRuleCount = recordService.getUnAssessmentRuleCount(assessmentDetailVO.getAssessmentRecordId());
            if (unAssessmentRuleCount == 0) {
                // 推送评估完成了事件
                sendAssessmentCompleted(session, assessmentRecordId);
            }
        }
    }

    /**
     * WEBSocket接受消息事件。
     *
     * @param session ws信息
     * @param message ws消息
     * @throws IOException io异常
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws IOException {
        // 消息类型。
        String payload = message.getPayload();
        log.info("收到客户端消息: {}", payload);
        JSONObject jsonObject = JSONObject.parseObject(payload);
        String type = jsonObject.getString("type");
        JSONObject data = jsonObject.getJSONObject("data");
        log.info("收到客户端消息 - data: {}", data);

        // 1. 修改AI得分。（需响应给前端一条消息）。
        if ("AI_SCORE_UPDATED".equals(type)) {

            String assessmentRecordId = data.getString("assessmentRecordId");
            String assessmentRecordRuleItemId = data.getString("assessmentRecordRuleItemId");
            Integer manualScore = data.getInteger("manualScore");
            Integer scoreRule = data.getInteger("scoreRule");

            if (StringUtil.isBlank(assessmentRecordId) ||
                    StringUtil.isBlank(assessmentRecordRuleItemId) ||
                    null == manualScore || null == scoreRule) {
                return;
            }


            // 执行修改人工得分字段，根据指定记录规则项ID
            BigDecimal lastScore = recordService.handleManualScoreUpdated(assessmentRecordId, assessmentRecordRuleItemId, BigDecimal.valueOf(manualScore), scoreRule);

            if (null != lastScore) {
                // 发送总分情况。
                JSONObject wsMessage = new JSONObject();
                wsMessage.put("assessmentRecordId", assessmentRecordId);
                wsMessage.put("totalScore", lastScore);
                sendMessage(session, "AI_SCORE_UPDATED", wsMessage);

                // 未评估完成的规则数量
                Integer unAssessmentRuleCount = recordService.getUnAssessmentRuleCount(assessmentRecordId);
                if (unAssessmentRuleCount == 0) {
                    // 发送已完成事件。
                    sendAssessmentCompleted(session, assessmentRecordId);
                }
            }
            return;
        }

        // 2. 添加备注。
        if ("ASSESSMENT_REMARK_ADDED".equals(type)) {

            String assessmentRecordId = data.getString("assessmentRecordId");
            String assessmentRecordRuleItemId = data.getString("assessmentRecordRuleItemId");
            String assessmentRemark = data.getString("assessmentRemark");
            if (StringUtil.isBlank(assessmentRecordId) ||
                    StringUtil.isBlank(assessmentRecordRuleItemId) ||
                    StringUtil.isBlank(assessmentRemark)) {
                return;
            }

            // 执行修改备注字段，根据指定记录规则项ID
            recordService.handleAssessmentRemarkAdded(assessmentRecordId, assessmentRecordRuleItemId, assessmentRemark);
            return;
        }

        // 3. 人工打分（需响应给前端一条消息）。
        if ("MANUAL_SCORE_SUBMITTED".equals(type)) {

            String assessmentRecordId = data.getString("assessmentRecordId");
            String assessmentRecordRuleItemId = data.getString("assessmentRecordRuleItemId");
            String manualOptionId = data.getString("manualOptionId");
            String manualOptionName = data.getString("manualOptionName");
            Integer manualScore = data.getInteger("manualScore");
            Integer scoreRule = data.getInteger("scoreRule");

            if (StringUtil.isBlank(assessmentRecordId) ||
                    StringUtil.isBlank(assessmentRecordRuleItemId) ||
                    StringUtil.isBlank(manualOptionId) ||
                    StringUtil.isBlank(manualOptionName) ||
                    null == manualScore || null == scoreRule) {
                return;
            }

            // 执行修改人工打分的选择项ID、选择项名称、选项项分数值。根据记录规则项ID。
            BigDecimal lastScore = recordService.handleManualScoreSubmitted(assessmentRecordId, assessmentRecordRuleItemId, manualOptionId, manualOptionName, BigDecimal.valueOf(manualScore), scoreRule);
            if (null != lastScore) {
                // 发送总分情况。
                JSONObject wsMessage = new JSONObject();
                wsMessage.put("assessmentRecordId", assessmentRecordId);
                wsMessage.put("totalScore", lastScore);
                sendMessage(session, "MANUAL_SCORE_SUBMITTED", wsMessage);

                // 未评估完成的规则数量
                Integer unAssessmentRuleCount = recordService.getUnAssessmentRuleCount(assessmentRecordId);
                if (unAssessmentRuleCount == 0) {
                    // 发送已完成事件。
                    sendAssessmentCompleted(session, assessmentRecordId);
                }
            }
            return;
        }

    }

    /**
     * WEBSocket断开事件。
     *
     * @param session ws信息
     * @param status  关闭状态
     * @throws Exception 异常信息
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        // 移除关闭的连接
        sessions.values().remove(session);
        System.out.println("客户端连接关闭: " + status);
    }

    /**
     * WEBSocket异常断开事件。
     *
     * @param session   ws信息
     * @param exception 异常信息
     * @throws Exception 异常信息
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        System.out.println("WebSocket传输错误: " + exception.getMessage());
        if (session.isOpen()) {
            session.close();
        }
        sessions.values().remove(session);
    }

    /**
     * WEBSocket发送消息事件。
     *
     * @param session ws信息
     * @param type    事件类型
     * @param data    事件数据
     */
    public void sendMessage(WebSocketSession session, String type, Object data) {
        try {
            Map<String, Object> message = new HashMap<>();
            message.put("type", type);
            message.put("data", data);

            session.sendMessage(new TextMessage(JSON.toJSONString(message, SerializerFeature.WriteMapNullValue)));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    /**
     * WEBSocket发送消息事件。
     *
     * @param session ws信息
     * @param type    事件类型
     * @param data    事件数据
     */
    public void sendMessage(WebSocketSession session, String type, Object data, String failMessage) {
        try {
            Map<String, Object> message = new HashMap<>();
            message.put("type", type);
            message.put("data", data);
            if (StringUtil.isNotBlank(failMessage)) {
                message.put("msg", failMessage);
            }
            session.sendMessage(new TextMessage(JSON.toJSONString(message, SerializerFeature.WriteMapNullValue)));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取WEBSocket连接信息。
     *
     * @param sessionId 自定义sessionId
     * @return WEBSocket连接信息
     */
    public WebSocketSession getSession(String sessionId) {
        return sessions.get(sessionId);
    }

    /**
     * 发送已评估完成的事件
     *
     * @param session  ws连接信息
     * @param recordId 评估记录ID
     */
    public void sendAssessmentCompleted(WebSocketSession session, String recordId) {
        if (null == session) {
            return;
        }
        JSONObject data = new JSONObject();
        data.put("assessmentRecordId", recordId);
        sendMessage(session, "ASSESSMENT_COMPLETED", data);
    }

}