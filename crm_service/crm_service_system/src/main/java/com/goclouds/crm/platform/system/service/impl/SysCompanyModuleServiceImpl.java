package com.goclouds.crm.platform.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.system.domain.SysCompanyModule;
import com.goclouds.crm.platform.system.service.SysCompanyModuleService;
import com.goclouds.crm.platform.system.mapper.SysCompanyModuleMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_company_module(公司模块表)】的数据库操作Service实现
* @createDate 2025-03-10 14:13:32
*/
@Service
public class SysCompanyModuleServiceImpl extends ServiceImpl<SysCompanyModuleMapper, SysCompanyModule>
    implements SysCompanyModuleService{

    /**
     * 获取公司所有的配置模块
     *
     * @param companyId 公司ID
     * @return 配置的模块列表
     */
    @Override
    public List<SysCompanyModule> getAllCompanyModule(String companyId) {
        LambdaQueryWrapper<SysCompanyModule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysCompanyModule::getCompanyId, companyId);
        return this.list(wrapper);
    }
}




