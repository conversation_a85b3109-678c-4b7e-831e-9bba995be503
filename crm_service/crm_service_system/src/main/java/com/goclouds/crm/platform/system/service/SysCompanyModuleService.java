package com.goclouds.crm.platform.system.service;

import com.goclouds.crm.platform.system.domain.SysCompanyModule;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_company_module(公司模块表)】的数据库操作Service
* @createDate 2025-03-10 14:13:32
*/
public interface SysCompanyModuleService extends IService<SysCompanyModule> {

    /**
     * 获取公司所有的配置模块
     *
     * @param companyId 公司ID
     * @return 配置的模块列表
     */
    List<SysCompanyModule> getAllCompanyModule(String companyId);

}
