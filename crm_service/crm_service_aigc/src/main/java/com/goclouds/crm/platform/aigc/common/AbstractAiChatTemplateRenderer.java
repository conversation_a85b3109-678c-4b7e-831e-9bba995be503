package com.goclouds.crm.platform.aigc.common;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> pengliang.sun
 * @description : 抽象模版类 --新版AI调用方法
 */

@Slf4j
public abstract class AbstractAiChatTemplateRenderer {


    /**
     * todo 这里入参需要优化，改为对象的结构
     * --String modelFunction 模型功能
     * --variables AI调用传参--模版参数
     * --不同模型返回的json格式可能会不一样，因此解析json不因在此处解析
     * --Map参数 key 为 prompt 中的占位符，value 为对应的值
     *
     *
     * @param modelFunction
     */
    public String execute(String modelFunction, Map<String, String> variables,String companyId,String llmPath) {
        log.info("AbstractAiChatTemplateRenderer->execute modelFunction:[{}],companyId:[{}],variables:[{}],llmPath:[{}]"
                ,modelFunction,companyId,variables,llmPath);
        PerAiChatQueryRequest perAiChatQueryRequest = constParam(modelFunction,companyId);
        String prompt = preConvertPromptParam(perAiChatQueryRequest.getPrompt(), variables);
        perAiChatQueryRequest.setPrompt(prompt);
        return perAiChatQuery(perAiChatQueryRequest,llmPath);
    }

    /**
     * 请求LLM，参数带上下文的。
     * @param modelFunction
     * @param variables
     * @param companyId
     * @param llmPath
     * @param chatHistory llm上下文
     * @return
     */
    public String execute(String modelFunction, Map<String, String> variables, String companyId,
                          String llmPath, List<JSONObject> chatHistory) {
        log.info("AbstractAiChatTemplateRenderer->execute modelFunction:[{}],companyId:[{}],variables:[{}],llmPath:[{}],chatHistory:[{}]"
                ,modelFunction,companyId,variables,llmPath,chatHistory);
        PerAiChatQueryRequest perAiChatQueryRequest = constParam(modelFunction,companyId);
        String prompt = preConvertPromptParam(perAiChatQueryRequest.getPrompt(), variables);
        perAiChatQueryRequest.setPrompt(prompt);
        perAiChatQueryRequest.setChatHistory(chatHistory);
        return perAiChatQuery(perAiChatQueryRequest,llmPath);
    }


    /**
     * 重载方法
     *
     * @param modelFunction
     */
    public String execute(String modelFunction, Map<String, String> variables,String companyId,
                          String llmPath,List<JSONObject> chatHistory,String systemRole) {
        log.info("AbstractAiChatTemplateRenderer->execute modelFunction:[{}],companyId:[{}],variables:[{}],llmPath:[{}],chatHistory:[{}],systemRole:[{}]"
                ,modelFunction,companyId,variables,llmPath, chatHistory, systemRole);
        PerAiChatQueryRequest perAiChatQueryRequest = constParam(modelFunction,companyId);
        String prompt = preConvertPromptParam(perAiChatQueryRequest.getPrompt(), variables);
        perAiChatQueryRequest.setPrompt(prompt);
        perAiChatQueryRequest.setSystemRole(systemRole);
        perAiChatQueryRequest.setChatHistory(chatHistory);
        return perAiChatQuery(perAiChatQueryRequest,llmPath);
    }



    /**
     * 重载方法
     * aiAgentExecute llm 信息采集
     *
     * @param modelFunction
     */
    public String aiAgentExecute(String modelFunction, Map<String, String> variables,String companyId,
                          String llmPath,List<JSONObject> chatHistory,String prompt) {
        log.info("AbstractAiChatTemplateRenderer->execute modelFunction:[{}],companyId:[{}],variables:[{}],llmPath:[{}],chatHistory:[{}],prompt:[{}]"
                ,modelFunction,companyId,variables,llmPath, chatHistory, prompt);
        PerAiChatQueryRequest perAiChatQueryRequest = constParam(modelFunction,companyId);
        // 这里拿出的是提示词
        String systemRole = preConvertPromptParam(perAiChatQueryRequest.getPrompt(), variables);
        perAiChatQueryRequest.setPrompt(prompt);
        perAiChatQueryRequest.setSystemRole(systemRole);
        perAiChatQueryRequest.setChatHistory(chatHistory);
        return perAiChatQuery(perAiChatQueryRequest,llmPath);
    }

    protected abstract PerAiChatQueryRequest constParam(String modelFunction, String companyId);


    private String preConvertPromptParam(String template, Map<String, String> variables) {
        if (variables == null) {
            variables = Collections.emptyMap();
        }
        Pattern pattern = Pattern.compile("\\$\\{(\\w+)}");
        Matcher matcher = pattern.matcher(template);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String key = matcher.group(1);
            String value = variables.getOrDefault(key, "${" + key + "}");
            matcher.appendReplacement(result, value != null ? value : "");
        }
        matcher.appendTail(result);
        return result.toString();
    }


    private String perAiChatQuery(PerAiChatQueryRequest perAiChatQueryRequest, String llmPath) {
        JSONObject jsonObject = new JSONObject();
        JSONObject conf = new JSONObject();
        conf.put("temperature", 0.1);
        conf.put("max_tokens", 2048);
        conf.put("stop", "</response>");
//        conf.put("top_k", 200);
        conf.put("top_p", 0.2);
        if (StringUtils.isNotBlank(perAiChatQueryRequest.getSystemRole())) {
            conf.put("system_role", perAiChatQueryRequest.getSystemRole());
        }else {
            conf.put("system_role", "");
        }
        jsonObject.put("llm_model_type", perAiChatQueryRequest.getModelType());
        JSONObject param = new JSONObject();
        param.put("knn_faq_threshold", 1.95);
        param.put("knn_paragraph_threshold", 1.55);
        param.put("knn_sentence_threshold", "1.4");
        param.put("timeout", "300");
        param.put("model_id", perAiChatQueryRequest.getModelId());
        param.put("api_key", perAiChatQueryRequest.getApiKey());
        param.put("knn_intent_threshold", 1.6);
        param.put("credentials", perAiChatQueryRequest.getCredentials());
        param.put("location", perAiChatQueryRequest.getLocation());
        param.put("anthropic_version", perAiChatQueryRequest.getAnthropicVersion());
        param.put("model_version", perAiChatQueryRequest.getModelVersion());
        param.put("output_sku", perAiChatQueryRequest.getOutputSku());
        param.put("input_sku", perAiChatQueryRequest.getInputSku());
        param.put("region", perAiChatQueryRequest.getRegion());
        param.put("ak", perAiChatQueryRequest.getAk());
        param.put("sk", perAiChatQueryRequest.getSk());
        param.put("project_id", perAiChatQueryRequest.getProjectId());
        jsonObject.put("llm_model_param", param);
        jsonObject.put("prompt_conf", conf);
        jsonObject.put("prompt", perAiChatQueryRequest.getPrompt());
        jsonObject.put("use_stream", false);
        jsonObject.put("company_id", perAiChatQueryRequest.getCompanyId());
        jsonObject.put("origin_prompt", perAiChatQueryRequest.getPrompt());
        jsonObject.put("func_name", "All-Know-Content-Review");
        jsonObject.put("extend_attr", new JSONObject());
        // 聊天上下文，如果存在
        if (CollectionUtils.isNotEmpty(perAiChatQueryRequest.getChatHistory())) {
            jsonObject.put("chat_history", perAiChatQueryRequest.getChatHistory());
        }
        OkHttpClient client = new OkHttpClient().newBuilder()
                .readTimeout(100000, TimeUnit.MILLISECONDS)
                .writeTimeout(100000, TimeUnit.MILLISECONDS)
                .build();
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonObject.toJSONString());
        log.info("请求参数：{}", jsonObject.toJSONString());
        log.info("请求路径：{}", llmPath);
        Request request = new Request.Builder()
                .url(llmPath + "/llm")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = null;
        String llm=null;
        try {
            response = client.newCall(request).execute();
            llm = response.body().string();
            log.info("AI模型返回值：【{}】",llm);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        boolean successful = response.isSuccessful();
        if (successful) {
            try {
                return llm;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }


    /**
     * 调用例子
     * @param args
     */
//    public static void main(String[] args) {
//        String template = "${theme}</title></head><body><h1>${theme}</h1><p>整体营销创意为：${MarketCreative}。在这个炎炎夏日，我们为您准备了特别的${keyword}活动！</p></body></html>";
//
//        Map<String, String> variables = org.elasticsearch.core.Map.of(
//                "theme", "夏日促销",
//                "MarketCreative", "清凉一夏，特惠来袭",
//                "keyword", "折扣"
//        );
//        String result = connect(template, variables);
//        System.out.println(result);
//    }

}
