# 音频格式选择功能说明

## 🎯 功能概述

在 `websocket-bridge2-test.html` 页面的"🎤 实时音频录制"部分添加了音频格式选择功能，支持PCM和μ-law两种音频格式，并根据选择的格式进行相应的音频转换和参数传递。

## 🔧 新增功能

### 1. 音频格式选择器
- **PCM格式**: 16000Hz采样率，适用于高质量音频
- **μ-law格式**: 8000Hz采样率，适用于电话音质

### 2. 动态采样率显示
- 根据选择的格式自动显示对应的采样率
- PCM: 16000 Hz
- μ-law: 8000 Hz

### 3. 智能音频转换
- **PCM格式**: Float32 → PCM 16位 → Base64
- **μ-law格式**: Float32 → PCM 16位 → μ-law编码 → Base64

## 📊 修改详情

### 1. UI界面修改

#### 新增控件
```html
<div class="format-control">
    <label for="audioFormat">音频格式:</label>
    <select id="audioFormat" onchange="onAudioFormatChange()">
        <option value="pcm">PCM (16000Hz)</option>
        <option value="mulaw">μ-law (8000Hz)</option>
    </select>
</div>
<div class="sample-rate-info">
    <label>采样率:</label>
    <span id="sampleRateDisplay">16000 Hz</span>
</div>
```

#### 新增CSS样式
```css
.format-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sample-rate-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #666;
}
```

### 2. JavaScript功能修改

#### 格式变化处理
```javascript
function onAudioFormatChange() {
    const audioFormat = document.getElementById('audioFormat').value;
    const sampleRateDisplay = document.getElementById('sampleRateDisplay');
    
    if (audioFormat === 'pcm') {
        sampleRateDisplay.textContent = '16000 Hz';
    } else if (audioFormat === 'mulaw') {
        sampleRateDisplay.textContent = '8000 Hz';
    }
}
```

#### μ-law编码算法
```javascript
function linearToMulaw(sample) {
    const BIAS = 0x84;
    const CLIP = 32635;
    
    let sign = (sample >> 8) & 0x80;
    if (sign !== 0) sample = -sample;
    if (sample > CLIP) sample = CLIP;
    
    sample = sample + BIAS;
    let exponent = 7;
    let expMask = 0x4000;
    
    for (let i = 0; i < 8; i++) {
        if ((sample & expMask) !== 0) break;
        exponent--;
        expMask >>= 1;
    }
    
    let mantissa = (sample >> (exponent + 3)) & 0x0F;
    let ulawByte = ~(sign | (exponent << 4) | mantissa);
    
    return ulawByte & 0xFF;
}
```

#### PCM到μ-law转换
```javascript
function convertPCMToMulaw(pcmData) {
    const mulawData = new Uint8Array(pcmData.length / 2);
    const view = new DataView(pcmData);
    
    for (let i = 0; i < mulawData.length; i++) {
        const sample = view.getInt16(i * 2, true); // little-endian
        mulawData[i] = linearToMulaw(sample);
    }
    
    return mulawData;
}
```

### 3. 录音逻辑修改

#### 动态采样率配置
```javascript
const audioFormat = document.getElementById('audioFormat').value;
const targetSampleRate = audioFormat === 'mulaw' ? 8000 : 16000;

recordingStream = await navigator.mediaDevices.getUserMedia({
    audio: {
        sampleRate: targetSampleRate,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
    }
});
```

#### 格式化音频处理
```javascript
if (audioFormat === 'mulaw') {
    const pcmData = convertToPCM16(adjustedData);
    const mulawData = convertPCMToMulaw(pcmData);
    audioData = mulawData;
    format = 'mulaw';
    sampleRate = '8000';
} else {
    const pcmData = convertToPCM16(adjustedData);
    audioData = new Uint8Array(pcmData);
    format = 'pcm';
    sampleRate = '16000';
}
```

### 4. 数据传输修改

#### 增强的sendAudioDataRealtime函数
```javascript
async function sendAudioDataRealtime(audioData, format = 'pcm', sampleRate = '16000') {
    // 安全的Base64编码（避免apply参数过多的问题）
    let base64Audio = '';
    const chunkSize = 8192;
    for (let i = 0; i < uint8Array.length; i += chunkSize) {
        const chunk = uint8Array.slice(i, i + chunkSize);
        base64Audio += btoa(String.fromCharCode.apply(null, chunk));
    }
    
    const requestBody = {
        audioData: base64Audio,
        format: format,
        sampleRate: parseInt(sampleRate)
    };
}
```

## 🧪 测试功能

### 新增测试按钮
- **🔧 测试μ-law转换**: 生成测试音频并验证μ-law转换功能

### 调试信息
- 详细的转换过程日志
- 数据长度和格式验证
- Base64编码状态监控

## 🔍 问题修复

### 原问题分析
1. **audioData为空**: μ-law转换后数据丢失
2. **Base64编码失败**: `String.fromCharCode.apply`在大数组时可能失败
3. **数据类型不匹配**: Uint8Array和ArrayBuffer混用

### 修复方案
1. **增强调试日志**: 跟踪每个转换步骤
2. **安全Base64编码**: 分块处理避免apply限制
3. **数据验证**: 检查每个步骤的数据完整性
4. **测试功能**: 独立测试μ-law转换

## 📋 使用方法

### 1. 选择音频格式
1. 在"🎤 实时音频录制"部分找到"音频格式"下拉框
2. 选择"PCM (16000Hz)"或"μ-law (8000Hz)"
3. 观察采样率显示自动更新

### 2. 测试转换功能
1. 点击"🔧 测试μ-law转换"按钮
2. 查看日志中的转换过程和结果
3. 验证Base64数据是否正确生成

### 3. 录音测试
1. 建立WebSocket连接
2. 选择所需的音频格式
3. 开始录音，观察日志中的格式信息
4. 检查发送的数据是否包含正确的format和sampleRate

## ⚠️ 注意事项

1. **格式切换**: 录音过程中切换格式需要重新开始录音
2. **采样率匹配**: 确保前端采样率与后端期望一致
3. **数据大小**: μ-law格式数据量约为PCM的一半
4. **浏览器兼容性**: 确保浏览器支持所选采样率

## 🔧 故障排除

### 如果audioData仍为空
1. 打开浏览器开发者工具查看控制台日志
2. 检查"发送音频数据调试"日志中的数据长度
3. 验证μ-law转换过程是否正常
4. 使用"🔧 测试μ-law转换"按钮独立测试

### 常见问题
- **权限问题**: 确保麦克风权限已授权
- **采样率问题**: 某些设备可能不支持8000Hz采样率
- **数据量问题**: μ-law数据量较小，发送频率可能需要调整

---

**修改时间**: 2025-08-05  
**功能状态**: ✅ 已完成  
**测试状态**: 🧪 待验证

现在您可以选择不同的音频格式进行录音，系统会自动进行相应的格式转换和参数传递！
