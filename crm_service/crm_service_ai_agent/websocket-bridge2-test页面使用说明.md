# WebSocket Bridge 2.0 测试页面使用说明

## 📋 页面概述

新创建的 `websocket-bridge2-test.html` 页面是基于 wsbridge2 功能模块的实时音频测试页面，与原 `websocket-realtime-audio.html` 页面功能完全一致，但使用了 wsbridge2 的后端服务。

## 🎯 功能对比

| 功能模块 | 原页面 | 新页面 (wsbridge2) | 状态 |
|---------|-------|------------------|------|
| 认证配置 | ✅ Bearer Token认证 | ✅ Bearer Token认证 | ✅ 完全一致 |
| WebSocket连接 | ✅ 连接/断开控制 | ✅ 连接/断开控制 | ✅ 完全一致 |
| 实时录音 | ✅ 开始/停止录音 | ✅ 开始/停止录音 | ✅ 完全一致 |
| 音频发送 | ✅ 实时发送音频 | ✅ 实时发送音频 | ✅ 完全一致 |
| TTS音频播放 | ✅ 队列播放 | ✅ 队列播放 | ✅ 完全一致 |
| 音频控制 | ✅ 暂停/恢复/清空 | ✅ 暂停/恢复/清空 | ✅ 完全一致 |
| 连接状态 | ✅ 实时状态显示 | ✅ 实时状态显示 | ✅ 完全一致 |
| 日志记录 | ✅ 实时日志 | ✅ 实时日志 | ✅ 完全一致 |

## 🔧 技术架构差异

### 原页面 (websocket-realtime-audio.html)
- **API路径**: `/wsbridge/test`
- **后端服务**: WebSocketBridgeService (wsbridge包)
- **连接方式**: 直接WebSocket连接

### 新页面 (websocket-bridge2-test.html)
- **API路径**: `/wsbridge2/test`
- **后端服务**: BusinessWebSocketBridgeService (wsbridge2包)
- **连接方式**: 通过wsbridge2业务层封装

## 🚀 使用方法

### 1. 访问页面
```
http://localhost:8080/websocket-bridge2-test.html
```

### 2. 认证配置
1. 输入有效的Bearer Token
2. 点击"🔍 测试认证"按钮验证Token有效性

### 3. 连接配置
1. **工单ID** (必填): 唯一标识此次连接的工单ID
2. **智能体ID**: AI智能体标识符
3. **公司ID**: 公司标识符
4. **用户ID**: 用户标识符

### 4. 建立连接
1. 点击"🚀 连接WebSocket"按钮
2. 观察连接状态变化：`未连接` → `连接中...` → `已连接`
3. 连接成功后会自动建立SSE音频流

### 5. 音频录制
1. 连接成功后，"🎤 开始录音"按钮会被启用
2. 点击开始录音，实时发送PCM音频数据
3. 可调整录音音量滑块控制音频增益

### 6. TTS音频播放
1. 系统会自动接收并播放TTS音频回复
2. 支持音频队列连续播放
3. 可使用"🗑️ 清空队列"清空待播放音频

## 📡 API接口

### 主要接口
```
POST /wsbridge2/test/connect          # 建立WebSocket连接
POST /wsbridge2/test/disconnect/{id}  # 断开WebSocket连接
POST /wsbridge2/test/send-audio/{id}  # 发送音频数据
GET  /wsbridge2/test/status/{id}      # 获取连接状态
GET  /wsbridge2/test/audio-stream/{id} # SSE音频流
```

### 接口特点
- **统一认证**: 所有接口使用Bearer Token认证
- **RESTful设计**: 符合REST规范的API设计
- **实时推送**: 通过SSE实现TTS音频实时推送
- **错误处理**: 完善的错误码和错误信息

## 🎵 音频处理

### 录音格式
- **采样率**: 16000Hz
- **声道数**: 1 (单声道)
- **位深度**: 16位
- **格式**: PCM
- **编码**: Base64

### TTS音频支持
- **PCM格式**: 自动转换为WAV播放
- **μ-law格式**: 解码后转换为WAV播放
- **其他格式**: 直接播放

## 🔍 调试功能

### 实时日志
- 详细记录所有操作和事件
- 支持日志导出功能
- 不同级别的日志颜色区分

### 测试功能
- **🧪 测试音频播放**: 生成440Hz测试音频
- **📡 测试SSE连接**: 验证SSE连接状态
- **🔍 测试认证**: 验证Bearer Token有效性

## ⚠️ 注意事项

### 1. 浏览器兼容性
- 需要支持WebRTC的现代浏览器
- 需要麦克风访问权限
- 建议使用Chrome、Firefox、Safari等

### 2. 网络要求
- 稳定的网络连接
- WebSocket服务器可访问
- 防火墙允许WebSocket连接

### 3. 权限要求
- 麦克风访问权限
- 可选：浏览器通知权限

## 🛠️ 故障排除

### 连接失败
1. 检查Bearer Token是否有效
2. 确认WebSocket服务器运行状态
3. 检查网络连接和防火墙设置

### 录音失败
1. 确认浏览器麦克风权限
2. 检查音频设备是否正常
3. 尝试刷新页面重新授权

### 音频播放问题
1. 检查浏览器音频播放权限
2. 确认音频设备输出正常
3. 查看控制台错误信息

## 📝 开发说明

### 文件位置
```
crm_service/crm_service_ai_agent/src/main/resources/static/websocket-bridge2-test.html
```

### 依赖服务
- BusinessWebSocketBridgeService
- WebSocketBridge2TestController
- wsbridge2功能模块

### 配置要求
- 正确的application.yml配置
- wsbridge2相关Bean注册
- 认证服务正常运行

---

**创建时间**: 2025-08-05  
**状态**: ✅ 已完成  
**测试状态**: 🧪 待验证

现在您可以使用新的WebSocket Bridge 2.0测试页面，享受与原页面完全一致的功能体验！
