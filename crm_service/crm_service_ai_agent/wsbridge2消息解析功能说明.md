# WebSocket Bridge 2.0 消息解析功能说明

## 📋 功能概述

在 `BusinessWebSocketBridgeService` 中新增了消息解析功能，能够解析和处理两种类型的WebSocket消息：
1. **Media消息** - 音频媒体数据
2. **DTMF消息** - 双音多频信号数据

## 🎯 新增方法

### 1. 主要解析方法

#### `parseAndProcessMessage(String connectionId, String messageData)`
- **功能**: 解析传入的JSON消息数据，根据事件类型调用相应的处理方法
- **参数**: 
  - `connectionId`: 连接ID
  - `messageData`: JSON格式的消息数据
- **返回**: `CompletableFuture<Boolean>` - 处理结果

### 2. 消息处理方法

#### `handleMediaMessage(String connectionId, JSONObject jsonMessage)`
- **功能**: 处理Media类型消息，解析音频数据并调用 `sendAudioAsync`
- **处理流程**: 解析media字段 → 提取payload → 调用sendAudioAsync发送音频

#### `handleDtmfMessage(String connectionId, JSONObject jsonMessage)`
- **功能**: 处理DTMF类型消息，解析按键数据并调用 `sendDTMFAsync`
- **处理流程**: 解析dtmf字段 → 提取digit → 调用sendDTMFAsync发送DTMF

## 📨 支持的消息格式

### 1. Media消息格式
```json
{
  "event": "media",
  "sequenceNumber": "3",
  "media": {
    "track": "outbound",
    "chunk": "1",
    "timestamp": "5",
    "payload": "no+JhoaJjpz..."
  },
  "streamSid": "MZXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
}
```

**字段说明**:
- `event`: 消息类型，值为 "media"
- `sequenceNumber`: 消息序号，用于跟踪发送顺序
- `media.track`: 音频轨道，"inbound" 或 "outbound"
- `media.chunk`: 消息块编号，从1开始递增
- `media.timestamp`: 时间戳（毫秒）
- `media.payload`: Base64编码的音频数据
- `streamSid`: 流的唯一标识符

### 2. DTMF消息格式
```json
{
  "event": "dtmf",
  "streamSid": "MZXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "sequenceNumber": "5",
  "dtmf": {
    "track": "inbound_track",
    "digit": "1"
  }
}
```

**字段说明**:
- `event`: 消息类型，值为 "dtmf"
- `streamSid`: 流的唯一标识符
- `sequenceNumber`: 消息序号
- `dtmf.track`: 轨道，值总是 "inbound_track"
- `dtmf.digit`: 检测到的数字按键

## 🔧 使用方法

### 1. 直接调用服务方法
```java
@Autowired
private BusinessWebSocketBridgeService businessWebSocketBridgeService;

// 解析Media消息
String mediaMessage = "{\"event\":\"media\",\"sequenceNumber\":\"1\",\"media\":{\"track\":\"outbound\",\"chunk\":\"1\",\"timestamp\":\"5\",\"payload\":\"dGVzdCBhdWRpbyBkYXRh\"},\"streamSid\":\"MZ123456\"}";
CompletableFuture<Boolean> result = businessWebSocketBridgeService.parseAndProcessMessage("connectionId", mediaMessage);

// 解析DTMF消息
String dtmfMessage = "{\"event\":\"dtmf\",\"streamSid\":\"MZ123456\",\"sequenceNumber\":\"2\",\"dtmf\":{\"track\":\"inbound_track\",\"digit\":\"1\"}}";
CompletableFuture<Boolean> result2 = businessWebSocketBridgeService.parseAndProcessMessage("connectionId", dtmfMessage);
```

### 2. 通过HTTP接口测试
```bash
# 测试Media消息
curl -X POST http://localhost:8080/wsbridge2/test/parse-message/your_connection_id \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "messageData": "{\"event\":\"media\",\"sequenceNumber\":\"1\",\"media\":{\"track\":\"outbound\",\"chunk\":\"1\",\"timestamp\":\"5\",\"payload\":\"dGVzdCBhdWRpbyBkYXRh\"},\"streamSid\":\"MZ123456\"}"
  }'

# 测试DTMF消息
curl -X POST http://localhost:8080/wsbridge2/test/parse-message/your_connection_id \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "messageData": "{\"event\":\"dtmf\",\"streamSid\":\"MZ123456\",\"sequenceNumber\":\"2\",\"dtmf\":{\"track\":\"inbound_track\",\"digit\":\"1\"}}"
  }'
```

## 📊 处理流程

### Media消息处理流程
1. **接收消息** → `parseAndProcessMessage`
2. **解析JSON** → 提取 `event` 字段
3. **识别类型** → `event = "media"`
4. **调用处理器** → `handleMediaMessage`
5. **解析字段** → 提取 `media.payload`
6. **发送音频** → `sendAudioAsync(connectionId, payload, "pcm", "16000")`

### DTMF消息处理流程
1. **接收消息** → `parseAndProcessMessage`
2. **解析JSON** → 提取 `event` 字段
3. **识别类型** → `event = "dtmf"`
4. **调用处理器** → `handleDtmfMessage`
5. **解析字段** → 提取 `dtmf.digit`
6. **发送DTMF** → `sendDTMFAsync(connectionId, digit)`

## 🔍 日志输出

### 成功处理日志
```
📨 收到media消息, 连接ID: test_connection
🎵 处理Media消息 - 连接ID: test_connection, 流ID: MZ123456, 序号: 1, 轨道: outbound, 块: 1, 时间戳: 5, 载荷长度: 16
📤 音频数据发送成功, 连接ID: test_connection

📨 收到dtmf消息, 连接ID: test_connection
📞 处理DTMF消息 - 连接ID: test_connection, 流ID: MZ123456, 序号: 2, 轨道: inbound_track, 数字: 1
📤 DTMF消息发送成功, 连接ID: test_connection
```

### 错误处理日志
```
⚠️ 消息事件类型为空, 连接ID: test_connection, 数据: {...}
⚠️ 未知的消息事件类型: unknown, 连接ID: test_connection
⚠️ Media消息缺少media字段, 连接ID: test_connection
💥 解析消息数据异常, 连接ID: test_connection, 数据: {...}
```

## 🧪 测试建议

### 1. 单元测试
- 测试正确格式的Media和DTMF消息解析
- 测试缺少必要字段的错误处理
- 测试无效JSON格式的错误处理
- 测试未知事件类型的处理

### 2. 集成测试
- 测试完整的消息处理流程
- 验证音频数据和DTMF数据的正确发送
- 测试连接状态检查逻辑

### 3. 性能测试
- 测试大量消息的并发处理
- 验证异步处理的性能表现

## ⚠️ 注意事项

1. **连接状态检查**: 确保连接处于CONNECTED状态才能处理消息
2. **JSON格式验证**: 输入数据必须是有效的JSON格式
3. **字段完整性**: Media消息必须包含media字段，DTMF消息必须包含dtmf字段
4. **异常处理**: 所有异常都会被捕获并记录，返回false表示处理失败
5. **音频格式**: 当前默认使用PCM格式，采样率16000Hz，可根据需要调整

## 🔄 扩展性

该解析功能设计具有良好的扩展性：
- 可以轻松添加新的消息类型处理
- 支持自定义音频格式和采样率
- 可以扩展字段验证逻辑
- 支持添加消息预处理和后处理逻辑

---

**创建时间**: 2025-08-05  
**功能状态**: ✅ 已完成  
**测试状态**: 🧪 待验证

现在您可以使用新的消息解析功能来处理Media和DTMF消息了！
