package com.goclouds.crm.platform.aiagent.wsbridge;

import com.goclouds.crm.platform.aiagent.wsbridge.service.WebSocketBridgeService;
import com.goclouds.crm.platform.aiagent.wsbridge.service.WebSocketBridgeService.WebSocketConnectionRequest;
import com.goclouds.crm.platform.aiagent.wsbridge.service.WebSocketBridgeService.WebSocketEventCallbacks;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket桥接服务单元测试类
 *
 * 功能说明：
 * 1. 基于Spring Boot的单元测试，能够真正访问AiAgent服务
 * 2. 使用真实的服务依赖注入，测试完整的业务流程
 * 3. 完整的init事件处理和数据组装流程
 * 4. 演示与AiAgentService和CrmAiAgentCallSettingService的真实集成
 * 5. 📝 自动记录所有WebSocket事件数据到文件（logs/websocket/目录下）
 *
 * 测试场景：
 * 1. 真实的业务参数和服务调用
 * 2. 完整的init事件处理流程
 * 3. 自动获取智能体信息和通话设置
 * 4. 完整的WebSocket消息交互测试
 *
 * 运行方式：
 * 1. 确保AiAgent服务已启动
 * 2. 在IDE中运行此测试类
 * 3. 或使用Maven命令：mvn test -Dtest=WebSocketBridgeMainTest
 *
 * <AUTHOR> Agent
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test") // 使用测试环境配置
public class WebSocketBridgeMainTest {

    @Autowired
    private WebSocketBridgeService webSocketBridgeService;

    private String currentClientId;
    private CountDownLatch connectionLatch;
    private CountDownLatch shutdownLatch;

    @BeforeEach
    void setUp() {
        log.info("� 设置测试环境");
        connectionLatch = new CountDownLatch(1);
        shutdownLatch = new CountDownLatch(1);
        currentClientId = null;
    }

    @AfterEach
    void tearDown() {
        log.info("🧹 清理测试资源");
        if (currentClientId != null && webSocketBridgeService != null) {
            try {
                webSocketBridgeService.closeConnection(currentClientId);
                log.info("✅ WebSocket连接已断开");
            } catch (Exception e) {
                log.warn("⚠️ 断开连接时发生异常: {}", e.getMessage());
            }
        }
    }

    /**
     * 测试WebSocket桥接服务的完整功能
     *
     * 此测试方法会：
     * 1. 创建真实的WebSocket连接
     * 2. 测试init事件处理和数据组装
     * 3. 验证与AiAgent服务的集成
     * 4. 测试音频数据发送功能
     */
    @Test
    void testWebSocketBridgeService() throws Exception {
        log.info("🚀 开始WebSocket桥接服务单元测试");

        try {
            // 1. 创建真实业务场景的WebSocket连接
            createRealBusinessWebSocketConnection();

            // 2. 等待连接建立完成
            boolean connected = connectionLatch.await(10, TimeUnit.SECONDS);
            Assertions.assertTrue(connected, "WebSocket连接应该在10秒内建立成功");

            log.info("✅ WebSocket连接建立成功，开始功能测试");

            // 3. 验证连接状态
            Assertions.assertNotNull(currentClientId, "客户端ID不应该为null");
            Assertions.assertNotNull(webSocketBridgeService, "WebSocket桥接服务不应该为null");

            // 4. 测试心跳功能
            testHeartbeatFunctionality();

            // 5. 测试音频数据发送
//            testAudioDataSending();

            // 6. 测试连接状态
            testConnectionStatus();

            log.info("🎉 所有测试完成");

        } catch (Exception e) {
            log.error("💥 测试执行异常", e);
            throw e;
        }
    }

    /**
     * 创建真实业务场景的WebSocket连接
     */
    private void createRealBusinessWebSocketConnection() {
        log.info("🔌 开始创建真实业务场景的WebSocket连接...");

        try {
            // 使用真实的测试数据 - 这些ID应该在测试数据库中存在
            String workOrderId = "WO_20250731_001";
            String aiAgentId = "ac19d983e9e747d6bb70010175493fe7";  // 使用测试环境中真实存在的智能体ID
            String companyId = "d6ff59a027861d7d4133e1d6b6872464"; // 使用测试环境中真实存在的公司ID

            // 构建WebSocket URL（使用新API文档中的地址）
            String webSocketUrl = "ws://192.168.110.21:8765/voice-duplex/?client_id=" + workOrderId;
            log.info("🔗 WebSocket连接地址: {}", webSocketUrl);

            // 1. 准备业务参数 - 使用真实的业务数据
            Map<String, Object> businessParams = new HashMap<>();
            businessParams.put("workOrderId", workOrderId);
            businessParams.put("aiAgent", aiAgentId);      // 智能体ID - 用于获取智能体信息
            businessParams.put("companyId", companyId);    // 公司ID - 用于获取配置信息
            businessParams.put("channelId", "7");
            businessParams.put("language", "zh-CN");
            businessParams.put("s3JsonPath", "s3://dddddddtestjsonpath");
            businessParams.put("s3WavPath", "s3://dddddddtestwavpath");
            // 添加音频格式配置（根据新API文档要求）
            businessParams.put("audioFormat", "mulaw");     // 音频格式：mulaw 或 pcm
            businessParams.put("audioSampleRate", 8000);    // 音频采样率：8000 或 16000

            log.info("📋 业务参数: {}", businessParams);

            // 2. 设置事件回调
            WebSocketEventCallbacks callbacks = createEventCallbacks();

            // 3. 创建连接请求
            WebSocketConnectionRequest request = new WebSocketConnectionRequest();
            request.setWebSocketUrl(webSocketUrl);
            request.setBusinessParams(businessParams);
            request.setCallbacks(callbacks);

            // 4. 异步创建连接
            CompletableFuture<String> connectionFuture = webSocketBridgeService.createWebSocketConnection(request);

            // 5. 等待连接结果（非阻塞，由connectionLatch控制）
            log.info("⏳ 等待WebSocket连接建立...");
            connectionFuture.whenComplete((clientId, throwable) -> {
                if (throwable != null) {
                    log.error("❌ WebSocket连接创建失败", throwable);
                    connectionLatch.countDown(); // 释放等待
                } else {
                    currentClientId = clientId;
                    log.info("🎉 WebSocket连接创建成功！客户端ID: {}", currentClientId);
                    // 注意：不在这里countDown，等待onConnected回调
                }
            });

        } catch (Exception e) {
            log.error("❌ WebSocket连接创建失败", e);
            connectionLatch.countDown(); // 释放等待
            throw new RuntimeException("连接创建失败", e);
        }
    }

    /**
     * 创建事件回调处理器
     */
    private WebSocketEventCallbacks createEventCallbacks() {
        WebSocketEventCallbacks callbacks = new WebSocketEventCallbacks();

        // 连接建立回调
        callbacks.setOnConnected(clientId -> {
            log.info("✅ [回调] WebSocket连接建立成功，客户端ID: {}", clientId);
            log.info("🎯 连接完全可用，可以开始发送音频数据");
            connectionLatch.countDown(); // 释放测试等待
        });

        // 语音识别结果回调
        callbacks.setOnSTTResult(message -> {
            String resultType = message.getIsFinal() ? "最终结果" : "中间结果";
            log.info("📝 [回调] 语音识别{}: {}", resultType, message.getText());
        });

        // AI回复回调
        callbacks.setOnApiResponse(message -> {
            String responseType = message.getIsFinal() ? "最终回复" : "中间回复";
            log.info("🤖 [回调] AI{}: {}", responseType, message.getText());
        });

        // TTS音频回调
        callbacks.setOnTTSAudio(message -> {
            String audioType = message.getIsFinal() ? "最终音频" : "中间音频";
            log.info("🔊 [回调] TTS{}，序号: {}, 音频数据长度: {}",
                audioType, message.getSequence(),
                (message.getAudioData() != null ? message.getAudioData().length() : 0));
        });

        // TTS打断回调
        callbacks.setOnTTSInterrupt(message -> {
            log.info("⏹️ [回调] TTS播放打断，原因: {}", message.getReason());
        });

        // 静默超时回调
        callbacks.setOnSilenceTimeout(message -> {
            log.info("🔇 [回调] 用户静默超时，时长: {}秒", message.getTimeoutDuration());
        });

        // 错误消息回调
        callbacks.setOnErrorMessage(message -> {
            log.error("❌ [回调] 收到错误消息: {}", message.getMessage());
        });

        // 连接断开回调
        callbacks.setOnDisconnected(clientId -> {
            log.warn("❌ [回调] WebSocket连接断开，客户端ID: {}", clientId);
            shutdownLatch.countDown(); // 通知测试可以退出
        });

        // 系统错误回调
        callbacks.setOnError(exception -> {
            log.error("💥 [回调] 系统错误", exception);
            connectionLatch.countDown(); // 释放连接等待
            shutdownLatch.countDown(); // 通知测试可以退出
        });

        return callbacks;
    }

    /**
     * 测试音频数据发送功能
     */
    private void testAudioDataSending() {
        Assertions.assertNotNull(currentClientId, "测试音频发送前应该有活跃的WebSocket连接");

        try {
            log.info("🎤 开始测试音频数据发送...");

            // 生成模拟音频数据
            byte[] mockAudioData = generateMockAudioData();
            Assertions.assertNotNull(mockAudioData, "模拟音频数据不应该为null");
            Assertions.assertTrue(mockAudioData.length > 0, "模拟音频数据长度应该大于0");

            // 发送音频数据
            boolean success = webSocketBridgeService.sendAudioData(currentClientId, mockAudioData);

            if (success) {
                log.info("✅ 音频数据发送成功，数据长度: {} bytes", mockAudioData.length);
            } else {
                log.error("❌ 音频数据发送失败");
            }

            // 验证发送结果
            Assertions.assertTrue(success, "音频数据发送应该成功");

            // 等待一段时间，观察服务器响应
            Thread.sleep(2000);

        } catch (Exception e) {
            log.error("💥 发送音频数据异常", e);
            Assertions.fail("音频数据发送不应该抛出异常: " + e.getMessage());
        }
    }

    /**
     * 测试连接状态
     */
    private void testConnectionStatus() {
        log.info("📊 测试连接状态信息:");
        log.info("  客户端ID: {}", currentClientId != null ? currentClientId : "无");
        log.info("  连接状态: {}", currentClientId != null ? "已连接" : "未连接");

        // 可以添加更多状态检查
        if (currentClientId != null) {
            // 这里可以添加更多的连接状态验证逻辑
            log.info("✅ 连接状态正常");
        }
    }

    /**
     * 生成模拟音频数据
     */
    private byte[] generateMockAudioData() {
        // 生成1KB的模拟PCM音频数据
        byte[] audioData = new byte[1024];
        for (int i = 0; i < audioData.length; i++) {
            // 生成简单的正弦波音频数据
            double frequency = 440.0; // A音符频率
            double sampleRate = 16000.0; // 16kHz采样率
            double amplitude = 32767.0; // 16bit音频幅度

            double sample = amplitude * Math.sin(2.0 * Math.PI * frequency * i / sampleRate);
            short shortSample = (short) sample;

            // 转换为字节（小端序）
            audioData[i] = (byte) (shortSample & 0xFF);
            if (i + 1 < audioData.length) {
                audioData[i + 1] = (byte) ((shortSample >> 8) & 0xFF);
                i++; // 跳过下一个字节
            }
        }
        return audioData;
    }

    /**
     * 测试心跳功能
     *
     * 功能说明：
     * 1. 验证心跳任务是否正常启动
     * 2. 等待一段时间观察心跳消息发送
     * 3. 检查心跳机制是否保持连接活跃
     */
    private void testHeartbeatFunctionality() {
        try {
            log.info("💓 开始测试心跳功能");

            // 等待心跳消息发送（心跳间隔通常是10秒）
            log.info("⏰ 等待心跳消息发送（等待15秒观察心跳）...");
            Thread.sleep(15000);

            // 验证连接仍然活跃
            Assertions.assertNotNull(currentClientId, "连接应该仍然活跃");

            log.info("✅ 心跳功能测试完成 - 连接保持活跃");

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("💥 心跳测试被中断", e);
            Assertions.fail("心跳测试被中断: " + e.getMessage());
        } catch (Exception e) {
            log.error("💥 心跳测试异常", e);
            Assertions.fail("心跳测试失败: " + e.getMessage());
        }
    }
}

