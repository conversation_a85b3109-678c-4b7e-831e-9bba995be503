package com.goclouds.crm.platform.aiagent.wsbridge2.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业务连接请求参数
 * 包含调用WebSocket桥接服务所需的业务字段
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessConnectionRequest {

    /**
     * 工单ID - 必填
     * 用于标识具体的业务工单
     */
    private String workOrderId;

    /**
     * 智能体ID - 必填
     * 用于标识使用的AI智能体
     */
    private String aiAgentId;

    /**
     * 公司ID - 必填
     * 用于标识所属公司
     */
    private String companyId;

    /**
     * 渠道ID - 必填
     * 用于标识接入渠道
     */
    private String channelId;

    /**
     * 渠道类型ID - 必填
     * 用于标识渠道类型
     */
    private String channelTypeId;

    /**
     * S3 JSON路径 - 必填
     */
    private String s3JsonPath;

    /**
     * S3 WAV路径 - 必填
     */
    private String s3WavPath;

    /**
     * 用户ID - 可选
     * 用于标识具体用户
     */
    private String userId;

    /**
     * 业务类型 - 可选
     * 用于区分不同的业务场景
     */
    private String businessType;

    /**
     * 扩展字段1 - 可选
     * 预留扩展字段，用于后续业务需求
     */
    private String extField1;

    /**
     * 扩展字段2 - 可选
     * 预留扩展字段，用于后续业务需求
     */
    private String extField2;

    /**
     * 扩展字段3 - 可选
     * 预留扩展字段，用于后续业务需求
     */
    private String extField3;

    /**
     * 备注信息 - 可选
     * 用于记录额外的业务信息
     */
    private String remark;

    /**
     * 音频格式
     * 客户端期望的音频格式，可选值: "pcm", "mulaw"
     */
    private String audioFormat;

    /**
     * 音频采样率
     * 客户端期望的音频采样率，如16000, 8000
     */
    private Integer audioSampleRate;

    /**
     * 验证必填字段
     *
     * @return 验证结果
     */
    public boolean isValid() {
        return workOrderId != null && !workOrderId.trim().isEmpty()
                && aiAgentId != null && !aiAgentId.trim().isEmpty()
                && companyId != null && !companyId.trim().isEmpty()
                && channelId != null && !channelId.trim().isEmpty()
                && channelTypeId != null && !channelTypeId.trim().isEmpty()
                && s3JsonPath != null && !s3JsonPath.trim().isEmpty()
                && s3WavPath != null && !s3WavPath.trim().isEmpty();
    }

    /**
     * 获取连接标识符
     * 使用workOrderId作为连接的唯一标识
     *
     * @return 连接标识符
     */
    public String getConnectionId() {
        return workOrderId;
    }

    /**
     * 转换为日志字符串
     *
     * @return 日志字符串
     */
    public String toLogString() {
        return "BusinessConnectionRequest{" +
                "workOrderId='" + workOrderId + '\'' +
                ", aiAgentId='" + aiAgentId + '\'' +
                ", companyId='" + companyId + '\'' +
                ", channelId='" + channelId + '\'' +
                ", channelTypeId='" + channelTypeId + '\'' +
                ", s3JsonPath='" + s3JsonPath + '\'' +
                ", s3WavPath='" + s3WavPath + '\'' +
                ", userId='" + userId + '\'' +
                ", businessType='" + businessType + '\'' +
                ", extField1='" + extField1 + '\'' +
                ", extField2='" + extField2 + '\'' +
                ", extField3='" + extField3 + '\'' +
                ", remark='" + remark + '\'' +
                ", audioFormat='" + audioFormat + '\'' +
                ", audioSampleRate=" + audioSampleRate +
                '}';
    }
}
