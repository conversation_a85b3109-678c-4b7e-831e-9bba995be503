package com.goclouds.crm.platform.aiagent.wsbridge2.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TTS打断消息
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TTSInterruptMessage extends BaseMessage {

    /**
     * 被打断的消息ID
     */
    @JSONField(name = "interrupted_msg_id")
    private String interruptedMsgId;

    /**
     * 新的消息ID
     */
    @JSONField(name = "new_msg_id")
    private String newMsgId;

    /**
     * 打断原因
     */
    @JSONField(name = "reason")
    private String reason;

    public TTSInterruptMessage() {
        super("tts_interrupt", null);
    }
}
