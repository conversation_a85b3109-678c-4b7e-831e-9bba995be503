package com.goclouds.crm.platform.aiagent.wsbridge2.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TTS音频消息
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TTSMessage extends BaseMessage {

    /**
     * 消息唯一标识符
     */
    @JSONField(name = "msg_id")
    private String msgId;

    /**
     * 会话唯一标识符
     */
    @JSONField(name = "session_id")
    private String sessionId;

    /**
     * 音频格式
     * 可能值: "pcm", "mulaw"
     */
    @JSONField(name = "format")
    private String format;

    /**
     * 音频采样率
     * 如8000或16000
     */
    @JSONField(name = "sample_rate")
    private Integer sampleRate;

    /**
     * Base64编码的音频数据
     */
    @JSONField(name = "audio")
    private String audio;

    /**
     * 音频时长（秒）
     */
    @JSONField(name = "duration")
    private Double duration;

    /**
     * 是否为最终识别结果
     */
//    @JSONField(name = "is_final")
//    private Boolean isFinal;

    /**
     * 是否为静默提醒
     * 静默提醒时为true
     */
    @JSONField(name = "is_silence_reminder")
    private Boolean isSilenceReminder;

    public TTSMessage() {
        super("tts_audio", null);
    }
}
