package com.goclouds.crm.platform.aiagent.wsbridge2.example;

import com.goclouds.crm.platform.aiagent.wsbridge2.dto.BusinessConnectionRequest;
import com.goclouds.crm.platform.aiagent.wsbridge2.callback.BusinessWebSocketCallback;
import com.goclouds.crm.platform.aiagent.wsbridge2.service.BusinessWebSocketBridgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * 业务WebSocket使用示例
 * 演示如何使用BusinessWebSocketBridgeService
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Component
public class BusinessWebSocketExample {
    
    @Autowired
    private BusinessWebSocketBridgeService businessWebSocketBridgeService;
    
    /**
     * 示例：建立WebSocket连接并处理事件
     */
    public void exampleUsage() {
        // 1. 构建业务连接请求
        BusinessConnectionRequest request = BusinessConnectionRequest.builder()
                .workOrderId("order_20240101_001")
                .aiAgentId("ac19d983e9e747d6bb70010175493fe7")
                .companyId("d6ff59a027861d7d4133e1d6b6872464")
                .channelId("0aae4cf7c891219d474fbdbc506ea3c2")
                .channelTypeId("17")
                .audioFormat("pcm")
                .audioSampleRate(16000)
                .businessType("WebSocket Bridge 2.0 测试连接")
                .remark("WebSocket Bridge 2.0 测试连接")
                .s3JsonPath("s3://voice-wangjie-test/voice/test/0.json")
                .s3WavPath("s3://voice-wangjie-test/voice/test/0.wav")
                .build();
        
        // 2. 实现业务回调
        BusinessWebSocketCallback callback = new BusinessWebSocketCallback() {
            @Override
            public void onConnected(BusinessConnectionRequest req, String connectionId) {
                log.info("✅ [示例] WebSocket连接建立成功！");
                log.info("   连接ID: {}", connectionId);
                log.info("   工单ID: {}", req.getWorkOrderId());
                log.info("   智能体ID: {}", req.getAiAgentId());
                log.info("   📋 说明: 此时WebSocket连接已完全建立，init消息已处理完成");

                // 连接成功后可以开始发送音频数据
                sendExampleAudioData(connectionId);
            }
            
            @Override
            public void onDisconnected(BusinessConnectionRequest req, String connectionId, String reason) {
                log.info("🔌 [示例] WebSocket连接已断开");
                log.info("   连接ID: {}", connectionId);
                log.info("   断开原因: {}", reason);
                
                // 连接断开后的清理工作
                cleanupResources(connectionId);
            }
            
            @Override
            public void onError(BusinessConnectionRequest req, String connectionId, String error) {
                log.error("💥 [示例] WebSocket连接发生错误");
                log.error("   连接ID: {}", connectionId);
                log.error("   错误信息: {}", error);
                
                // 错误处理逻辑
                handleConnectionError(connectionId, error);
            }
            
            @Override
            public void onTTSAudio(BusinessConnectionRequest req, String connectionId, 
                                  String audioData, String format, Integer sampleRate, Double duration) {
                log.info("🔊 [示例] 收到TTS音频数据");
                log.info("   连接ID: {}", connectionId);
                log.info("   音频格式: {}", format);
                log.info("   采样率: {}Hz", sampleRate);
                log.info("   时长: {}秒", duration);
                log.info("   数据长度: {} bytes", audioData.length());
                
                // 处理TTS音频数据，如推送给前端播放
                processTTSAudio(connectionId, audioData, format, sampleRate, duration);
            }
            
            @Override
            public void onTTSInterrupt(BusinessConnectionRequest req, String connectionId, String reason) {
                log.info("⏹️ [示例] TTS播放被打断");
                log.info("   连接ID: {}", connectionId);
                log.info("   打断原因: {}", reason);
                
                // 处理TTS打断逻辑
                handleTTSInterrupt(connectionId, reason);
            }
            
            @Override
            public void onSilenceTimeout(BusinessConnectionRequest req, String connectionId, 
                                        String message) {
                log.warn("🔇 [示例] 用户静默超时");
                log.warn("   连接ID: {}", connectionId);
                log.warn("   超时消息: {}", message);
                
                // 处理静默超时逻辑
                handleSilenceTimeout(connectionId, message);
            }
            
            @Override
            public void onASRResult(BusinessConnectionRequest req, String connectionId, 
                                   String text, Boolean isFinal) {
                log.info("🎤 [示例] 收到ASR识别结果");
                log.info("   连接ID: {}", connectionId);
                log.info("   识别文本: {}", text);
                log.info("   是否最终结果: {}", isFinal);
                
                // 处理ASR识别结果
                processASRResult(connectionId, text, isFinal);
            }
            
            @Override
            public void onConversationStateChanged(BusinessConnectionRequest req, String connectionId, 
                                                  String oldState, String newState) {
                log.info("🔄 [示例] 对话状态变更");
                log.info("   连接ID: {}", connectionId);
                log.info("   旧状态: {}", oldState);
                log.info("   新状态: {}", newState);
                
                // 处理对话状态变更
                handleConversationStateChange(connectionId, oldState, newState);
            }
        };
        
        // 3. 异步建立连接
        log.info("🚀 [示例] 开始建立WebSocket连接...");
        CompletableFuture<Boolean> connectFuture = businessWebSocketBridgeService.connectAsync(request, callback);
        
        connectFuture.thenAccept(success -> {
            if (success) {
                log.info("✅ [示例] WebSocket连接建立成功！");
                
                // 连接成功后可以进行其他操作
                // 如发送音频数据、查询连接状态等
                
            } else {
                log.error("❌ [示例] WebSocket连接建立失败！");
            }
        }).exceptionally(throwable -> {
            log.error("💥 [示例] WebSocket连接建立异常: {}", throwable.getMessage(), throwable);
            return null;
        });
    }
    
    /**
     * 示例：发送音频数据
     */
    private void sendExampleAudioData(String connectionId) {
        // 这里应该是实际的音频数据（Base64编码）
        String audioData = "UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA="; // 示例数据
        
        CompletableFuture<Boolean> sendFuture = businessWebSocketBridgeService.sendAudioAsync(connectionId, audioData);
        
        sendFuture.thenAccept(success -> {
            if (success) {
                log.info("📤 [示例] 音频数据发送成功");
            } else {
                log.warn("⚠️ [示例] 音频数据发送失败");
            }
        });
    }
    
    /**
     * 示例：断开连接
     */
    public void exampleDisconnect(String connectionId) {
        log.info("🔌 [示例] 开始断开WebSocket连接...");
        
        CompletableFuture<Boolean> disconnectFuture = businessWebSocketBridgeService.disconnectAsync(connectionId, "主动断开");
        
        disconnectFuture.thenAccept(success -> {
            if (success) {
                log.info("✅ [示例] WebSocket连接断开成功");
            } else {
                log.warn("⚠️ [示例] WebSocket连接断开失败");
            }
        });
    }
    
    // ========== 业务处理方法示例 ==========
    
    private void processTTSAudio(String connectionId, String audioData, String format, Integer sampleRate, Double duration) {
        // TODO: 实现TTS音频处理逻辑
        // 例如：推送给前端播放、保存到文件、转换格式等
        log.debug("处理TTS音频数据，连接ID: {}", connectionId);
    }
    
    private void handleTTSInterrupt(String connectionId, String reason) {
        // TODO: 实现TTS打断处理逻辑
        // 例如：停止当前播放、清空播放队列等
        log.debug("处理TTS打断，连接ID: {}, 原因: {}", connectionId, reason);
    }
    
    private void handleSilenceTimeout(String connectionId, String message) {
        // TODO: 实现静默超时处理逻辑
        // 例如：发送提醒消息、记录超时日志等
        log.debug("处理静默超时，连接ID: {}, 超时信息: {}", connectionId, message);
    }
    
    private void processASRResult(String connectionId, String text, Boolean isFinal) {
        // TODO: 实现ASR结果处理逻辑
        // 例如：更新UI显示、保存识别结果、触发业务逻辑等
        log.debug("处理ASR结果，连接ID: {}, 文本: {}, 最终: {}", connectionId, text, isFinal);
    }
    
    private void handleConversationStateChange(String connectionId, String oldState, String newState) {
        // TODO: 实现对话状态变更处理逻辑
        // 例如：更新UI状态、记录状态变更日志等
        log.debug("处理对话状态变更，连接ID: {}, {} -> {}", connectionId, oldState, newState);
    }
    
    private void handleConnectionError(String connectionId, String error) {
        // TODO: 实现连接错误处理逻辑
        // 例如：重试连接、发送错误通知、记录错误日志等
        log.debug("处理连接错误，连接ID: {}, 错误: {}", connectionId, error);
    }
    
    private void cleanupResources(String connectionId) {
        // TODO: 实现资源清理逻辑
        // 例如：清理缓存、释放资源、更新状态等
        log.debug("清理资源，连接ID: {}", connectionId);
    }
}
