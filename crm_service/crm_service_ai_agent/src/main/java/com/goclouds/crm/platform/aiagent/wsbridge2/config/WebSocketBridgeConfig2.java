package com.goclouds.crm.platform.aiagent.wsbridge2.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * WebSocket桥接配置类
 * 管理WebSocket连接的各种配置参数
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Data
@Component
//@ConfigurationProperties(prefix = "websocket.bridge")
public class WebSocketBridgeConfig2 {
    
    /**
     * WebSocket服务器URL
     * 默认值，可通过配置文件覆盖
     */
    private String serverUrl = "ws://10.200.3.163:30765/voice-duplex/";

    /**
     * 连接超时时间（毫秒）
     */
    private Long connectTimeout = 30000L;
    
    /**
     * 心跳间隔（毫秒）
     */
    private Long heartbeatInterval = 30000L;
    
    /**
     * 心跳超时时间（毫秒）
     */
    private Long heartbeatTimeout = 10000L;
    
    /**
     * 重连间隔（毫秒）
     */
    private Long reconnectInterval = 5000L;
    
    /**
     * 最大重连次数
     */
    private Integer maxReconnectAttempts = 3;
    
    /**
     * 是否启用自动重连
     */
    private Boolean autoReconnect = true;
    
    /**
     * 消息发送超时时间（毫秒）
     */
    private Long sendTimeout = 5000L;
    
    /**
     * 接收缓冲区大小
     */
    private Integer receiveBufferSize = 8192;
    
    /**
     * 发送缓冲区大小
     */
    private Integer sendBufferSize = 8192;
    
    /**
     * 是否启用压缩
     */
    private Boolean compressionEnabled = false;
    
    /**
     * 静默超时时间（秒）
     * 用户静默超过此时间将自动断开连接
     */
    private Integer silenceTimeout = 60;
    
    /**
     * 音频采样率 pcm：16000/mulaw：8000
     */
    private Integer audioSampleRate = 8000;
    
    /**
     * 音频格式，pcm / mulaw
     */
    private String audioFormat = "mulaw";
    
    /**
     * 是否启用音频压缩
     */
    private Boolean audioCompressionEnabled = false;
    
    /**
     * 日志级别
     * DEBUG, INFO, WARN, ERROR
     */
    private String logLevel = "DEBUG";
    
    /**
     * 是否启用详细日志
     */
    private Boolean verboseLogging = false;
    
    /**
     * 连接池大小
     */
    private Integer connectionPoolSize = 100;
    
    /**
     * 线程池核心大小
     */
    private Integer threadPoolCoreSize = 10;
    
    /**
     * 线程池最大大小
     */
    private Integer threadPoolMaxSize = 50;
    
    /**
     * 线程池队列大小
     */
    private Integer threadPoolQueueSize = 1000;
    
    /**
     * 验证配置是否有效
     * 
     * @return 验证结果
     */
    public boolean isValid() {
        return serverUrl != null && !serverUrl.trim().isEmpty()
                && connectTimeout > 0
                && heartbeatInterval > 0
                && maxReconnectAttempts >= 0;
    }
    
    /**
     * 获取完整的WebSocket URL
     * 
     * @return WebSocket URL
     */
    public String getFullWebSocketUrl() {
        return serverUrl;
    }
    
    /**
     * 转换为日志字符串
     * 
     * @return 日志字符串
     */
    public String toLogString() {
        return String.format("WebSocketBridgeConfig{serverUrl='%s', connectTimeout=%d, heartbeatInterval=%d, maxReconnectAttempts=%d}",
                serverUrl, connectTimeout, heartbeatInterval, maxReconnectAttempts);
    }
}
