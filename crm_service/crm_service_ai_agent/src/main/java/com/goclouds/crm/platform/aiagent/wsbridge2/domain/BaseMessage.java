package com.goclouds.crm.platform.aiagent.wsbridge2.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * WebSocket消息基类
 *
 * 功能说明：
 * 1. 定义所有WebSocket消息的公共字段和结构
 * 2. 使用Jackson注解支持JSON序列化/反序列化
 * 3. 所有具体的消息类型都继承自此基类
 * 4. 确保消息格式符合WebSocket API规范
 *
 * 支持的消息类型：
 * - init: 初始化消息
 * - stt_audio: 语音识别音频
 * - stt_result: 语音识别结果
 * - api_response: AI回复
 * - tts_audio: TTS音频
 * - tts_interrupt: TTS打断
 * - silence_timeout: 静默超时
 * - error: 错误消息
 *
 * <AUTHOR>
 */
@Data
public class BaseMessage {

    /**
     * 消息类型
     *
     * 标识消息的具体类型，用于消息路由和处理
     * 如：init、stt_result、api_response、tts_audio等
     */
    @JSONField(name = "type")
    private String type;

    /**
     * 客户端ID
     *
     * 唯一标识WebSocket客户端的ID
     * 用于区分不同的连接和会话
     */
    @JSONField(name = "client_id")
    private String clientId;

    /**
     * 状态
     *
     * 消息的状态信息，如：ready、processing、completed等
     * 用于表示消息处理的当前状态
     */
    @JSONField(name = "status")
    private String status;

    /**
     * 消息内容
     *
     * 通用的消息内容字段
     * 具体含义根据消息类型而定
     */
    @JSONField(name = "message")
    private String message;

    /**
     * 默认构造函数
     */
    public BaseMessage() {}

    /**
     * 带参数的构造函数
     *
     * @param type 消息类型
     * @param clientId 客户端ID
     */
    public BaseMessage(String type, String clientId) {
        this.type = type;
        this.clientId = clientId;
    }
}
