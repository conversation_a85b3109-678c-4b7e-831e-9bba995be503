package com.goclouds.crm.platform.aiagent.service;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.aiagent.domain.AiAgentExecRequest;
import com.goclouds.crm.platform.aiagent.domain.CrmAiAgentInfo;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentChannelCountVo;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentInfoVo;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentShareCodeVo;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.util.List;

/**
* <AUTHOR>
* @description AiAgent
*/


public interface AiAgentService {

    void run(AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer consumer);

//    AjaxResult<AiAgentRes> runTest(AiAgentExecRequest aiAgentExecRequest);

    AjaxResult<String> saveAgentInfo(AiAgentInfoVo agent);

    AjaxResult<AiAgentInfoVo> getAgentById(String agentId);

    AjaxResult<IPage<AiAgentInfoVo>> queryAgentInfoPages(IPage<Object> pageParam, AiAgentInfoVo agentInfoVo);

    AjaxResult<List<AiAgentChannelCountVo>> agentCount(AiAgentInfoVo agentInfoVo);

    AjaxResult<Boolean> deleteAgentById(String agentId);

    AjaxResult<Boolean> updateStatus(String agentId);

    AjaxResult<AiAgentInfoVo> copyAiagent(String aiAgentId);

    AjaxResult<AiAgentInfoVo> createDefaultAndEnsure(String companyId);

    void updateChannel(String aiAgentId, String s);

    void updateName(String aiAgentId, String name);

    void updateIntentId(String aiAgentId, String intentId);
    
    void runTestStream(AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer consumer);

    AjaxResult<Integer> aiAgentCount(String companyId);

    AjaxResult<AiAgentShareCodeVo> shareCodeInfo(String aiAgentId);

    AjaxResult<JSONObject> getShareContent(String shareCode);

    /**
     * 获取智能体信息，根据绑定的系统手机号查询。
     *
     * @param companyId   公司ID。
     * @param systemPhone 系统手机号。
     * @return 智能体信息。
     */
    AjaxResult<AiAgentInfoVo> getAgentInfoBySystemPhone(String companyId, String systemPhone);

    /**
     * 获取智能体信息
     *
     * @param aiAgentId
     * @return
     */
    CrmAiAgentInfo getAiAgentApiInfo(String aiAgentId);


}
