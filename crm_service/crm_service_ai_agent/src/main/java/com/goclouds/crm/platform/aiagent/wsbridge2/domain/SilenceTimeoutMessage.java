package com.goclouds.crm.platform.aiagent.wsbridge2.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 静默超时消息
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SilenceTimeoutMessage extends BaseMessage {

    /**
     * 描述性消息
     */
    @JSONField(name = "message")
    private String message;

    /**
     * 当前连续静默次数
     */
    @JSONField(name = "silence_count")
    private Integer silenceCount;

    /**
     * 最大允许的静默次数
     */
    @JSONField(name = "max_silence_count")
    private Integer maxSilenceCount;

    /**
     * 时间戳
     */
    @JSONField(name = "timestamp")
    private Double timestamp;


    /**
     * 执行的动作类型
     * 如 "disconnect_recommended"
     */
    @JSONField(name = "action")
    private String action;

    public SilenceTimeoutMessage() {
        super("silence_timeout", null);
    }
}
