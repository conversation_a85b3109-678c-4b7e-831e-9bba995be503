package com.goclouds.crm.platform.aiagent.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 智能体主表
 * @TableName crm_ai_agent_info
 */
@TableName(value ="crm_ai_agent_info")
@Data
public class CrmAiAgentInfo implements Serializable {
    /**
     * id
     */
    @TableId
    private String aiAgentId;

    /**
     * 智能体名称
     */
    private String aiAgentName;

    /**
     * 智能体类型，1:正常，2:欢迎语，3:fallback
     */
    private Integer aiAgentType;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 1:聊天，2:邮件，3:电话
     */
    private Integer channelTypeId;

    /**
     * 来源渠道，和咱们定义的那个序号1-20+对应的id,多个逗号分隔
     */
    private String channelIds;

    /**
     * 1:意图 2: 其他智能体 3:没有
     */
    private Integer triggerType;

    /**
     * 意图id
     */
    private String intentionId;

    /**
     * 数据分析 1:开启 0：关闭
     */
    private Integer dataAnalysis;

    /**
     * 生效状态 0:未生效 1:生效
     */
    private Integer aiAgentStatus;

    /**
     * 是否允许打断， 0:否 1:是
     */
    private Integer allowInterrupt;

    /**
     * 数据状态0-删除，1-正常
     */
    private Integer dataStatus;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 语音编码
     */
    private String voiceCode;
    /**
     * 语音语速
     */
    private String voiceSpeed;
    /**
     * 语音音量
     */
    private String voiceVolume;

    /**
     * 系统电话，多个使用逗号分隔
     */
    private String systemPhone;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}