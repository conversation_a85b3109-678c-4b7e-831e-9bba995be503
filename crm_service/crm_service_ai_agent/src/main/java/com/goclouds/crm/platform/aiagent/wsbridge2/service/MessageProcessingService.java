package com.goclouds.crm.platform.aiagent.wsbridge2.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.goclouds.crm.platform.aiagent.wsbridge2.dto.BusinessConnectionRequest;
import com.goclouds.crm.platform.aiagent.wsbridge2.callback.BusinessWebSocketCallback;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 状态更新回调接口
 */
interface StatusUpdateCallback {
    void updateStatus(String connectionId, String status);
}

/**
 * 消息处理服务 - 用于处理OnMessage各事件的业务逻辑
 * 负责解析和处理WebSocket消息，将消息转换为业务回调
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
public class MessageProcessingService {
    
    @Autowired
    private WebSocketConnectionService webSocketConnectionService;
    
    @Autowired
    private BusinessDataService businessDataService;

    /**
     * 处理WebSocket消息
     *
     * @param clientId 客户端ID
     * @param message 原始消息
     * @param request 连接请求
     * @param callback 业务回调
     */
    public void processMessage(String clientId, String message, BusinessConnectionRequest request, BusinessWebSocketCallback callback) {
        processMessage(clientId, message, request, callback, null);
    }

    /**
     * 处理WebSocket消息（带状态更新回调）
     *
     * @param clientId 客户端ID
     * @param message 原始消息
     * @param request 连接请求
     * @param callback 业务回调
     * @param statusUpdateCallback 状态更新回调
     */
    public void processMessage(String clientId, String message, BusinessConnectionRequest request, BusinessWebSocketCallback callback, StatusUpdateCallback statusUpdateCallback) {
        try {
            // 解析消息类型
            BaseMessage baseMessage = parseMessage(clientId, message);

            if (baseMessage == null) {
                log.warn("⚠️ 无法解析的消息格式，忽略处理, 客户端ID: {}", clientId);
                return;
            }
            
            // request参数已直接传入，无需再次获取
            
            // 根据消息类型进行处理
            String messageType = baseMessage.getType();
            switch (messageType) {
                case "init":
                    if (baseMessage.getStatus().equals("success")) {
                        // 触发连接建立回调
                        callback.onConnected(request, baseMessage.getClientId());
                    } else {
                        handleInitMessage((InitMessage) baseMessage, request, callback, statusUpdateCallback);
                    }
                    break;
                case "tts_audio":
                    handleTTSAudioMessage((TTSMessage) baseMessage, request, callback);
                    break;
                case "tts_interrupt":
                    handleTTSInterruptMessage((TTSInterruptMessage) baseMessage, request, callback);
                    break;
                case "silence_timeout":
                    handleSilenceTimeoutMessage((SilenceTimeoutMessage) baseMessage, request, callback);
                    break;
                case "error":
                    handleErrorMessage((ErrorMessage) baseMessage, request, callback);
                    break;
                default:
                    // 忽略未处理的消息类型
                    break;
            }
            
        } catch (Exception e) {
            log.error("💥 处理WebSocket消息异常, 客户端ID: {}", clientId, e);
            if (callback != null) {
                if (request != null) {
                    callback.onError(request, clientId, "消息处理异常: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 解析WebSocket消息
     * 
     * @param clientId 客户端id
     * @param message 原始消息
     * @return 解析后的消息对象
     */
    private BaseMessage parseMessage(String clientId, String message) {
        try {
            JSONObject jsonObject = JSON.parseObject(message);
            String type = jsonObject.getString("type");
            log.info("📨1⃣️️ 开始处理WebSocket消息, 客户端ID: {}, 类型：{}, 消息: {}", clientId, type, message);

            if (type == null) {
                return null;
            }
            
            switch (type) {
                case "init":
                    return JSON.parseObject(message, InitMessage.class);
                case "tts_audio":
                    return JSON.parseObject(message, TTSMessage.class);
                case "tts_interrupt":
                    return JSON.parseObject(message, TTSInterruptMessage.class);
                case "silence_timeout":
                    return JSON.parseObject(message, SilenceTimeoutMessage.class);
                case "error":
                    return JSON.parseObject(message, ErrorMessage.class);
                default:
                    // 对于未知类型，创建基础消息对象
                    BaseMessage baseMessage = new BaseMessage();
                    baseMessage.setType(type);
                    return baseMessage;
            }
        } catch (Exception e) {
            log.error("💥 解析消息异常: {}", message, e);
            return null;
        }
    }

    /**
     * 处理init消息（带状态更新回调）
     *
     * @param message init消息
     * @param request 连接请求
     * @param callback 业务回调
     * @param statusUpdateCallback 状态更新回调
     */
    private void handleInitMessage(InitMessage message, BusinessConnectionRequest request, BusinessWebSocketCallback callback, StatusUpdateCallback statusUpdateCallback) {
        try {
            // 构建init响应消息
            InitMessage responseMessage = businessDataService.buildInitResponseMessage(message.getClientId(), request, callback);

            // 校验init数据是否完整
            if (!responseMessage.isValid()) {
                // 响应失败数据
                callback.onError(request, message.getClientId(), "构建初始化数据不完整，数据为：" + responseMessage);
                return;
            }

            log.info("发送init响应客户端ID：{}, 数据：{}", message.getClientId() ,JSON.toJSONString(responseMessage));

            // 发送init响应
            boolean sent = webSocketConnectionService.sendMessage(message.getClientId(), responseMessage);
            
            if (sent) {
                log.info("✅ init响应发送成功， 客户端ID: {}", message.getClientId());

                // 更新连接状态为CONNECTED
                if (statusUpdateCallback != null) {
                    statusUpdateCallback.updateStatus(message.getClientId(), "CONNECTED");
                    log.info("🔗 连接状态已更新为CONNECTED, 客户端ID: {}", message.getClientId());
                }

                // init处理完成，触发连接建立回调
                callback.onConnected(request, message.getClientId());
            } else {
                log.error("❌ init响应发送失败, 客户端ID: {}", message.getClientId());
                callback.onError(request, message.getClientId(), "init响应发送失败");
            }
            
        } catch (Exception e) {
            log.error("💥 处理init消息异常, 客户端ID: {}", message.getClientId(), e);
            callback.onError(request, message.getClientId(), "init处理异常: " + e.getMessage());
        }
    }
    
    /**
     * 处理TTS音频消息
     * 
     * @param message TTS音频消息
     * @param request 连接请求
     * @param callback 业务回调
     */
    private void handleTTSAudioMessage(TTSMessage message, BusinessConnectionRequest request, BusinessWebSocketCallback callback) {
        log.info("🔊 处理TTS音频消息, 客户端ID: {}, 格式: {}, 采样率: {}, 时长: {}秒", 
                message.getClientId(), message.getFormat(), message.getSampleRate(), message.getDuration());
        
        // 触发TTS音频回调
        callback.onTTSAudio(request, message.getClientId(), message.getAudio(), 
                message.getFormat(), message.getSampleRate(), message.getDuration());
    }
    
    /**
     * 处理TTS打断消息
     * 
     * @param message TTS打断消息
     * @param request 连接请求
     * @param callback 业务回调
     */
    private void handleTTSInterruptMessage(TTSInterruptMessage message, BusinessConnectionRequest request, BusinessWebSocketCallback callback) {
        log.info("⏹️ 处理TTS打断消息, 客户端ID: {}, 原因: {}", message.getClientId(), message.getReason());
        
        // 触发TTS打断回调
        callback.onTTSInterrupt(request, message.getClientId(), message.getReason());
    }
    
    /**
     * 处理静默超时消息
     * 
     * @param message 静默超时消息
     * @param request 连接请求
     * @param callback 业务回调
     */
    private void handleSilenceTimeoutMessage(SilenceTimeoutMessage message, BusinessConnectionRequest request, BusinessWebSocketCallback callback) {
        log.info("🔇 处理静默超时消息, 客户端ID: {}, 消息: {}", message.getClientId(), message.getMessage());
        
        // 触发静默超时回调
        callback.onSilenceTimeout(request, message.getClientId(), message.getMessage());
    }
    
    /**
     * 处理错误消息
     * 
     * @param message 错误消息
     * @param request 连接请求
     * @param callback 业务回调
     */
    private void handleErrorMessage(ErrorMessage message, BusinessConnectionRequest request, BusinessWebSocketCallback callback) {
        log.error("💥 处理错误消息, 客户端ID: {}, 错误类型: {}, 错误信息: {}", 
                message.getClientId(), message.getErrorType(), message.getMessage());
        
        // 触发错误回调
        callback.onError(request, message.getClientId(), 
                String.format("服务器错误[%s]: %s", message.getErrorType(), message.getMessage()));
    }
    

}
