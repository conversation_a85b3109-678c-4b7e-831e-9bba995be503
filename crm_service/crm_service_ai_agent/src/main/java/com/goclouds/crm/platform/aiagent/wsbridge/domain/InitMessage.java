package com.goclouds.crm.platform.aiagent.wsbridge.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentCallSettingVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 初始化消息
 *
 * <AUTHOR> Agent
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InitMessage extends BaseMessage {

    /**
     * 工单ID
     */
    @JSONField(name = "work_order_id")
    private String workOrderId;
    /**
     * 公司ID
     */
    @JSONField(name = "company_id")
    private String companyId;
    /**
     * 渠道ID
     */
    @JSONField(name = "channelId")
    private String channelId;
    /**
     * 渠道类型ID
     */
    @JSONField(name = "channelTypeId")
    private String channelTypeId;

    /**
     * AI代理ID
     */
    @JSONField(name = "aiAgentId")
    private String aiAgentId;

    /**
     * 设置映射
     */
    @JSONField(name = "settingMap")
    private Map<String, String> settingMap;

    /**
     * 话术列表
     */
    @JSONField(name = "phraseList")
    private List<AiAgentCallSettingVo.PhraseItem> phraseList;

    /**
     * TTS语言
     */
    @JSONField(name = "tts_language")
    private String ttsLanguage;

    /**
     * TTS声音名称
     */
    @JSONField(name = "tts_voice_name")
    private String ttsVoiceName;

    /**
     * 语言
     */
    @JSONField(name = "language")
    private String language;

    /**
     * S3 JSON路径
     */
    @JSONField(name = "s3_json_path")
    private String s3JsonPath;

    /**
     * S3 WAV路径
     */
    @JSONField(name = "s3_wav_path")
    private String s3WavPath;

    /**
     * 音频格式
     * 客户端期望的音频格式，可选值: "pcm", "mulaw"
     */
    @JSONField(name = "audio_format")
    private String audioFormat;

    /**
     * 音频采样率
     * 客户端期望的音频采样率，如8000, 16000
     */
    @JSONField(name = "audio_sample_rate")
    private Integer audioSampleRate;

    /**
     * 音量
     */
    @JSONField(name = "volume")
    private String volume;

    /**
     * 语速
     */
    @JSONField(name = "speed")
    private String speed;

    public InitMessage() {
        super("init", null);
    }

    public InitMessage(String clientId) {
        super("init", clientId);
    }

    /**
     * 校验 InitMessage 中的关键字段是否都有值
     * 如果任意一个字段为 null 或空字符串，则返回 false
     * 所有字段都有值时返回 true
     */
    public boolean isValid() {
        // 检查字符串类型的字段是否为 null 或空字符串
//        if (isNullOrEmpty(workOrderId)) return false;
//        if (isNullOrEmpty(companyId)) return false;
//        if (isNullOrEmpty(channelId)) return false;
//        if (isNullOrEmpty(channelTypeId)) return false;
//        if (isNullOrEmpty(aiAgentId)) return false;
//        if (isNullOrEmpty(ttsLanguage)) return false;
//        if (isNullOrEmpty(ttsVoiceName)) return false;
//        if (isNullOrEmpty(language)) return false;
//        if (isNullOrEmpty(s3JsonPath)) return false;
//        if (isNullOrEmpty(s3WavPath)) return false;
//        if (isNullOrEmpty(audioFormat)) return false;
//
//        // 检查 Map 和 List 是否为空
//        if (settingMap == null || settingMap.isEmpty()) return false;
//        if (phraseList == null || phraseList.isEmpty()) return false;
//
//        // 检查采样率是否为 null 或 <= 0
//        if (audioSampleRate == null || audioSampleRate <= 0) return false;

        // 所有检查通过
        return true;
    }

    /**
     * 工具方法：判断字符串是否为 null 或空
     */
    private boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
}
