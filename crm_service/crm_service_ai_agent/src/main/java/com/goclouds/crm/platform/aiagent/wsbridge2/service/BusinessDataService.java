package com.goclouds.crm.platform.aiagent.wsbridge2.service;

import com.goclouds.crm.platform.aiagent.domain.CrmAiAgentInfo;
import com.goclouds.crm.platform.aiagent.domain.SysTtsVoiceDictionary;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentCallSettingVo;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentInfoVo;
import com.goclouds.crm.platform.aiagent.service.AiAgentService;
import com.goclouds.crm.platform.aiagent.service.CrmAiAgentCallSettingService;
import com.goclouds.crm.platform.aiagent.service.SysTtsVoiceDictionaryService;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.InitMessage;
import com.goclouds.crm.platform.aiagent.wsbridge2.callback.BusinessWebSocketCallback;
import com.goclouds.crm.platform.aiagent.wsbridge2.config.WebSocketBridgeConfig2;
import com.goclouds.crm.platform.aiagent.wsbridge2.dto.BusinessConnectionRequest;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 业务数据服务 - 用于获取初始化系统信息的service，如：智能体信息、通话配置等。
 * 负责准备WebSocket连接所需的初始化数据
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
public class BusinessDataService {

    @Autowired
    private WebSocketBridgeConfig2 config;

    @Autowired
    private AiAgentService aiAgentService; // 智能体服务，用于获取智能体信息

    @Autowired
    private CrmAiAgentCallSettingService crmAiAgentCallSettingService; // 通话设置服务，用于获取通话配置

    @Autowired
    private SysTtsVoiceDictionaryService sysTtsVoiceDictionaryService; // 语音设置服务，用于获取语音信息

    /**
     * 构建init响应消息
     *
     * @param clientId 客户端ID
     * @param request  连接请求
     * @return init响应消息，如果构建失败则返回null
     */
    public InitMessage buildInitResponseMessage(String clientId, BusinessConnectionRequest request, BusinessWebSocketCallback callback) {
        try {
            log.info("🔧 构建init响应消息, 客户端ID: {}", clientId);

            // 基本参数校验
            if (request.getAiAgentId() == null || aiAgentService == null) {
                String errorMsg = "缺少必要参数: aiAgentId 或 aiAgentService 未初始化";
                log.warn("⚠️ [消息构建] {}", errorMsg);
                callback.onError(request, clientId, errorMsg);
                return null;
            }

            // 1. 获取电话智能体信息
            CrmAiAgentInfo aiAgentInfo = aiAgentService.getAiAgentApiInfo(request.getAiAgentId());
            if (aiAgentInfo == null) {
                String errorMsg = "获取智能体信息失败: 返回结果为空";
                log.warn("⚠️ [消息构建] {}", errorMsg);
                callback.onError(request, clientId, errorMsg);
                return null;
            }

            // 2. 初始化消息对象并设置基本属性
            InitMessage message = initBaseMessage(clientId, request, aiAgentInfo);

            // 3. 设置语音信息
            setVoiceProperties(message, aiAgentInfo);

            // 4. 设置通话配置信息
            setCallSettings(message, request, callback);

            return message;
        } catch (Exception e) {
            log.error("❌ [消息构建] 构建init响应消息时发生异常", e);
            callback.onError(request, clientId, "构建init响应消息时发生异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 初始化消息基本属性
     */
    private InitMessage initBaseMessage(String clientId, BusinessConnectionRequest request, CrmAiAgentInfo aiAgentInfo) {
        InitMessage message = new InitMessage();
        message.setType("init");
        message.setClientId(clientId);
        message.setStatus("ready");

        // 设置基本配置
        message.setWorkOrderId(request.getWorkOrderId());
        message.setCompanyId(request.getCompanyId());
        message.setAiAgentId(request.getAiAgentId());
        message.setChannelId(request.getChannelId());
        message.setChannelTypeId(request.getChannelTypeId());

        // 存储音频地址
        message.setS3JsonPath(request.getS3JsonPath());
        message.setS3WavPath(request.getS3WavPath());

        // 音频设置
        message.setAudioFormat(request.getAudioFormat());
        message.setAudioSampleRate(request.getAudioSampleRate());
        message.setVolume(aiAgentInfo.getVoiceVolume());
        message.setSpeed(aiAgentInfo.getVoiceSpeed());

        return message;
    }

    /**
     * 设置语音属性
     */
    private void setVoiceProperties(InitMessage message, CrmAiAgentInfo aiAgentInfo) {
        if (sysTtsVoiceDictionaryService != null) {
            SysTtsVoiceDictionary voiceDictionary = sysTtsVoiceDictionaryService.voiceByVoiceCode(aiAgentInfo.getVoiceCode());
            if (voiceDictionary != null) {
                message.setLanguage(voiceDictionary.getLocale());
                message.setTtsLanguage(voiceDictionary.getLocale());
                message.setTtsVoiceName(voiceDictionary.getVoiceCode());
            } else {
                log.warn("⚠️ [消息构建] 未找到语音代码 {} 对应的语音字典", aiAgentInfo.getVoiceCode());
            }
        } else {
            log.warn("⚠️ [消息构建] sysTtsVoiceDictionaryService 未初始化，跳过语音属性设置");
        }
    }

    /**
     * 设置通话配置
     */
    private void setCallSettings(InitMessage message, BusinessConnectionRequest request, BusinessWebSocketCallback callback) {
        if (crmAiAgentCallSettingService != null) {
            try {
                AiAgentCallSettingVo callSetting = crmAiAgentCallSettingService.getCallSettingDetail(request.getAiAgentId());
                if (callSetting != null) {
                    message.setSettingMap(callSetting.getSettingMap());
                    message.setPhraseList(callSetting.getPhraseList());
                } else {
                    log.warn("⚠️ [消息构建] 获取通话设置信息为空");
                }
            } catch (Exception e) {
                log.error("❌ [消息构建] 获取通话设置时发生异常", e);
                callback.onError(request, message.getClientId(), "获取通话设置时发生异常");
            }
        }
    }

}
