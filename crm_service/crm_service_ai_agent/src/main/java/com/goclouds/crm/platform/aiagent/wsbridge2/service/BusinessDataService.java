package com.goclouds.crm.platform.aiagent.wsbridge2.service;

import com.goclouds.crm.platform.aiagent.domain.CrmAiAgentInfo;
import com.goclouds.crm.platform.aiagent.domain.SysTtsVoiceDictionary;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentCallSettingVo;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentInfoVo;
import com.goclouds.crm.platform.aiagent.service.AiAgentService;
import com.goclouds.crm.platform.aiagent.service.CrmAiAgentCallSettingService;
import com.goclouds.crm.platform.aiagent.service.SysTtsVoiceDictionaryService;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.InitMessage;
import com.goclouds.crm.platform.aiagent.wsbridge2.callback.BusinessWebSocketCallback;
import com.goclouds.crm.platform.aiagent.wsbridge2.config.WebSocketBridgeConfig2;
import com.goclouds.crm.platform.aiagent.wsbridge2.dto.BusinessConnectionRequest;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 业务数据服务 - 作获取各个数据
 * 负责准备WebSocket连接所需的初始化数据
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
public class BusinessDataService {

    @Autowired
    private WebSocketBridgeConfig2 config;

    @Autowired
    private AiAgentService aiAgentService; // 智能体服务，用于获取智能体信息

    @Autowired
    private CrmAiAgentCallSettingService crmAiAgentCallSettingService; // 通话设置服务，用于获取通话配置

    @Autowired
    private SysTtsVoiceDictionaryService sysTtsVoiceDictionaryService; // 语音设置服务，用于获取语音信息


    /**
     * 构建init响应消息
     *
     * @param clientId 客户端ID
     * @param request  连接请求
     * @return init响应消息
     */
    public InitMessage buildInitResponseMessage(String clientId, BusinessConnectionRequest request, BusinessWebSocketCallback callback) {
        log.info("🔧 构建init响应消息, 客户端ID: {}", clientId);

        InitMessage message = new InitMessage();
        message.setType("init");
        message.setClientId(clientId);
        message.setStatus("ready");

        // 设置基本配置
        message.setWorkOrderId(request.getWorkOrderId());
        message.setCompanyId(request.getCompanyId());
        message.setAiAgentId(request.getAiAgentId());
        message.setChannelId(request.getChannelId());
        message.setChannelTypeId(request.getChannelTypeId());
        message.setLanguage(request.getSttLanguage()); // 语音转文字的语言

        // 音频设置
        message.setAudioFormat(request.getAudioFormat()); // 定义发送的音频的格式
        message.setAudioSampleRate(request.getAudioSampleRate()); // 定义发送的音频的码率

        // 存储音频地址
        message.setS3JsonPath(config.getS3JsonPath());
        message.setS3WavPath(config.getS3WavPath());


        if (request.getAiAgentId() == null && aiAgentService == null) {
            callback.onError(request, message.getClientId(), "调用AiAgentService异常");
            return null;
        }

        // 1. 获取电话智能体信息
        CrmAiAgentInfo aiAgentInfo = aiAgentService.getAiAgentApiInfo(request.getAiAgentId());
        if (aiAgentInfo == null) {
            log.warn("⚠️ [消息构建] 获取智能体信息失败: 返回结果为空");
            callback.onError(request, message.getClientId(), "获取智能体信息失败: 返回结果为空");
            return null;
        }

        message.setVolume(aiAgentInfo.getVoiceVolume());
        message.setSpeed(aiAgentInfo.getVoiceSeeped());

        // 2. 获取语音信息并设置
        SysTtsVoiceDictionary sysTtsVoiceDictionary = sysTtsVoiceDictionaryService.voiceByVoiceCode(aiAgentInfo.getVoiceCode());
        if (null != sysTtsVoiceDictionary) {
            message.setTtsLanguage(sysTtsVoiceDictionary.getLocale()); // 文字转语音的语言
            message.setTtsVoiceName(sysTtsVoiceDictionary.getVoiceCode()); // 音频的语言
        }

        // 3. 获取智能体通话配置信息
        AiAgentCallSettingVo callSetting = null;
        if (request.getAiAgentId() != null && crmAiAgentCallSettingService != null) {
            try {
                callSetting = crmAiAgentCallSettingService.getCallSettingDetail(request.getAiAgentId());
                if (callSetting == null) log.warn("⚠️ [消息构建] 获取通话设置信息为空");
            } catch (Exception e) {
                log.error("❌ [消息构建] 调用CrmAiAgentCallSettingService异常", e);
                callback.onError(request, message.getClientId(), "调用CrmAiAgentCallSettingService异常");
            }
        } else if (crmAiAgentCallSettingService == null) {
            log.warn("⚠️ [消息构建] CrmAiAgentCallSettingService未注入，跳过通话设置获取");
        }
        // 通话配置信息
        if (callSetting != null) {
            message.setSettingMap(callSetting.getSettingMap());
            message.setPhraseList(callSetting.getPhraseList());
        }
        return message;
    }

}
