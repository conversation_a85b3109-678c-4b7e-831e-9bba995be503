package com.goclouds.crm.platform.aiagent.wsbridge.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.goclouds.crm.platform.aiagent.wsbridge.config.WebSocketBridgeConfig;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.*;
import com.goclouds.crm.platform.aiagent.wsbridge.handler.WebSocketEventHandler;
import com.goclouds.crm.platform.aiagent.service.AiAgentService;
import com.goclouds.crm.platform.aiagent.service.CrmAiAgentCallSettingService;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentInfoVo;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentCallSettingVo;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * WebSocket桥接服务 - 新版本
 * 根据用户需求重新设计的WebSocket桥接服务
 *
 * 核心功能：
 * 1. 支持动态WebSocket地址连接
 * 2. 灵活的业务参数传递
 * 3. 智能的init事件处理（收到服务器init后自动响应）
 * 4. 完整的中间层事件处理和回调机制
 * 5. 线程安全的连接管理
 *
 * <AUTHOR> Agent
 */
@Slf4j
@Service
public class WebSocketBridgeService {

    @Autowired
    private WebSocketBridgeConfig config; // WebSocket配置信息

    @Autowired
    private AiAgentService aiAgentService; // 智能体服务，用于获取智能体信息

    @Autowired
    private CrmAiAgentCallSettingService crmAiAgentCallSettingService; // 通话设置服务，用于获取通话配置

    // 连接管理 - 使用线程安全的ConcurrentHashMap
    private final Map<String, WebSocketClient> clients = new ConcurrentHashMap<>(); // 存储活跃的WebSocket客户端
    private final Map<String, WebSocketConnectionRequest> requests = new ConcurrentHashMap<>(); // 存储连接请求信息

    // 心跳管理 - 维护WebSocket连接的心跳机制
    private final ScheduledExecutorService heartbeatExecutor = Executors.newScheduledThreadPool(5); // 心跳线程池
    private final Map<String, ScheduledFuture<?>> heartbeatTasks = new ConcurrentHashMap<>(); // 存储每个连接的心跳任务

    // 文件日志配置
    private static final String LOG_DIR = "logs/websocket"; // 日志文件目录
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"); // 时间格式化器

    /**
     * 创建WebSocket连接 - 核心入口方法
     *
     * 工作流程：
     * 1. 生成唯一客户端ID
     * 2. 创建中间层事件处理器
     * 3. 建立WebSocket连接
     * 4. 等待服务器发送init事件并自动响应
     * 5. 回调业务层告知连接完全建立
     *
     * @param connectionRequest 连接请求参数（包含WebSocket地址、业务参数、事件回调）
     * @return 连接操作的Future，成功时返回客户端ID
     */
    public CompletableFuture<String> createWebSocketConnection(WebSocketConnectionRequest connectionRequest) {
        log.info("🚀 开始创建WebSocket连接，地址: {}", connectionRequest.getWebSocketUrl());

        // 使用异步方式处理连接创建，避免阻塞调用线程
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 1. 生成唯一的客户端ID，用于标识此次连接
                String clientId = (String) connectionRequest.getBusinessParams().get("workOrderId");

                // 2. 创建中间层事件处理器，负责处理所有WebSocket事件
                WebSocketEventHandler eventHandler = createMiddlewareEventHandler(connectionRequest, clientId);

                // 3. 建立底层WebSocket连接
                boolean connected = connectToWebSocket(connectionRequest.getWebSocketUrl(), clientId, eventHandler);

                if (connected) {
                    // 连接成功，保存请求信息供后续使用
                    requests.put(clientId, connectionRequest);
                    log.info("✅ WebSocket连接建立成功2，客户端ID: {}", clientId);
                    return clientId;
                } else {
                    log.error("❌ WebSocket连接建立失败");
                    throw new RuntimeException("WebSocket连接失败");
                }
            } catch (Exception e) {
                log.error("💥 创建WebSocket连接时发生异常", e);
                throw new RuntimeException("创建WebSocket连接异常", e);
            }
        });
    }

    /**
     * 发送音频数据到WebSocket服务器
     *
     * 功能说明：
     * 1. 将原始音频数据编码为Base64格式
     * 2. 封装成STT消息格式
     * 3. 通过WebSocket发送给服务器进行语音识别
     *
     * @param clientId 客户端ID，标识具体的WebSocket连接
     * @param audioData 原始音频数据（PCM 16kHz 16bit mono格式）
     * @return 发送是否成功
     */
    public boolean sendAudioData(String clientId, byte[] audioData) {
        log.info("🎤 发送音频数据，客户端ID: {}, 数据大小: {} bytes", clientId, audioData.length);

        try {
            // 将原始音频数据编码为Base64格式，符合WebSocket API要求
            String base64Audio = encodeAudioToBase64(audioData);
//            String base64Audio = "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";
            // 创建语音转文字(STT)消息对象
            STTMessage sttMessage = new STTMessage();
            sttMessage.setClientId(clientId); // 设置客户端标识
            sttMessage.setAudioData(base64Audio); // 设置Base64编码的音频数据
            // 根据新的API文档设置音频格式参数
            sttMessage.setEncoding("pcm"); // 音频编码格式：pcm 或 mulaw
            sttMessage.setSampleRate(16000L); // 音频采样率：16000 (PCM) 或 8000 (mulaw)

            // 通过WebSocket发送STT消息到服务器
            return sendMessage(clientId, sttMessage);

        } catch (Exception e) {
            log.error("💥 发送音频数据时发生异常，客户端ID: {}", clientId, e);
            return false;
        }
    }

    /**
     * 关闭WebSocket连接
     *
     * 功能说明：
     * 1. 发送关闭消息通知服务器
     * 2. 清理本地连接资源
     * 3. 移除连接管理信息
     *
     * @param clientId 客户端ID，标识要关闭的连接
     */
    public void closeConnection(String clientId) {
        log.info("🛑 关闭WebSocket连接，客户端ID: {}", clientId);

        try {
            // 创建关闭消息，礼貌地通知服务器连接即将关闭
            CloseMessage closeMessage = new CloseMessage();
            closeMessage.setClientId(clientId);
            closeMessage.setReason("用户主动关闭"); // 设置关闭原因

            // 发送关闭消息给服务器
            sendMessage(clientId, closeMessage);

            // 关闭底层WebSocket连接并清理资源
            closeWebSocketConnection(clientId);

        } catch (Exception e) {
            log.error("💥 关闭WebSocket连接时发生异常，客户端ID: {}", clientId, e);
        }
    }

    /**
     * 创建中间层事件处理器 - 核心中间件逻辑
     *
     * 这里是整个系统的关键部分：
     * 1. 接收所有WebSocket原始事件
     * 2. 在中间层进行预处理和过滤
     * 3. 特殊处理init事件（自动响应服务器）
     * 4. 将处理后的事件回调给业务层
     *
     * @param request 连接请求信息，包含业务参数和回调函数
     * @param clientId 客户端ID
     * @return 中间层事件处理器实例
     */
    private WebSocketEventHandler createMiddlewareEventHandler(WebSocketConnectionRequest request, String clientId) {
        return new WebSocketEventHandler() {

            @Override
            public void onConnected(String clientId) {
//                log.info("🔗 [中间层] WebSocket底层连接已建立，等待服务器发送init事件");

                // 📝 写入连接事件到文件
                writeConnectedEventToFile(clientId);

                // 重要：这里不立即回调业务层的onConnected
                // 必须等到收到服务器init事件并成功响应后，才算真正连接完成
            }

            @Override
            public void onMessage(String message) {
                log.info("📨1⃣️️ [中间层] 收到WebSocket原始消息: {}", message);

                // 📝 写入消息事件到文件
                writeMessageEventToFile(clientId, message);

                try {
                    // 解析JSON消息，确定消息类型和内容
                    BaseMessage baseMessage = parseMessage(message);

                    if (baseMessage != null) {
                        // 根据消息类型进行相应的中间层处理
                        handleParsedMessage(baseMessage, request, clientId);
//                    } else {
//                        log.warn("⚠️ [中间层] 无法解析的消息格式，忽略处理");
                    }

                } catch (Exception e) {
                    log.error("💥 [中间层] 解析消息时发生异常", e);
                    // 将解析异常回调给业务层
                    if (request.getCallbacks().getOnError() != null) {
                        request.getCallbacks().getOnError().accept(e);
                    }
                }
            }

            @Override
            public void onDisconnected(String clientId, int code, String reason) {
                log.warn("❌ [中间层] WebSocket连接断开，客户端ID: {}, 代码: {}, 原因: {}", clientId, code, reason);

                // 📝 写入断开连接事件到文件
                writeDisconnectedEventToFile(clientId, code, reason);

                // 清理本地资源
                clients.remove(clientId);
                requests.remove(clientId);

                // 将断开事件回调给业务层
                if (request.getCallbacks().getOnDisconnected() != null) {
                    request.getCallbacks().getOnDisconnected().accept(clientId);
                }
            }

            @Override
            public void onError(String clientId, Exception ex) {
                log.error("💥 [中间层] WebSocket发生错误，客户端ID: {}", clientId, ex);

                // 📝 写入错误事件到文件
                writeErrorEventToFile(clientId, ex);

                // 将错误事件回调给业务层
                if (request.getCallbacks().getOnError() != null) {
                    request.getCallbacks().getOnError().accept(ex);
                }
            }

            // 以下方法为WebSocketEventHandler接口的其他方法
            // 在这个中间层实现中，我们主要通过onMessage方法统一处理所有消息
            // 这些方法保持空实现，实际处理逻辑在handleParsedMessage中完成

            /*@Override
            public void onInitResponse(InitMessage message) {
                // 空实现 - 实际处理在handleParsedMessage中
            }

            @Override
            public void onHeartbeatResponse(HeartbeatMessage message) {
                // 空实现 - 心跳消息在中间层自动处理
            }

            @Override
            public void onSTTResult(STTResultMessage message) {
                // 空实现 - 实际处理在handleParsedMessage中
            }

            @Override
            public void onApiResponse(ApiResponseMessage message) {
                // 空实现 - 实际处理在handleParsedMessage中
            }

            @Override
            public void onTTSAudio(TTSMessage message) {
                // 空实现 - 实际处理在handleParsedMessage中
            }

            @Override
            public void onTTSInterrupt(TTSInterruptMessage message) {
                // 空实现 - 实际处理在handleParsedMessage中
            }

            @Override
            public void onSilenceTimeout(SilenceTimeoutMessage message) {
                // 空实现 - 实际处理在handleParsedMessage中
            }

            @Override
            public void onErrorMessage(ErrorMessage message) {
                // 空实现 - 实际处理在handleParsedMessage中
            }

            @Override
            public void onCloseMessage(CloseMessage message) {
                // 空实现 - 实际处理在handleParsedMessage中
            }

            @Override
            public void onUnknownMessage(String clientId, String messageType, String rawMessage) {
                // 空实现 - 未知消息类型的处理在handleParsedMessage中完成
            }*/
        };
    }

    /**
     * 处理解析后的消息 - 中间层核心分发逻辑
     *
     * 功能说明：
     * 1. 根据消息类型分发到对应的处理方法
     * 2. 每种消息类型都有专门的处理逻辑
     * 3. 特别处理init消息（自动响应服务器）
     * 4. 其他消息经过中间层处理后回调业务层
     *
     * @param message 解析后的消息对象
     * @param request 连接请求信息
     * @param clientId 客户端ID
     */
    private void handleParsedMessage(BaseMessage message, WebSocketConnectionRequest request, String clientId) {
        String messageType = message.getType();
//        log.info("📋 [中间层] 开始处理消息类型: {}", messageType);

        // 根据消息类型分发到对应的处理方法
        switch (messageType) {
            case "init":
                if (message.getStatus().equals("success")) {
                    // 4. 重要：只有在init流程完成后，才回调业务层的onConnected
                    // 这确保业务层收到onConnected回调时，连接已经完全可用
                    if (request.getCallbacks().getOnConnected() != null) {
//                    log.info("🔔 [中间层] 回调业务层onConnected，连接完全建立");
                        request.getCallbacks().getOnConnected().accept(clientId);
//                } else {
//                    log.warn("⚠️ [中间层] 业务层未设置onConnected回调");
                    }
                } else {
                    // 处理服务器发送的初始化消息（关键流程）
                    handleInitMessage((InitMessage) message, request, clientId);
                }
                break;

//            case "stt_result":
//                // 处理语音识别结果
//                handleSTTResultMessage((STTResultMessage) message, request);
//                break;
//
//            case "api_response":
//                // 处理AI回复消息
//                handleApiResponseMessage((ApiResponseMessage) message, request);
//                break;

            case "tts_audio":
                // 处理语音音频数据
                handleTTSAudioMessage((TTSMessage) message, request);
                break;

            case "tts_interrupt":
                // 处理TTS打断消息
                handleTTSInterruptMessage((TTSInterruptMessage) message, request);
                break;

            case "silence_timeout":
                // 处理静默超时消息
                handleSilenceTimeoutMessage((SilenceTimeoutMessage) message, request);
                break;

            case "error":
                // 处理错误消息
                handleErrorMessage((ErrorMessage) message, request);
                break;

            default:
                // 处理未知消息类型
                log.warn("⚠️ [中间层] 收到未知消息类型: {}，原始消息: {}", messageType, message);
                // 可以选择回调业务层的未知消息处理器
        }
    }

    /**
     * 处理服务器发送的init事件 - 关键的自动响应流程
     *
     * 这是用户描述的核心工作流程：
     * 1. 服务器主动发送init事件
     * 2. 中间层收到后自动获取配置信息
     * 3. 组装业务数据并发送init响应
     * 4. 响应成功后才回调业务层的onConnected
     *
     * 这个流程确保了WebSocket连接的完整初始化
     *
     * @param initMessage 服务器发送的init消息
     * @param request 连接请求信息，包含业务参数
     * @param clientId 客户端ID
     */
    private void handleInitMessage(InitMessage initMessage, WebSocketConnectionRequest request, String clientId) {
//        log.info("🎯 [中间层] 收到服务器init事件，开始自动响应流程");
        log.info("📋 [中间层] 收到服务器init消息内容: {}", initMessage);

        try {
            // 1. 获取配置信息
            // 从业务参数中提取配置，或者调用配置服务获取动态配置
            Map<String, Object> configData = getConfigurationData(request);
            log.info("📋 [中间层] 获取到配置数据: {}", configData);

            // 2. 组装init响应消息
            // 根据业务参数和配置数据构建符合API要求的init响应
            InitMessage responseMessage = buildInitResponseMessage(clientId, request.getBusinessParams(), configData);
            log.info("📋 [中间层] 构建init响应消息: {}", responseMessage);

            // 3. 发送init响应给WebSocket服务器
            boolean sent = sendMessage(clientId, responseMessage);

            if (sent) {
                log.info("✅ [中间层] init响应发送成功，WebSocket初始化流程完成");
            } else {
                log.error("❌ [中间层] init响应发送失败，连接初始化失败");
                throw new RuntimeException("init响应发送失败");
            }

        } catch (Exception e) {
            log.error("💥 [中间层] 处理init事件时发生异常", e);
            // 将异常回调给业务层
            if (request.getCallbacks().getOnError() != null) {
                request.getCallbacks().getOnError().accept(e);
            }
        }
    }

    /**
     * 处理语音识别结果消息
     *
     * 功能说明：
     * 1. 接收服务器返回的语音识别结果
     * 2. 在中间层进行文本处理（过滤、格式化等）
     * 3. 将处理后的结果回调给业务层
     *
     * @param message 语音识别结果消息
     * @param request 连接请求信息
     */
    private void handleSTTResultMessage(STTResultMessage message, WebSocketConnectionRequest request) {
        log.info("🎤 [中间层] 处理语音识别结果: {}, 是否最终结果: {}", message.getText(), message.getIsFinal());

        // 中间层文本处理：去除多余空格、敏感词过滤等
        String processedText = processSTTResult(message.getText());
        message.setText(processedText);

        // 将处理后的语音识别结果回调给业务层
        if (request.getCallbacks().getOnSTTResult() != null) {
            request.getCallbacks().getOnSTTResult().accept(message);
        } else {
            log.warn("⚠️ [中间层] 业务层未设置STT结果回调");
        }
    }

    /**
     * 处理AI回复消息
     *
     * 功能说明：
     * 1. 接收服务器返回的AI对话回复
     * 2. 在中间层进行内容审核和格式化
     * 3. 将处理后的回复回调给业务层
     *
     * @param message AI回复消息
     * @param request 连接请求信息
     */
    private void handleApiResponseMessage(ApiResponseMessage message, WebSocketConnectionRequest request) {
        log.info("🤖 [中间层] 处理AI回复: {}, 是否最终回复: {}", message.getText(), message.getIsFinal());

        // 中间层内容处理：敏感词过滤、长度限制、格式化等
        String processedText = processAIResponse(message.getText());
        message.setText(processedText);

        // 将处理后的AI回复回调给业务层
        if (request.getCallbacks().getOnApiResponse() != null) {
            request.getCallbacks().getOnApiResponse().accept(message);
        } else {
            log.warn("⚠️ [中间层] 业务层未设置AI回复回调");
        }
    }

    /**
     * 处理TTS音频消息
     *
     * 功能说明：
     * 1. 接收服务器返回的文字转语音音频数据
     * 2. 在中间层进行音频处理（格式转换、音量调节等）
     * 3. 将处理后的音频数据回调给业务层
     *
     * @param message TTS音频消息
     * @param request 连接请求信息
     */
    private void handleTTSAudioMessage(TTSMessage message, WebSocketConnectionRequest request) {
//        log.info("🔊 [中间层] 处理TTS音频, 是否是静默音频: {}", message.getIsSilenceReminder());

        // 中间层音频处理：格式转换、音量调节、质量优化等
        String processedAudio = processTTSAudio(message.getAudio());
        message.setAudio(processedAudio);

        // 将处理后的TTS音频回调给业务层
        if (request.getCallbacks().getOnTTSAudio() != null) {
            request.getCallbacks().getOnTTSAudio().accept(message);
//        } else {
//            log.warn("⚠️ [中间层] 业务层未设置TTS音频回调");
        }
    }

    /**
     * 处理TTS打断消息
     *
     * 功能说明：
     * 1. 接收服务器发送的TTS播放打断通知
     * 2. 通知业务层停止当前TTS播放
     *
     * @param message TTS打断消息
     * @param request 连接请求信息
     */
    private void handleTTSInterruptMessage(TTSInterruptMessage message, WebSocketConnectionRequest request) {
        log.info("⏹️ [中间层] 处理TTS打断消息，打断原因: {}", message.getReason());

        // 将TTS打断事件回调给业务层，业务层需要停止当前音频播放
        if (request.getCallbacks().getOnTTSInterrupt() != null) {
            request.getCallbacks().getOnTTSInterrupt().accept(message);
        } else {
            log.warn("⚠️ [中间层] 业务层未设置TTS打断回调");
        }
    }

    /**
     * 处理静默超时消息
     *
     * 功能说明：
     * 1. 接收服务器发送的静默超时通知
     * 2. 通知业务层用户长时间无语音输入
     *
     * @param message 静默超时消息
     * @param request 连接请求信息
     */
    private void handleSilenceTimeoutMessage(SilenceTimeoutMessage message, WebSocketConnectionRequest request) {
//        log.info("🔇 [中间层] 处理静默超时消息: {}", message.getMessage());

        // 将静默超时事件回调给业务层，业务层可以提示用户或执行其他逻辑
        if (request.getCallbacks().getOnSilenceTimeout() != null) {
            request.getCallbacks().getOnSilenceTimeout().accept(message);
        } else {
            log.warn("⚠️ [中间层] 业务层未设置静默超时回调");
        }
    }

    /**
     * 处理错误消息
     *
     * 功能说明：
     * 1. 接收服务器发送的各种错误通知
     * 2. 记录错误信息并回调给业务层处理
     *
     * @param message 错误消息
     * @param request 连接请求信息
     */
    private void handleErrorMessage(ErrorMessage message, WebSocketConnectionRequest request) {
        log.error("❌ [中间层] 处理服务器错误消息，错误类型: {}, 错误内容: {}",
                 message.getErrorType(), message.getMessage());

        // 将错误消息回调给业务层，业务层可以根据错误类型进行相应处理
        if (request.getCallbacks().getOnErrorMessage() != null) {
            request.getCallbacks().getOnErrorMessage().accept(message);
        } else {
            log.warn("⚠️ [中间层] 业务层未设置错误消息回调");
        }
    }

    // ========== 辅助工具方法 ==========

    /**
     * 获取配置数据
     *
     * 功能说明：
     * 1. 从业务参数中提取配置信息
     * 2. 添加系统默认配置
     * 3. 可以扩展为从配置中心动态获取
     *
     * @param request 连接请求信息
     * @return 完整的配置数据
     */
    private Map<String, Object> getConfigurationData(WebSocketConnectionRequest request) {
        // 复制业务参数作为基础配置
        Map<String, Object> configData = new ConcurrentHashMap<>(request.getBusinessParams());

        // 添加系统默认配置（如果业务参数中没有指定）
        configData.putIfAbsent("heartbeatInterval", config.getHeartbeatInterval()); // 心跳间隔
        configData.putIfAbsent("connectionTimeout", config.getConnectionTimeout()); // 连接超时
        configData.putIfAbsent("audioFormat", "pcm_16khz_16bit_mono"); // 音频格式

        log.info("📋 [配置] 最终配置数据: {}", configData);
        return configData;
    }

    /**
     * 构建init响应消息 - 增强版本
     *
     * 功能说明：
     * 1. 从业务参数中提取智能体ID和公司ID
     * 2. 调用AiAgentService获取智能体信息
     * 3. 调用CrmAiAgentCallSettingService获取通话设置
     * 4. 组装完整的init响应消息
     * 5. 设置TTS语言为"zh"，TTS声音名称使用智能体的voiceCode
     *
     * @param clientId 客户端ID
     * @param businessParams 业务参数
     * @param configData 配置数据
     * @return 构建好的init响应消息
     */
    private InitMessage buildInitResponseMessage(String clientId, Map<String, Object> businessParams, Map<String, Object> configData) {
        log.info("🔧 [消息构建] 开始构建init响应消息，clientId: {}", clientId);

        // 创建init响应消息对象
        InitMessage message = new InitMessage();
        message.setType("init"); // 消息类型
        message.setClientId(clientId); // 客户端标识
        message.setStatus("ready"); // 状态：准备就绪

        try {
            // 1. 从业务参数中提取关键信息
            String aiAgentId = (String) businessParams.get("aiAgentId"); // 智能体ID
            String companyId = (String) businessParams.get("companyId"); // 公司ID

            log.info("📋 [消息构建] 提取业务参数 - aiAgentId: {}, companyId: {}", aiAgentId, companyId);

            // 2. 获取智能体信息
            AiAgentInfoVo aiAgentInfo = null;
            if (aiAgentId != null && aiAgentService != null) {
                try {
                    AjaxResult<AiAgentInfoVo> agentResult = aiAgentService.getAgentById(aiAgentId);
                    if (agentResult != null && agentResult.getCode() == 200) {
                        aiAgentInfo = agentResult.getData();
                        log.info("✅ [消息构建] 成功获取智能体信息: {}", aiAgentInfo.getAiAgentName());
                    } else {
                        log.warn("⚠️ [消息构建] 获取智能体信息失败: {}", agentResult != null ? agentResult.getMsg() : "返回结果为空");
                    }
                } catch (Exception e) {
                    log.error("❌ [消息构建] 调用AiAgentService异常", e);
                }
            } else if (aiAgentService == null) {
                log.warn("⚠️ [消息构建] AiAgentService未注入，跳过智能体信息获取（测试环境）");
            }

            // 3. 获取通话设置信息
            AiAgentCallSettingVo callSetting = null;
            if (aiAgentId != null && crmAiAgentCallSettingService != null) {
                try {
                    callSetting = crmAiAgentCallSettingService.getCallSettingDetail(aiAgentId);
                    if (callSetting != null) {
                        log.info("✅ [消息构建] 成功获取通话设置信息");
                    } else {
                        log.warn("⚠️ [消息构建] 获取通话设置信息为空");
                    }
                } catch (Exception e) {
                    log.error("❌ [消息构建] 调用CrmAiAgentCallSettingService异常", e);
                }
            } else if (crmAiAgentCallSettingService == null) {
                log.warn("⚠️ [消息构建] CrmAiAgentCallSettingService未注入，跳过通话设置获取（测试环境）");
            }

            // 4. 设置基础业务标识信息
            if (companyId != null) {
                message.setCompanyId(companyId); // 公司ID
            }
            if (aiAgentId != null) {
                message.setAiAgentId(aiAgentId); // 智能体ID
            }
            if (businessParams.containsKey("channelId")) {
                message.setChannelId((String) businessParams.get("channelId")); // 渠道ID
            }
            if (businessParams.containsKey("language")) {
                message.setLanguage((String) businessParams.get("language")); // stt语言设置
            }
            if (businessParams.containsKey("s3JsonPath")) {
                message.setS3JsonPath((String) businessParams.get("s3JsonPath")); // s3路径
            }
            if (businessParams.containsKey("s3WavPath")) {
                message.setS3WavPath((String) businessParams.get("s3WavPath")); // s3音频路径
            }

            // 5. 设置TTS相关配置
            message.setTtsLanguage("zh"); // 固定设置为中文

            // 6. 设置TTS声音名称 - 使用智能体的voiceCode
            if (aiAgentInfo != null && aiAgentInfo.getVoiceCode() != null) {
                message.setTtsVoiceName(aiAgentInfo.getVoiceCode());
                log.info("🎵 [消息构建] 使用智能体声音: {}", aiAgentInfo.getVoiceCode());
            } else {
                // 如果没有获取到智能体声音，使用默认声音
                message.setTtsVoiceName("zh-CN-XiaoxiaoNeural");
                log.warn("⚠️ [消息构建] 未获取到智能体声音，使用默认声音: zh-CN-XiaoxiaoNeural");
            }

            // 7. 设置通话设置相关信息
            if (callSetting != null) {
                message.setSettingMap(callSetting.getSettingMap());
                if (callSetting.getPhraseList() != null && !callSetting.getPhraseList().isEmpty()) {
                    message.setPhraseList(callSetting.getPhraseList());
                }
            }

            // 8. 设置音频格式配置（根据新的API文档要求）
            // 从业务参数中获取音频格式配置，如果没有则使用默认值
//            String audioFormat = (String) businessParams.getOrDefault("audioFormat", "mulaw");
//            Integer audioSampleRate = (Integer) businessParams.getOrDefault("audioSampleRate", 8000);
//
//            message.setAudioFormat(audioFormat);
//            message.setAudioSampleRate(audioSampleRate);
//
//            log.info("🎵 [消息构建] 音频配置 - 格式: {}, 采样率: {}", audioFormat, audioSampleRate);

            log.info("✅ [消息构建] init响应消息构建完成");
            log.info("📋 [消息构建] 完整init响应消息: {}", message);

        } catch (Exception e) {
            log.error("💥 [消息构建] 构建init响应消息时发生异常", e);
            // 即使发生异常，也要返回基本的消息结构
            if (businessParams.containsKey("companyId")) {
                message.setCompanyId((String) businessParams.get("companyId"));
            }
            if (businessParams.containsKey("channelId")) {
                message.setChannelId((String) businessParams.get("channelId"));
            }
            message.setTtsLanguage("zh");
            message.setTtsVoiceName("zh-CN-XiaoxiaoNeural");
            // 设置默认音频格式配置
            message.setAudioFormat("mulaw");
            message.setAudioSampleRate(8000);
        }

        return message;
    }

    /**
     * 中间层处理语音识别结果
     *
     * 功能说明：
     * 1. 对语音识别文本进行清理和格式化
     * 2. 可以扩展为敏感词过滤、文本纠错等
     *
     * @param text 原始语音识别文本
     * @return 处理后的文本
     */
    private String processSTTResult(String text) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        // 基础文本处理
        String processed = text.trim(); // 去除首尾空格

        // 可以在这里扩展更多处理逻辑：
        // 1. 去除多余空格：processed = processed.replaceAll("\\s+", " ");
        // 2. 敏感词过滤：processed = filterSensitiveWords(processed);
        // 3. 文本纠错：processed = correctText(processed);
        // 4. 格式化处理：processed = formatText(processed);

        log.info("📝 [文本处理] STT原文: {} -> 处理后: {}", text, processed);
        return processed;
    }

    /**
     * 中间层处理AI回复内容
     *
     * 功能说明：
     * 1. 对AI回复进行内容审核和格式化
     * 2. 可以扩展为长度限制、敏感词过滤等
     *
     * @param text 原始AI回复文本
     * @return 处理后的文本
     */
    private String processAIResponse(String text) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        // 基础文本处理
        String processed = text.trim(); // 去除首尾空格

        // 可以在这里扩展更多处理逻辑：
        // 1. 内容审核：processed = auditContent(processed);
        // 2. 敏感词过滤：processed = filterSensitiveWords(processed);
        // 3. 长度限制：processed = limitLength(processed, maxLength);
        // 4. 格式化：processed = formatResponse(processed);

        log.info("🤖 [内容处理] AI原文: {} -> 处理后: {}", text, processed);
        return processed;
    }

    /**
     * 中间层处理TTS音频数据
     *
     * 功能说明：
     * 1. 对TTS音频进行格式处理和优化
     * 2. 可以扩展为音频格式转换、音量调节等
     *
     * @param audioData 原始音频数据（Base64格式）
     * @return 处理后的音频数据
     */
    private String processTTSAudio(String audioData) {
        if (audioData == null || audioData.trim().isEmpty()) {
            return audioData;
        }

        // 基础音频处理（这里暂时直接返回，可以扩展）
        String processed = audioData;

        // 可以在这里扩展更多处理逻辑：
        // 1. 音频格式转换：processed = convertAudioFormat(processed);
        // 2. 音量调节：processed = adjustVolume(processed, volumeLevel);
        // 3. 音质优化：processed = enhanceQuality(processed);
        // 4. 压缩处理：processed = compressAudio(processed);

        log.info("🔊 [音频处理] 音频数据长度: {} -> 处理后长度: {}",
                 audioData.length(), processed.length());
        return processed;
    }

    /**
     * 将音频数据编码为Base64格式
     *
     * 功能说明：
     * 1. 将原始字节数组转换为Base64字符串
     * 2. 符合WebSocket API的数据传输要求
     *
     * @param audioData 原始音频字节数组
     * @return Base64编码的音频字符串
     */
    private String encodeAudioToBase64(byte[] audioData) {
        if (audioData == null || audioData.length == 0) {
            log.warn("⚠️ [音频编码] 音频数据为空，返回空字符串");
            return "";
        }

        String encoded = java.util.Base64.getEncoder().encodeToString(audioData);
        log.info("🔄 [音频编码] 原始数据: {} bytes -> Base64: {} chars",
                 audioData.length, encoded.length());
        return encoded;
    }
    
    // ========== WebSocket底层操作实现 ==========

    /**
     * 建立WebSocket连接
     */
    private boolean connectToWebSocket(String url, String clientId, WebSocketEventHandler handler) {
        try {
            log.info("🔗 建立WebSocket连接，地址: {}, 客户端ID: {}", url, clientId);

            WebSocketClient client = new WebSocketClient(URI.create(url)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
//                    log.info("✅ WebSocket连接已建立，客户端ID: {}", clientId);
                    handler.onConnected(clientId);
                }

                @Override
                public void onMessage(String message) {
                    handler.onMessage(message);
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.warn("❌ WebSocket连接关闭，客户端ID: {}, 代码: {}, 原因: {}", clientId, code, reason);

                    // 停止心跳任务
                    stopHeartbeat(clientId);

                    handler.onDisconnected(clientId, code, reason);
                    clients.remove(clientId);
                    requests.remove(clientId);
                }

                @Override
                public void onError(Exception ex) {
                    log.error("💥 WebSocket连接错误，客户端ID: {}", clientId, ex);
                    handler.onError(clientId, ex);
                }
            };

            // 设置连接超时
            client.setConnectionLostTimeout(30);

            // 建立连接
            boolean connected = client.connectBlocking();
            if (connected) {
                clients.put(clientId, client);
//                log.info("✅ WebSocket连接建立成功1，客户端ID: {}", clientId);

                // 启动心跳任务
//                startHeartbeat(clientId);
            }

            return connected;

        } catch (Exception e) {
            log.error("💥 建立WebSocket连接时发生异常，客户端ID: {}", clientId, e);
            return false;
        }
    }

    /**
     * 发送消息到WebSocket服务器
     *
     * 功能说明：
     * 1. 检查连接状态
     * 2. 将消息对象序列化为JSON
     * 3. 通过WebSocket发送消息
     * 4. 记录发送结果
     *
     * @param clientId 客户端ID
     * @param message 要发送的消息对象
     * @return 发送是否成功
     */
    private boolean sendMessage(String clientId, BaseMessage message) {
        try {
            // 获取对应的WebSocket客户端
            WebSocketClient client = clients.get(clientId);
            if (client == null) {
                log.error("❌ WebSocket客户端不存在，客户端ID: {}", clientId);
                return false;
            }

            if (!client.isOpen()) {
                log.error("❌ WebSocket连接未打开，客户端ID: {}", clientId);
                return false;
            }

            // 将消息对象序列化为JSON字符串
            String jsonMessage = JSON.toJSONString(message);

            // 通过WebSocket发送JSON消息
            client.send(jsonMessage);

            log.info("📤 WebSocket消息发送成功，客户端ID: {}, 消息类型: {}, 消息长度: {} chars",
                     clientId, message.getType(), jsonMessage.length());
            return true;

        } catch (Exception e) {
            log.error("💥 发送WebSocket消息时发生异常，客户端ID: {}, 消息类型: {}",
                     clientId, message.getType(), e);
            return false;
        }
    }

    /**
     * 关闭WebSocket连接并清理资源
     *
     * 功能说明：
     * 1. 关闭底层WebSocket连接
     * 2. 从连接管理器中移除
     * 3. 清理相关资源
     *
     * @param clientId 客户端ID
     */
    public void closeWebSocketConnection(String clientId) {
        try {
            // 停止心跳任务
            stopHeartbeat(clientId);

            WebSocketClient client = clients.get(clientId);
            if (client != null) {
                // 关闭WebSocket连接
                if (client.isOpen()) {
                    client.close();
                    log.info("🛑 WebSocket底层连接已关闭，客户端ID: {}", clientId);
                }

                // 从管理器中移除
                clients.remove(clientId);
                requests.remove(clientId);

                log.info("🧹 WebSocket连接资源已清理，客户端ID: {}", clientId);
            } else {
                log.warn("⚠️ 要关闭的WebSocket客户端不存在，客户端ID: {}", clientId);
            }
        } catch (Exception e) {
            log.error("💥 关闭WebSocket连接时发生异常，客户端ID: {}", clientId, e);
        }
    }

    /**
     * 解析WebSocket消息 - JSON消息解析器
     *
     * 功能说明：
     * 1. 解析JSON格式的WebSocket消息
     * 2. 根据消息类型创建对应的消息对象
     * 3. 处理解析异常和未知消息类型
     *
     * @param message 原始JSON消息字符串
     * @return 解析后的消息对象，解析失败返回null
     */
    private BaseMessage parseMessage(String message) {
        try {
            // 解析JSON对象
            JSONObject jsonObject = JSON.parseObject(message);
            log.info("📨2⃣️ [中间层] 解析WebSocket原始消息: {}", jsonObject);
            String type = jsonObject.getString("type");

            if (type == null || type.trim().isEmpty()) {
                log.warn("⚠️ 消息类型为空或无效，忽略消息: {}", message);
                return null;
            }

            // 根据消息类型创建对应的消息对象
            switch (type) {
                case "init":
                    // 初始化消息
                    return JSON.parseObject(message, InitMessage.class);
                case "stt_result":
                    // 语音识别结果消息
//                    return JSON.parseObject(message, STTResultMessage.class);
                    return null;
                case "api_response":
                    // AI回复消息
//                    return JSON.parseObject(message, ApiResponseMessage.class);
                    return null;
                case "tts_audio":
                    // TTS音频消息
                    return JSON.parseObject(message, TTSMessage.class);
                case "tts_interrupt":
                    // TTS打断消息
                    return JSON.parseObject(message, TTSInterruptMessage.class);
                case "silence_timeout":
                    // 静默超时消息
                    return JSON.parseObject(message, SilenceTimeoutMessage.class);
                case "heartbeat":
                    // 心跳消息
//                    log.warn("⚠️ 收到心跳消息类型: {}, 完整消息: {}", type, message);
                    return null;
                case "error":
                    // 错误消息
                    return JSON.parseObject(message, ErrorMessage.class);
                default:
                    // 未知消息类型
                    log.warn("⚠️ 不处理 - 收到未知消息类型: {}, 完整消息: {}", type, message);
                    return null;
            }

        } catch (Exception e) {
            log.error("💥 解析WebSocket消息时发生异常，原始消息: {}", message, e);
            return null;
        }
    }

    /**
     * 清理资源 - Spring Bean销毁时调用
     *
     * 功能说明：
     * 1. 在Spring容器关闭时自动调用
     * 2. 关闭所有活跃的WebSocket连接
     * 3. 清理内存中的连接管理数据
     * 4. 确保资源正确释放
     */
    @PreDestroy
    public void cleanup() {
        log.info("🧹 开始清理WebSocket桥接服务资源，当前活跃连接数: {}", clients.size());

        // 关闭所有活跃的WebSocket连接
        clients.values().forEach(client -> {
            try {
                if (client.isOpen()) {
                    client.close();
                    log.info("🛑 已关闭WebSocket连接");
                }
            } catch (Exception e) {
                log.error("💥 清理WebSocket连接时发生异常", e);
            }
        });

        // 停止所有心跳任务
        heartbeatTasks.values().forEach(task -> {
            try {
                if (!task.isCancelled()) {
                    task.cancel(true);
                    log.info("🛑 已停止心跳任务");
                }
            } catch (Exception e) {
                log.error("💥 停止心跳任务时发生异常", e);
            }
        });

        // 关闭心跳线程池
        try {
            heartbeatExecutor.shutdown();
            if (!heartbeatExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                heartbeatExecutor.shutdownNow();
                log.warn("⚠️ 心跳线程池强制关闭");
            } else {
                log.info("✅ 心跳线程池正常关闭");
            }
        } catch (InterruptedException e) {
            heartbeatExecutor.shutdownNow();
            Thread.currentThread().interrupt();
            log.error("💥 关闭心跳线程池时被中断", e);
        }

        // 清理内存中的管理数据
        clients.clear();
        requests.clear();
        heartbeatTasks.clear();

        log.info("✅ WebSocket桥接服务资源清理完成");
    }

    // ========== 数据结构定义 ==========

    /**
     * WebSocket连接请求参数
     *
     * 包含创建WebSocket连接所需的所有信息：
     * 1. WebSocket服务器地址
     * 2. 业务逻辑参数
     * 3. 事件回调函数集合
     */
    @Data
    public static class WebSocketConnectionRequest {
        /**
         * WebSocket连接地址
         * 例如: "ws://192.168.110.21:8765"
         */
        private String webSocketUrl;

        /**
         * 业务逻辑参数值
         * 包含业务相关的配置信息，如：
         * - companyId: 公司ID
         * - channelId: 渠道ID
         * - agentId: 坐席ID
         * - language: 语言设置
         * - voiceName: 语音名称
         */
        private Map<String, Object> businessParams;

        /**
         * 事件回调函数集合
         * 定义各种WebSocket事件的处理回调
         */
        private WebSocketEventCallbacks callbacks;
    }

    /**
     * WebSocket事件回调集合
     *
     * 定义了所有WebSocket事件的回调函数，业务层通过设置这些回调
     * 来处理各种WebSocket事件。所有回调都是可选的，未设置的回调
     * 在事件发生时会被忽略。
     */
    @Data
    public static class WebSocketEventCallbacks {
        /**
         * 连接建立并初始化完成回调
         *
         * 重要说明：
         * - 这个回调在收到服务器init事件并成功响应后才会触发
         * - 不是在WebSocket底层连接建立时触发
         * - 收到此回调表示连接完全可用，可以开始发送音频数据
         *
         * 参数：clientId - 客户端ID
         */
        private Consumer<String> onConnected;

        /**
         * 语音识别结果回调
         *
         * 当服务器返回语音识别结果时触发
         * 参数：STTResultMessage - 包含识别文本和是否为最终结果
         */
        private Consumer<STTResultMessage> onSTTResult;

        /**
         * AI回复回调
         *
         * 当服务器返回AI对话回复时触发
         * 参数：ApiResponseMessage - 包含AI回复文本和是否为最终回复
         */
        private Consumer<ApiResponseMessage> onApiResponse;

        /**
         * TTS音频回调
         *
         * 当服务器返回文字转语音音频数据时触发
         * 参数：TTSMessage - 包含Base64编码的音频数据
         */
        private Consumer<TTSMessage> onTTSAudio;

        /**
         * TTS打断回调
         *
         * 当服务器要求打断当前TTS播放时触发
         * 参数：TTSInterruptMessage - 包含打断原因
         */
        private Consumer<TTSInterruptMessage> onTTSInterrupt;

        /**
         * 静默超时回调
         *
         * 当用户长时间无语音输入导致超时时触发
         * 参数：SilenceTimeoutMessage - 包含超时时长信息
         */
        private Consumer<SilenceTimeoutMessage> onSilenceTimeout;

        /**
         * 错误消息回调
         *
         * 当服务器发送错误消息时触发
         * 参数：ErrorMessage - 包含错误类型和错误信息
         */
        private Consumer<ErrorMessage> onErrorMessage;

        /**
         * 连接断开回调
         *
         * 当WebSocket连接断开时触发
         * 参数：clientId - 断开连接的客户端ID
         */
        private Consumer<String> onDisconnected;

        /**
         * 系统错误回调
         *
         * 当系统发生异常（如连接异常、解析异常等）时触发
         * 参数：Exception - 异常对象
         */
        private Consumer<Exception> onError;
    }

    // ========== 心跳管理方法 ==========

    /**
     * 启动心跳任务
     *
     * 功能说明：
     * 1. 为指定的WebSocket连接启动定时心跳任务
     * 2. 根据配置的心跳间隔定期发送心跳消息
     * 3. 保持WebSocket连接活跃，防止被网络设备断开
     * 4. 如果连接已有心跳任务，会先停止旧任务再启动新任务
     *
     * @param clientId 客户端ID，标识要启动心跳的连接
     */
    private void startHeartbeat(String clientId) {
        try {
            // 如果已有心跳任务，先停止
            stopHeartbeat(clientId);

            // 获取心跳间隔配置（毫秒）
            long heartbeatInterval = config.getHeartbeatInterval();

            log.info("💓 启动心跳任务，客户端ID: {}, 心跳间隔: {}ms", clientId, heartbeatInterval);

            // 创建定时心跳任务
            ScheduledFuture<?> heartbeatTask = heartbeatExecutor.scheduleWithFixedDelay(() -> {
                try {
                    // 检查连接是否仍然有效
                    WebSocketClient client = clients.get(clientId);
                    if (client == null || !client.isOpen()) {
                        log.warn("⚠️ WebSocket连接已关闭，停止心跳任务，客户端ID: {}", clientId);
                        stopHeartbeat(clientId);
                        return;
                    }

                    // 创建并发送心跳消息
                    HeartbeatMessage heartbeatMessage = new HeartbeatMessage(clientId);
                    boolean sent = sendMessage(clientId, heartbeatMessage);

                    if (sent) {
                        log.info("💓 心跳消息发送成功，客户端ID: {}", clientId);
                    } else {
                        log.warn("⚠️ 心跳消息发送失败，客户端ID: {}", clientId);
                    }

                } catch (Exception e) {
                    log.error("💥 发送心跳消息时发生异常，客户端ID: {}", clientId, e);
                }
            }, heartbeatInterval, heartbeatInterval, TimeUnit.MILLISECONDS);

            // 保存心跳任务引用，用于后续管理
            heartbeatTasks.put(clientId, heartbeatTask);

            log.info("✅ 心跳任务启动成功，客户端ID: {}", clientId);

        } catch (Exception e) {
            log.error("💥 启动心跳任务时发生异常，客户端ID: {}", clientId, e);
        }
    }

    /**
     * 停止心跳任务
     *
     * 功能说明：
     * 1. 停止指定连接的心跳任务
     * 2. 取消定时任务的执行
     * 3. 清理心跳任务管理数据
     * 4. 在连接关闭或异常时调用
     *
     * @param clientId 客户端ID，标识要停止心跳的连接
     */
    private void stopHeartbeat(String clientId) {
        try {
            ScheduledFuture<?> heartbeatTask = heartbeatTasks.remove(clientId);
            if (heartbeatTask != null) {
                // 取消心跳任务
                boolean cancelled = heartbeatTask.cancel(true);
                if (cancelled) {
                    log.info("🛑 心跳任务已停止，客户端ID: {}", clientId);
                } else {
                    log.warn("⚠️ 心跳任务停止失败（可能已完成），客户端ID: {}", clientId);
                }
            } else {
                log.info("📝 没有找到要停止的心跳任务，客户端ID: {}", clientId);
            }
        } catch (Exception e) {
            log.error("💥 停止心跳任务时发生异常，客户端ID: {}", clientId, e);
        }
    }

    /**
     * 检查心跳任务状态
     *
     * 功能说明：
     * 1. 检查指定连接的心跳任务是否正在运行
     * 2. 用于调试和监控心跳状态
     * 3. 返回心跳任务的运行状态
     *
     * @param clientId 客户端ID
     * @return true表示心跳任务正在运行，false表示没有心跳任务或任务已停止
     */
    private boolean isHeartbeatRunning(String clientId) {
        ScheduledFuture<?> heartbeatTask = heartbeatTasks.get(clientId);
        return heartbeatTask != null && !heartbeatTask.isCancelled() && !heartbeatTask.isDone();
    }

    /**
     * 写入事件数据到文件
     *
     * 功能说明：
     * 1. 将WebSocket事件数据记录到文件中
     * 2. 按客户端ID分别创建日志文件
     * 3. 包含时间戳、事件类型、数据内容等信息
     * 4. 用于调试和数据分析
     *
     * @param clientId 客户端ID
     * @param eventType 事件类型（onConnected、onMessage、onDisconnected、onError）
     * @param data 事件数据内容
     */
    private void writeEventToFile(String clientId, String eventType, String data) {
        try {
            // 确保日志目录存在
            Path logDir = Paths.get(LOG_DIR);
            if (!Files.exists(logDir)) {
                Files.createDirectories(logDir);
            }

            // 创建以客户端ID命名的日志文件
            String fileName = String.format("websocket_events_%s.log", clientId);
            Path logFile = logDir.resolve(fileName);

            // 格式化日志内容
            String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);
            String logEntry = String.format("[%s] [%s] %s%n", timestamp, eventType, data);

            // 追加写入文件
            try (FileWriter writer = new FileWriter(logFile.toFile(), true)) {
                writer.write(logEntry);
                writer.flush();
            }

            log.debug("📝 事件数据已写入文件: {} - {}", fileName, eventType);

        } catch (IOException e) {
            log.error("💥 写入事件数据到文件失败，客户端ID: {}, 事件类型: {}", clientId, eventType, e);
        }
    }

    /**
     * 写入连接事件到文件
     *
     * @param clientId 客户端ID
     */
    private void writeConnectedEventToFile(String clientId) {
        String data = String.format("WebSocket连接已建立，客户端ID: %s", clientId);
        writeEventToFile(clientId, "onConnected", data);
    }

    /**
     * 写入消息事件到文件
     *
     * @param clientId 客户端ID
     * @param message 收到的消息内容
     */
    private void writeMessageEventToFile(String clientId, String message) {
        String data = String.format("收到消息: %s", message);
        writeEventToFile(clientId, "onMessage", data);
    }

    /**
     * 写入断开连接事件到文件
     *
     * @param clientId 客户端ID
     * @param code 断开代码
     * @param reason 断开原因
     */
    private void writeDisconnectedEventToFile(String clientId, int code, String reason) {
        String data = String.format("连接断开，代码: %d, 原因: %s", code, reason);
        writeEventToFile(clientId, "onDisconnected", data);
    }

    /**
     * 写入错误事件到文件
     *
     * @param clientId 客户端ID
     * @param exception 异常信息
     */
    private void writeErrorEventToFile(String clientId, Exception exception) {
        String data = String.format("发生错误: %s - %s", exception.getClass().getSimpleName(), exception.getMessage());
        writeEventToFile(clientId, "onError", data);
    }
}
