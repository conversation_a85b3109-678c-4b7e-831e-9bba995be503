package com.goclouds.crm.platform.aiagent.wsbridge.controller;

import com.goclouds.crm.platform.aiagent.wsbridge.service.WebSocketBridgeService;
import com.goclouds.crm.platform.aiagent.wsbridge.service.WebSocketBridgeService.WebSocketConnectionRequest;
import com.goclouds.crm.platform.aiagent.wsbridge.service.WebSocketBridgeService.WebSocketEventCallbacks;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.*;
import com.goclouds.crm.platform.annotation.NoLogin;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.http.MediaType;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.io.IOException;

/**
 * WebSocket桥接服务测试控制器
 *
 * 功能说明：
 * 1. 提供HTTP接口来测试WebSocket桥接服务
 * 2. 演示如何使用WebSocketBridgeService
 * 3. 方便本地开发和调试
 * 4. 提供完整的WebSocket连接测试功能
 * 5. 支持心跳功能测试
 *
 * API接口：
 * 1. POST /wsbridge/test/connect - 创建WebSocket连接
 * 2. POST /wsbridge/test/send-audio/{clientId} - 发送音频数据
 * 3. POST /wsbridge/test/heartbeat/{clientId} - 测试心跳功能
 * 4. POST /wsbridge/test/disconnect/{clientId} - 断开连接
 * 5. GET /wsbridge/test/status/{clientId} - 查看连接状态
 *
 * <AUTHOR> Agent
 */
@RestController
@RequestMapping("/wsbridge/test")
@Slf4j
public class WebSocketBridgeTestController {

    @Autowired
    private WebSocketBridgeService webSocketBridgeService;

    // 存储活跃的连接信息，用于测试管理
    private final Map<String, TestConnectionInfo> activeConnections = new ConcurrentHashMap<>();

    // 存储SSE连接，用于实时推送音频数据
    private final Map<String, SseEmitter> audioStreamEmitters = new ConcurrentHashMap<>();

    /**
     * 创建WebSocket连接测试接口
     *
     * 功能说明：
     * 1. 创建真实的WebSocket连接
     * 2. 使用真实的业务参数和服务调用
     * 3. 完整的init事件处理流程
     * 4. 返回连接结果和客户端ID
     *
     * @param request 连接请求参数
     * @return 连接结果
     */
    @PostMapping("/connect")
    public AjaxResult<?> createConnection(@RequestBody TestConnectionRequest request) {
        try {
            request.setWebSocketUrl(request.getWebSocketUrl() + "?client_id=" + request.getWorkOrderId());

            // 创建连接请求
            WebSocketConnectionRequest connectionRequest = createRealBusinessConnectionRequest(request);

            // 创建连接
            CompletableFuture<String> connectionFuture = webSocketBridgeService.createWebSocketConnection(connectionRequest);

            // 等待连接建立（最多30秒）
            String clientId = connectionFuture.get(30, TimeUnit.SECONDS);

            if (clientId != null) {
                // 保存连接信息用于后续测试
                TestConnectionInfo connectionInfo = new TestConnectionInfo();
                connectionInfo.setClientId(clientId);
                connectionInfo.setWebSocketUrl(request.getWebSocketUrl());
                connectionInfo.setConnectTime(System.currentTimeMillis());
                connectionInfo.setStatus("CONNECTED");
                activeConnections.put(clientId, connectionInfo);

                Map<String, Object> result = new HashMap<>();
                result.put("clientId", clientId);
                result.put("status", "CONNECTED");
                result.put("message", "WebSocket连接创建成功");
                result.put("connectTime", connectionInfo.getConnectTime());

                return AjaxResult.ok(result,"连接创建成功");
            } else {
                log.error("❌ WebSocket连接创建失败");
                return AjaxResult.failure("WebSocket连接创建失败");
            }

        } catch (Exception e) {
            log.error("💥 创建WebSocket连接时发生异常", e);
            return AjaxResult.failure("创建连接异常: " + e.getMessage());
        }
    }

    /**
     * 发送音频数据测试接口
     *
     * @param clientId 客户端ID
     * @param request 音频数据请求（可选）
     * @return 发送结果
     */
    @PostMapping("/send-audio")
    public AjaxResult<?> sendAudioData(@RequestParam String clientId, @RequestBody(required = false) AudioDataRequest request) {
        try {
            log.info("🎵 开始发送音频数据，客户端ID: {}", clientId);

            // 检查连接是否存在
            TestConnectionInfo connectionInfo = activeConnections.get(clientId);
            if (connectionInfo == null) {
                return AjaxResult.failure("连接不存在，请先创建连接");
            }

            byte[] audioData;

            // 如果请求中包含音频数据，使用实际数据；否则生成测试数据
            if (request != null && request.getAudioData() != null && !request.getAudioData().isEmpty()) {
                try {
                    // 解码Base64音频数据
                    audioData = java.util.Base64.getDecoder().decode(request.getAudioData());
                    log.info("📥 接收到实际音频数据，大小: {} bytes", audioData.length);
                } catch (IllegalArgumentException e) {
                    log.error("❌ Base64音频数据解码失败: {}", e.getMessage());
                    return AjaxResult.failure("音频数据格式错误");
                }
            } else {
                // 生成测试音频数据
                audioData = generateTestAudioData();
                log.info("🎵 使用生成的测试音频数据，大小: {} bytes", audioData.length);
            }

            // 发送音频数据
            boolean sent = webSocketBridgeService.sendAudioData(clientId, audioData);

            if (sent) {
                log.info("✅ 音频数据发送成功，客户端ID: {}, 数据大小: {} bytes", clientId, audioData.length);

                Map<String, Object> result = new HashMap<>();
                result.put("clientId", clientId);
                result.put("audioDataSize", audioData.length);
                result.put("sendTime", System.currentTimeMillis());
                result.put("status", "SUCCESS");
                result.put("dataType", request != null && request.getAudioData() != null ? "REAL_AUDIO" : "TEST_AUDIO");

                return AjaxResult.ok(result, "音频数据发送成功");
            } else {
                log.error("❌ 音频数据发送失败，客户端ID: {}", clientId);
                return AjaxResult.failure("音频数据发送失败");
            }

        } catch (Exception e) {
            log.error("💥 发送音频数据时发生异常，客户端ID: {}", clientId, e);
            return AjaxResult.failure("发送音频数据异常: " + e.getMessage());
        }
    }

    /**
     * 心跳功能测试接口
     *
     * @param clientId 客户端ID
     * @return 心跳测试结果
     */
    @PostMapping("/heartbeat/{clientId}")
    public AjaxResult testHeartbeat(@PathVariable String clientId) {
        try {
            log.info("💓 开始心跳功能测试，客户端ID: {}", clientId);

            // 检查连接是否存在
            TestConnectionInfo connectionInfo = activeConnections.get(clientId);
            if (connectionInfo == null) {
                return AjaxResult.failure("连接不存在，请先创建连接");
            }

            // 等待一段时间观察心跳（15秒）
            log.info("⏰ 等待心跳消息发送（等待15秒观察心跳）...");
            Thread.sleep(15000);

            // 检查连接是否仍然活跃
            if (activeConnections.containsKey(clientId)) {
                log.info("✅ 心跳功能测试完成 - 连接保持活跃，客户端ID: {}", clientId);

                Map<String, Object> result = new HashMap<>();
                result.put("clientId", clientId);
                result.put("heartbeatTest", "SUCCESS");
                result.put("connectionStatus", "ACTIVE");
                result.put("testDuration", "15秒");
                result.put("testTime", System.currentTimeMillis());

                return AjaxResult.ok( result,"心跳功能测试成功");
            } else {
                log.warn("⚠️ 心跳测试期间连接已断开，客户端ID: {}", clientId);
                return AjaxResult.failure("心跳测试失败 - 连接已断开");
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("💥 心跳测试被中断，客户端ID: {}", clientId, e);
            return AjaxResult.failure("心跳测试被中断: " + e.getMessage());
        } catch (Exception e) {
            log.error("💥 心跳测试异常，客户端ID: {}", clientId, e);
            return AjaxResult.failure("心跳测试异常: " + e.getMessage());
        }
    }

    /**
     * 断开WebSocket连接测试接口
     *
     * @param clientId 客户端ID
     * @return 断开结果
     */
    @PostMapping("/disconnect/{clientId}")
    public AjaxResult disconnectConnection(@PathVariable String clientId) {
        try {
            log.info("🛑 开始断开WebSocket连接，客户端ID: {}", clientId);

            // 检查连接是否存在
            TestConnectionInfo connectionInfo = activeConnections.get(clientId);
            if (connectionInfo == null) {
                return AjaxResult.failure("连接不存在或已断开");
            }

            // 断开连接
            webSocketBridgeService.closeConnection(clientId);

            // 更新连接状态
            connectionInfo.setStatus("DISCONNECTED");
            connectionInfo.setDisconnectTime(System.currentTimeMillis());

            log.info("✅ WebSocket连接已断开，客户端ID: {}", clientId);

            Map<String, Object> result = new HashMap<>();
            result.put("clientId", clientId);
            result.put("status", "DISCONNECTED");
            result.put("message", "WebSocket连接已断开");
            result.put("disconnectTime", connectionInfo.getDisconnectTime());
            result.put("connectionDuration", connectionInfo.getDisconnectTime() - connectionInfo.getConnectTime());

            // 从活跃连接中移除
            activeConnections.remove(clientId);

            return AjaxResult.ok( result,"连接断开成功");

        } catch (Exception e) {
            log.error("💥 断开WebSocket连接时发生异常，客户端ID: {}", clientId, e);
            return AjaxResult.failure("断开连接异常: " + e.getMessage());
        }
    }

    /**
     * 查看连接状态接口
     *
     * @param clientId 客户端ID
     * @return 连接状态信息
     */
    @GetMapping("/status/{clientId}")
    public AjaxResult getConnectionStatus(@PathVariable String clientId) {
        try {
            log.info("📊 查看连接状态，客户端ID: {}", clientId);

            TestConnectionInfo connectionInfo = activeConnections.get(clientId);
            if (connectionInfo == null) {
                return AjaxResult.failure("连接不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("clientId", clientId);
            result.put("status", connectionInfo.getStatus());
            result.put("webSocketUrl", connectionInfo.getWebSocketUrl());
            result.put("connectTime", connectionInfo.getConnectTime());
            result.put("currentTime", System.currentTimeMillis());

            if ("CONNECTED".equals(connectionInfo.getStatus())) {
                result.put("connectionDuration", System.currentTimeMillis() - connectionInfo.getConnectTime());
            } else if (connectionInfo.getDisconnectTime() != null) {
                result.put("disconnectTime", connectionInfo.getDisconnectTime());
                result.put("totalDuration", connectionInfo.getDisconnectTime() - connectionInfo.getConnectTime());
            }

            return AjaxResult.ok( result,"连接状态查询成功");

        } catch (Exception e) {
            log.error("💥 查看连接状态时发生异常，客户端ID: {}", clientId, e);
            return AjaxResult.failure("查询连接状态异常: " + e.getMessage());
        }
    }

    /**
     * 获取所有活跃连接列表
     *
     * @return 活跃连接列表
     */
    @GetMapping("/connections")
    public AjaxResult getAllConnections() {
        try {
            log.info("📋 获取所有活跃连接列表");

            Map<String, Object> result = new HashMap<>();
            result.put("totalConnections", activeConnections.size());
            result.put("connections", activeConnections);
            result.put("queryTime", System.currentTimeMillis());

            return AjaxResult.ok(result, "获取连接列表成功");

        } catch (Exception e) {
            log.error("💥 获取连接列表时发生异常", e);
            return AjaxResult.failure("获取连接列表异常: " + e.getMessage());
        }
    }

    /**
     * 创建音频流SSE连接
     * 前端通过此接口建立SSE连接，用于接收实时音频数据
     * 注意：由于EventSource不支持自定义头部，此接口使用@NoLogin注解跳过认证
     *
     * @param clientId WebSocket客户端ID
     * @return SSE连接
     */
    @GetMapping(value = "/audio-stream/{clientId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @NoLogin
    public SseEmitter createAudioStream(@PathVariable String clientId) {
        log.info("🎵 创建音频流SSE连接，客户端ID: {}", clientId);

        // 创建SSE连接，设置超时时间为30分钟
        SseEmitter emitter = new SseEmitter(30 * 60 * 1000L);

        // 存储SSE连接
        audioStreamEmitters.put(clientId, emitter);

        // 设置连接完成和超时处理
        emitter.onCompletion(() -> {
            log.info("🔚 音频流SSE连接完成，客户端ID: {}", clientId);
            audioStreamEmitters.remove(clientId);
        });

        emitter.onTimeout(() -> {
            log.warn("⏰ 音频流SSE连接超时，客户端ID: {}", clientId);
            audioStreamEmitters.remove(clientId);
        });

        emitter.onError((ex) -> {
            log.error("❌ 音频流SSE连接错误，客户端ID: {}", clientId, ex);
            audioStreamEmitters.remove(clientId);
        });

        try {
            // 发送连接成功消息
            Map<String, Object> connectMessage = new HashMap<>();
            connectMessage.put("type", "connected");
            connectMessage.put("clientId", clientId);
            connectMessage.put("message", "音频流连接建立成功");
            connectMessage.put("timestamp", System.currentTimeMillis());

            emitter.send(SseEmitter.event()
                    .name("audio-stream-connected")
                    .data(connectMessage));

            log.info("✅ 音频流SSE连接建立成功，客户端ID: {}", clientId);

        } catch (IOException e) {
            log.error("💥 发送SSE连接成功消息失败，客户端ID: {}", clientId, e);
            audioStreamEmitters.remove(clientId);
            emitter.completeWithError(e);
        }

        return emitter;
    }

    // ========== 辅助方法 ==========

    /**
     * 创建真实业务场景的WebSocket连接请求
     *
     * @param request 测试连接请求
     * @return WebSocket连接请求对象
     */
    private WebSocketConnectionRequest createRealBusinessConnectionRequest(TestConnectionRequest request) {
        // 创建业务参数
        Map<String, Object> businessParams = new HashMap<>();
        businessParams.put("workOrderId", request.getWorkOrderId() != null ? request.getWorkOrderId() : "test_work_order_" + System.currentTimeMillis());
        businessParams.put("aiAgentId", request.getAiAgentId() != null ? request.getAiAgentId() : "ac19d983e9e747d6bb70010175493fe7");
        businessParams.put("companyId", request.getCompanyId() != null ? request.getCompanyId() : "d6ff59a027861d7d4133e1d6b6872464");
        businessParams.put("language", "zh-CN");
        businessParams.put("channelId", "7");
        businessParams.put("s3JsonPath", "s3://voice-wangjie-test/voice/test/0.json");
        businessParams.put("s3WavPath", "s3://voice-wangjie-test/voice/test/0.wav");

        // 创建事件回调处理器
        WebSocketEventCallbacks callbacks = new WebSocketEventCallbacks();

        // 连接成功回调
        callbacks.setOnConnected((clientId) -> {
            log.info("🎉 [API测试] WebSocket连接建立成功！客户端ID: {}", clientId);
        });

        // 连接断开回调 - 修正参数：只有clientId一个参数
        callbacks.setOnDisconnected((clientId) -> {
            log.warn("❌ [API测试] WebSocket连接断开，客户端ID: {}", clientId);

            // 更新连接状态
            TestConnectionInfo connectionInfo = activeConnections.get(clientId);
            if (connectionInfo != null) {
                connectionInfo.setStatus("DISCONNECTED");
                connectionInfo.setDisconnectTime(System.currentTimeMillis());
            }
        });

        /*// STT结果回调 - 修正方法名：setOnSTTResult
        callbacks.setOnSTTResult((message) -> {
            String resultType = message.getIsFinal() ? "最终结果" : "中间结果";
            log.info("🎤 [API测试] 语音识别{}: {}", resultType, message.getText());
        });

        // API响应回调
        callbacks.setOnApiResponse((message) -> {
            String responseType = message.getIsFinal() ? "最终回复" : "中间回复";
            log.info("📡 [API测试] AI{}: {}", responseType, message.getText());
        });*/

        // TTS音频回调 - 修正方法名：setOnTTSAudio
        callbacks.setOnTTSAudio((message) -> {
            log.info("🔊 [API测试] TTS音频数据，长度: {}, 格式: {}, 采样率: {}, 时长: {}秒",
                    (message.getAudio() != null ? message.getAudio().length() : 0),
                    message.getFormat(),
                    message.getSampleRate(),
                    message.getDuration());

            // 实时推送音频数据到前端 - 使用workOrderId作为clientId标识
            String clientIdForPush = request.getWorkOrderId() != null ? request.getWorkOrderId() : "unknown_client";
            pushAudioDataToFrontend(clientIdForPush, message);
        });

        // TTS打断回调 - 添加缺失的回调
        callbacks.setOnTTSInterrupt((message) -> {
            log.info("⏹️ [API测试] TTS播放打断，原因: {}", message.getReason());
            // 收到此事件的作用是：停止播放音频。

            // 推送TTS打断消息到前端 - 使用workOrderId作为clientId标识
            String clientIdForPush = request.getWorkOrderId() != null ? request.getWorkOrderId() : "unknown_client";
            pushTTSInterruptToFrontend(clientIdForPush, message);
        });

        // 静默超时回调 - 添加缺失的回调
        callbacks.setOnSilenceTimeout((message) -> {
            log.info("🔇 [API测试] 用户静默超时：{}", message.getMessage());
            // 执行close事件 - 推送静默超时消息到前端并断开连接
            String clientIdForPush = request.getWorkOrderId() != null ? request.getWorkOrderId() : "unknown_client";

            // 延迟断开WebSocket连接，给前端时间处理超时消息
            CompletableFuture.runAsync(() -> {

                try {

                    Thread.sleep(4000); // 等待4秒让前端处理消息
                    log.info("🔌 [API测试] 因静默超时断开WebSocket连接，客户端ID: {}", clientIdForPush);

                    pushSilenceTimeoutToFrontend(clientIdForPush, message);


                    webSocketBridgeService.closeWebSocketConnection(clientIdForPush);

                    // 更新连接状态
                    TestConnectionInfo connectionInfo = activeConnections.get(clientIdForPush);
                    if (connectionInfo != null) {
                        connectionInfo.setStatus("DISCONNECTED_TIMEOUT");
                        connectionInfo.setDisconnectTime(System.currentTimeMillis());
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("静默超时断开连接时被中断", e);
                } catch (Exception e) {
                    log.error("静默超时断开连接失败", e);
                }
            });
        });

        // 错误消息回调 - 添加缺失的回调
        callbacks.setOnErrorMessage((message) -> {
            log.error("❌ [API测试] 收到错误消息，错误类型: {}, 错误内容: {}",
                     message.getErrorType(), message.getMessage());
        });

        // 系统错误回调
        callbacks.setOnError((exception) -> {
            log.error("💥 [API测试] WebSocket系统发生错误", exception);
        });

        // 创建连接请求
        WebSocketConnectionRequest connectionRequest = new WebSocketConnectionRequest();
        connectionRequest.setWebSocketUrl(request.getWebSocketUrl());
        connectionRequest.setBusinessParams(businessParams);
        connectionRequest.setCallbacks(callbacks);

        return connectionRequest;
    }

    /**
     * 生成测试音频数据
     *
     * @return 测试音频数据（PCM 16kHz 16bit mono格式）
     */
    private byte[] generateTestAudioData() {
        // 生成1024字节的测试音频数据（约32ms的16kHz音频）
        byte[] audioData = new byte[1024];
        for (int i = 0; i < audioData.length; i += 2) {
            // 生成简单的正弦波音频数据
            double frequency = 440.0; // A音符频率
            double sampleRate = 16000.0; // 16kHz采样率
            double amplitude = 16383.0; // 16bit音频幅度（减半避免溢出）

            double sample = amplitude * Math.sin(2.0 * Math.PI * frequency * (i / 2) / sampleRate);
            short shortSample = (short) sample;

            // 转换为字节（小端序）
            audioData[i] = (byte) (shortSample & 0xFF);
            if (i + 1 < audioData.length) {
                audioData[i + 1] = (byte) ((shortSample >> 8) & 0xFF);
            }
        }
        return audioData;
    }

    /**
     * 推送音频数据到前端
     * 通过SSE连接将TTS音频数据实时推送给前端页面
     *
     * @param clientId 客户端ID
     * @param ttsMessage TTS音频消息
     */
    private void pushAudioDataToFrontend(String clientId, TTSMessage ttsMessage) {
        SseEmitter emitter = audioStreamEmitters.get(clientId);
        if (emitter == null) {
            log.debug("📡 没有找到客户端的SSE连接，跳过音频推送，客户端ID: {}", clientId);
            return;
        }

        try {
            // 构建音频数据消息
            Map<String, Object> audioData = new HashMap<>();
            audioData.put("type", "tts-audio");
            audioData.put("clientId", clientId);
            audioData.put("sessionId", ttsMessage.getSessionId());
            audioData.put("msgId", ttsMessage.getMsgId());
            audioData.put("audio", ttsMessage.getAudio()); // Base64编码的音频数据
            audioData.put("format", ttsMessage.getFormat()); // 音频格式：pcm, mulaw
            audioData.put("sampleRate", ttsMessage.getSampleRate()); // 采样率
            audioData.put("duration", ttsMessage.getDuration()); // 音频时长（秒）
            audioData.put("isSilenceReminder", ttsMessage.getIsSilenceReminder()); // 是否静默提醒
            audioData.put("timestamp", System.currentTimeMillis());

            // 通过SSE发送音频数据
            emitter.send(SseEmitter.event()
                    .name("tts-audio-data")
                    .data(audioData));

            log.info("📤 音频数据推送成功，客户端ID: {}, 音频长度: {} bytes, 格式: {}, 采样率: {}Hz",
                    clientId,
                    (ttsMessage.getAudio() != null ? ttsMessage.getAudio().length() : 0),
                    ttsMessage.getFormat(),
                    ttsMessage.getSampleRate());

        } catch (IOException e) {
            log.error("💥 推送音频数据失败，客户端ID: {}", clientId, e);
            // 移除失效的SSE连接
            audioStreamEmitters.remove(clientId);
            emitter.completeWithError(e);
        }
    }

    /**
     * 推送TTS打断消息到前端
     *
     * @param clientId 客户端ID
     * @param interruptMessage TTS打断消息
     */
    private void pushTTSInterruptToFrontend(String clientId, TTSInterruptMessage interruptMessage) {
        SseEmitter emitter = audioStreamEmitters.get(clientId);
        if (emitter == null) {
            log.debug("📡 没有找到客户端的SSE连接，跳过TTS打断推送，客户端ID: {}", clientId);
            return;
        }

        try {
            // 构建TTS打断消息
            Map<String, Object> interruptData = new HashMap<>();
            interruptData.put("type", "tts-interrupt");
            interruptData.put("clientId", clientId);
            interruptData.put("msgId", interruptMessage.getNewMsgId());
            interruptData.put("reason", interruptMessage.getReason());
            interruptData.put("timestamp", System.currentTimeMillis());

            // 通过SSE发送打断消息
            emitter.send(SseEmitter.event()
                    .name("tts-interrupt")
                    .data(interruptData));

            log.info("📤 TTS打断消息推送成功，客户端ID: {}, 原因: {}", clientId, interruptMessage.getReason());

        } catch (IOException e) {
            log.error("💥 推送TTS打断消息失败，客户端ID: {}", clientId, e);
            // 移除失效的SSE连接
            audioStreamEmitters.remove(clientId);
            emitter.completeWithError(e);
        }
    }

    /**
     * 推送静默超时消息到前端
     *
     * @param clientId 客户端ID
     * @param silenceMessage 静默超时消息
     */
    private void pushSilenceTimeoutToFrontend(String clientId, SilenceTimeoutMessage silenceMessage) {
        SseEmitter emitter = audioStreamEmitters.get(clientId);
        if (emitter == null) {
            log.debug("📡 没有找到客户端的SSE连接，跳过静默超时推送，客户端ID: {}", clientId);
            return;
        }

        try {
            // 构建静默超时消息
            Map<String, Object> timeoutData = new HashMap<>();
            timeoutData.put("type", "silence-timeout");
            timeoutData.put("clientId", clientId);
            timeoutData.put("message", silenceMessage.getMessage());
            timeoutData.put("timestamp", System.currentTimeMillis());

            // 通过SSE发送静默超时消息
            emitter.send(SseEmitter.event()
                    .name("silence-timeout")
                    .data(timeoutData));

            log.info("📤 静默超时消息推送成功，客户端ID: {}, 超时信息: {}", clientId, silenceMessage.getMessage());

        } catch (IOException e) {
            log.error("💥 推送静默超时消息失败，客户端ID: {}", clientId, e);
            // 移除失效的SSE连接
            audioStreamEmitters.remove(clientId);
            emitter.completeWithError(e);
        }
    }

    // ========== 数据类定义 ==========

    /**
     * 测试连接请求参数
     */
    @Data
    public static class TestConnectionRequest {
        /**
         * WebSocket服务器地址
         * 示例：ws://10.200.3.163:30765/voice-duplex/?client_id={workOrderId}
         */
        private String webSocketUrl = "ws://10.200.3.163:30765/voice-duplex/";

        /**
         * 工单ID（可选，不提供时自动生成）
         */
        private String workOrderId;

        /**
         * 智能体ID（可选，不提供时使用默认值）
         */
        private String aiAgentId;

        /**
         * 公司ID（可选，不提供时使用默认值）
         */
        private String companyId;
    }

    /**
     * 音频发送请求参数
     */
    @Data
    public static class AudioSendRequest {
        /**
         * 是否使用生成的测试音频数据
         */
        private boolean useTestData = true;

        /**
         * 自定义音频数据（Base64编码，可选）
         */
        private String audioDataBase64;

        /**
         * 音频数据大小（字节，可选）
         */
        private Integer audioDataSize;
    }

    /**
     * 音频数据请求
     */
    @Data
    public static class AudioDataRequest {
        /**
         * Base64编码的音频数据
         */
        private String audioData;
    }

    /**
     * 测试连接信息
     */
    @Data
    public static class TestConnectionInfo {
        /**
         * 客户端ID
         */
        private String clientId;

        /**
         * WebSocket服务器地址
         */
        private String webSocketUrl;

        /**
         * 连接状态
         */
        private String status;

        /**
         * 连接建立时间
         */
        private Long connectTime;

        /**
         * 连接断开时间
         */
        private Long disconnectTime;
    }
}
