package com.goclouds.crm.platform.aiagent.wsbridge2.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.goclouds.crm.platform.aiagent.wsbridge2.dto.BusinessConnectionRequest;
import com.goclouds.crm.platform.aiagent.wsbridge2.handler.WebSocketEventHandler;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.*;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket连接服务 - 构建与WebSocket操作的方法
 * 负责建立和管理WebSocket连接，处理底层WebSocket通信
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
public class WebSocketConnectionService {
    
    /**
     * 活跃的WebSocket客户端连接
     * Key: clientId, Value: WebSocketClient
     */
    private final Map<String, WebSocketClient> clients = new ConcurrentHashMap<>();
    
    /**
     * 连接请求信息
     * Key: clientId, Value: BusinessConnectionRequest
     */
    private final Map<String, BusinessConnectionRequest> requests = new ConcurrentHashMap<>();
    
    /**
     * 建立WebSocket连接 ❗️❗️❗️
     * 
     * @param webSocketUrl WebSocket服务器地址
     * @param clientId 客户端ID
     * @param eventHandler 事件处理器
     * @return 连接结果
     */
    public CompletableFuture<Boolean> connectAsync(String webSocketUrl, String clientId, WebSocketEventHandler eventHandler) {
        log.info("🚀 建立WebSocket连接, 客户端ID: {}", clientId);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 创建连接等待锁
                CountDownLatch connectionLatch = new CountDownLatch(1);
                final boolean[] connectionSuccess = {false};
                
                // 创建WebSocket客户端
                WebSocketClient client = new WebSocketClient(URI.create(webSocketUrl)) {
                    @Override
                    public void onOpen(ServerHandshake handshake) {
                        connectionSuccess[0] = true;
                        connectionLatch.countDown();
                        
                        // 触发连接建立事件
                        eventHandler.onConnected(clientId);
                    }
                    
                    @Override
                    public void onMessage(String message) {
                        // 触发消息接收事件
                        eventHandler.onMessage(message);
                    }
                    
                    @Override
                    public void onClose(int code, String reason, boolean remote) {
                        log.info("🔌 WebSocket连接已断开, 客户端ID: {}", clientId);
                        
                        // 清理资源
                        clients.remove(clientId);
                        requests.remove(clientId);
                        
                        // 触发连接断开事件
                        eventHandler.onDisconnected(clientId, code, reason);
                        
                        // 如果连接还未建立就断开，释放等待锁
                        if (!connectionSuccess[0]) {
                            connectionLatch.countDown();
                        }
                    }
                    
                    @Override
                    public void onError(Exception ex) {
                        log.error("💥 WebSocket连接发生错误, 客户端ID: {}", clientId, ex);
                        
                        // 触发错误事件
                        eventHandler.onError(clientId, ex.getMessage());
                        
                        // 如果连接还未建立就出错，释放等待锁
                        if (!connectionSuccess[0]) {
                            connectionLatch.countDown();
                        }
                    }
                };
                
                // 启动连接
                client.connect();
                
                // 等待连接建立（最多60秒）
                boolean connected = connectionLatch.await(60, TimeUnit.SECONDS);
                
                if (connected && connectionSuccess[0]) {
                    // 保存客户端连接
                    clients.put(clientId, client);
                    log.info("✅ WebSocket连接成功, 客户端ID: {}", clientId);
                    return true;
                } else {
                    log.error("❌ WebSocket连接失败, 客户端ID: {}", clientId);
                    if (client.isOpen()) {
                        client.close();
                    }
                    return false;
                }
                
            } catch (Exception e) {
                log.error("💥 建立WebSocket连接异常, 客户端ID: {}", clientId, e);
                return false;
            }
        });
    }
    
    /**
     * 发送消息到WebSocket服务器
     * 
     * @param clientId 客户端ID
     * @param message 消息对象
     * @return 发送结果
     */
    public boolean sendMessage(String clientId, Object message) {
        WebSocketClient client = clients.get(clientId);
        if (client == null || !client.isOpen()) {
            log.warn("⚠️ WebSocket连接不存在或已断开, 无法发送消息, 客户端ID: {}", clientId);
            return false;
        }
        
        try {
            String jsonMessage = JSON.toJSONString(message);
            client.send(jsonMessage);
            // 移除详细发送日志
            return true;
        } catch (Exception e) {
            log.error("💥 发送消息异常, 客户端ID: {}", clientId, e);
            return false;
        }
    }
    
    /**
     * 发送音频数据
     * 
     * @param clientId 客户端ID
     * @param audioData 音频数据（字节数组）
     * @param audioFormat 音频类型
     * @param audioSampleRate 音频码率
     * @return 发送结果
     */
    public boolean sendAudioData(String clientId, byte[] audioData, String audioFormat, String audioSampleRate) {
        try {
            // 将音频数据编码为Base64
            String base64Audio = java.util.Base64.getEncoder().encodeToString(audioData);
            
            // 创建STT消息
            STTMessage sttMessage = new STTMessage();
            sttMessage.setClientId(clientId);
            sttMessage.setAudioData(base64Audio);
            sttMessage.setEncoding(audioFormat);
            sttMessage.setSampleRate(Long.parseLong(audioSampleRate));
            
            return sendMessage(clientId, sttMessage);
        } catch (Exception e) {
            log.error("💥 发送音频数据异常, 客户端ID: {}", clientId, e);
            return false;
        }
    }
    
    /**
     * 关闭WebSocket连接
     * 
     * @param clientId 客户端ID
     */
    public void closeConnection(String clientId) {
        WebSocketClient client = clients.get(clientId);
        if (client != null) {
            try {
                log.info("🛑 关闭WebSocket连接, 客户端ID: {}", clientId);
                client.close();
            } catch (Exception e) {
                log.error("💥 关闭WebSocket连接异常, 客户端ID: {}", clientId, e);
            }
        }
        
        // 清理资源
        clients.remove(clientId);
        requests.remove(clientId);
    }
    
    /**
     * 检查连接是否活跃
     * 
     * @param clientId 客户端ID
     * @return 是否活跃
     */
    public boolean isConnectionActive(String clientId) {
        WebSocketClient client = clients.get(clientId);
        return client != null && client.isOpen();
    }
    
    /**
     * 保存连接请求信息
     * 
     * @param clientId 客户端ID
     * @param request 连接请求
     */
    public void saveConnectionRequest(String clientId, BusinessConnectionRequest request) {
        requests.put(clientId, request);
    }
    
    /**
     * 获取连接请求信息
     * 
     * @param clientId 客户端ID
     * @return 连接请求
     */
    public BusinessConnectionRequest getConnectionRequest(String clientId) {
        return requests.get(clientId);
    }
    
    /**
     * 获取所有活跃连接的客户端ID
     * 
     * @return 客户端ID集合
     */
    public java.util.Set<String> getActiveClientIds() {
        return clients.keySet();
    }
}
