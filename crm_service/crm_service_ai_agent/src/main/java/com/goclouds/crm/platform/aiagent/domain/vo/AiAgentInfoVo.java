package com.goclouds.crm.platform.aiagent.domain.vo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import java.util.List;

@Data
public class AiAgentInfoVo {
    /**
     * id
     */
    @TableId
    private String aiAgentId;

    /**
     * 智能体名称
     */
    private String aiAgentName;

    /**
     * 智能体类型，1:正常，2:欢迎语，3:fallback
     */
    private Integer aiAgentType;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 1:聊天，2:邮件，3:电话
     */
    private Integer channelTypeId;

    /**
     * 来源渠道，和咱们定义的那个序号1-20+对应的id,多个逗号分隔
     */
    private String channelIds;

    /**
     * 1:意图 2: 其他智能体 3:没有
     */
    private Integer triggerType;

    /**
     * 意图id
     */
    private String intentionId;

    /**
     * 意图名称
     */
    private String intentName;

    /**
     * 0:保存 1:部署
     */
    private Integer deployStatus;

    /**
     * 生效状态 0:未生效 1:生效
     */
    private Integer aiAgentStatus;

    /**
     *部署信息
     */
    private CrmAiAgentDeployVo crmAiAgentDeployVo;

    private JSONObject aiAgentInfo;

    /**
     * 存在已部署
     */
    private Boolean isDeploy;

    /**
     * 智能体使用次数
     */
    public Integer agentUsageCount;

    /**
     * 语音编码
     */
    private String voiceCode;
    /**
     * 语音语速
     */
    private String voiceSpeed;
    /**
     * 语音音量
     */
    private String voiceVolume;

    /**
     * 系统电话，多个使用逗号分隔
     */
    private String systemPhone;
}
