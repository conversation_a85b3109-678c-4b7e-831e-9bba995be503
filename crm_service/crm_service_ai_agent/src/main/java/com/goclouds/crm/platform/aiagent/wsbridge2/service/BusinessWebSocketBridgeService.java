package com.goclouds.crm.platform.aiagent.wsbridge2.service;

import com.goclouds.crm.platform.aiagent.wsbridge2.dto.BusinessConnectionRequest;
import com.goclouds.crm.platform.aiagent.wsbridge2.dto.ConnectionInfo;
import com.goclouds.crm.platform.aiagent.wsbridge2.callback.BusinessWebSocketCallback;
import com.goclouds.crm.platform.aiagent.wsbridge2.config.WebSocketBridgeConfig2;
import com.goclouds.crm.platform.aiagent.wsbridge2.handler.WebSocketEventHandler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * 业务WebSocket桥接服务，用于外部调用此service中的方法来构建桥。
 * 提供简化的WebSocket连接接口，隐藏底层实现细节
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
public class BusinessWebSocketBridgeService {

    @Autowired
    private WebSocketConnectionService webSocketConnectionService;

    @Autowired
    private MessageProcessingService messageProcessingService;

    @Autowired
    private WebSocketBridgeConfig2 config;

//    @Autowired
//    private BusinessDataService businessDataService;

    /**
     * 活跃连接管理
     * Key: connectionId, Value: ConnectionInfo
     */
    private final Map<String, ConnectionInfo> activeConnections = new ConcurrentHashMap<>();

    /**
     * 回调管理
     * Key: connectionId, Value: BusinessWebSocketCallback
     */
    private final Map<String, BusinessWebSocketCallback> callbackMap = new ConcurrentHashMap<>();

    /**
     * 建立业务WebSocket连接 ❗️❗️❗️这里
     *
     * @param request  业务连接请求参数
     * @param callback 业务回调接口
     * @return 连接结果
     */
    public CompletableFuture<Boolean> connectAsync(BusinessConnectionRequest request, BusinessWebSocketCallback callback) {
        // 参数验证
        if (request == null || !request.isValid()) {
            log.error("🚫 业务连接请求参数无效: {}", request != null ? request.toLogString() : "null");
            return CompletableFuture.completedFuture(false);
        }

        if (callback == null) {
            log.error("🚫 业务回调接口不能为空, 连接请求: {}", request.toLogString());
            return CompletableFuture.completedFuture(false);
        }

        String connectionId = request.getConnectionId(); // 工单ID

        // 检查是否已存在连接
        if (activeConnections.containsKey(connectionId)) {
            log.warn("⚠️ 连接已存在，先断开旧连接, 连接ID: {}", connectionId);
            disconnectAsync(connectionId, "重新连接断开原连接");
        }

        // 创建连接信息
        ConnectionInfo connectionInfo = ConnectionInfo.builder()
                .connectionId(connectionId)
                .request(request)
                .status("CONNECTING")
                .connectTime(LocalDateTime.now())
                .reconnectCount(0)
                .sentMessageCount(0L)
                .receivedMessageCount(0L)
                .build();

        activeConnections.put(connectionId, connectionInfo);
        log.info("activeConnections中添加一个connectionId：{}", connectionId);
        callbackMap.put(connectionId, callback);

        // 异步建立连接
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 构建WebSocket URL
                String webSocketUrl = config.getFullWebSocketUrl() + "?client_id=" + connectionId;

                // 创建WebSocket事件处理器
                WebSocketEventHandler eventHandler = createWebSocketEventHandler(request, callback);

                // 保存连接请求信息
                webSocketConnectionService.saveConnectionRequest(connectionId, request);

                // 建立WebSocket连接
                CompletableFuture<Boolean> connectFuture = webSocketConnectionService.connectAsync(webSocketUrl, connectionId, eventHandler);
                boolean success = connectFuture.get(); // 等待连接完成

                if (success) {
                    // 更新连接状态
                    connectionInfo.setStatus("CONNECTING");  // 设置为连接中，等待init消息
                    connectionInfo.updateLastActiveTime();
                    // 注意：不在这里触发业务回调，等待init消息处理完成后才触发
                } else {
                    // 连接失败，清理资源
                    connectionInfo.setStatus("ERROR");
                    connectionInfo.setErrorMessage("WebSocket连接失败");
                    activeConnections.remove(connectionId);
                    log.info("activeConnections中删除一个connectionId：{}", connectionId);
                    callbackMap.remove(connectionId);
                    log.error("❌ WebSocket连接失败, 连接ID: {}", connectionId);

                    // 触发错误回调
                    callback.onError(request, connectionId, "WebSocket连接失败");
                }

                return success;

            } catch (Exception e) {
                // 异常处理
                connectionInfo.setStatus("ERROR");
                connectionInfo.setErrorMessage(e.getMessage());
                activeConnections.remove(connectionId);
                callbackMap.remove(connectionId);
                log.error("💥 业务WebSocket连接建立异常, 连接ID: {}", connectionId, e);

                // 触发错误回调
                callback.onError(request, connectionId, "连接建立异常: " + e.getMessage());
                return false;
            }
        });
    }

    /**
     * 断开业务WebSocket连接
     *
     * @param connectionId 连接ID
     * @return 断开结果
     */
    public CompletableFuture<Boolean> disconnectAsync(String connectionId, String disconnectReason) {
        log.info("🔌 开始断开业务WebSocket连接, 连接ID: {}", connectionId);

        ConnectionInfo connectionInfo = activeConnections.get(connectionId);
        if (connectionInfo == null) {
            log.warn("⚠️ 连接不存在，无需断开, 连接ID: {}", connectionId);
            return CompletableFuture.completedFuture(true);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 发送断开消息
                CompletableFuture<Boolean> future = sendCloseAsync(connectionId, disconnectReason);
                Boolean closeResult = future.get();
                log.info("发送断开消息结果：{}", closeResult);

                // 断开WebSocket连接
                webSocketConnectionService.closeConnection(connectionId);
                boolean success = true;

                // 更新连接状态
                connectionInfo.setStatus("DISCONNECTED");
                connectionInfo.setDisconnectTime(LocalDateTime.now());
                connectionInfo.setDisconnectReason(disconnectReason);

                // 触发断开回调
                BusinessWebSocketCallback callback = callbackMap.get(connectionId);
                if (callback != null) {
                    callback.onDisconnected(connectionInfo.getRequest(), connectionId, disconnectReason);
                }

                // 清理资源
                activeConnections.remove(connectionId);
                callbackMap.remove(connectionId);

                log.info("✅ 业务WebSocket连接断开成功, 连接ID: {}, 连接时长: {}秒", connectionId, connectionInfo.getConnectionDurationSeconds());

                return success;

            } catch (Exception e) {
                log.error("💥 业务WebSocket连接断开异常, 连接ID: {}", connectionId, e);

                // 强制清理资源
                activeConnections.remove(connectionId);
                callbackMap.remove(connectionId);

                return false;
            }
        });
    }

    /**
     * 发送音频数据
     *
     * @param connectionId   连接ID
     * @param audioData      音频数据（Base64编码）
     * @param audioFormat    音频格式
     * @param audioSampleRte 音频码率
     * @return 发送结果
     */
    public CompletableFuture<Boolean> sendAudioAsync(String connectionId, String audioData, String audioFormat, String audioSampleRte) {
        ConnectionInfo connectionInfo = activeConnections.get(connectionId);

        if (connectionInfo == null || !connectionInfo.isActive()) {
            log.warn("⚠️ 连接不存在或未激活，无法发送音频, 连接ID: {}", connectionId);
            return CompletableFuture.completedFuture(false);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 将Base64音频数据转换为字节数组
                byte[] audioBytes = java.util.Base64.getDecoder().decode(audioData);

                // 发送音频数据到WebSocket
                boolean success = webSocketConnectionService.sendAudioData(connectionId, audioBytes, audioFormat, audioSampleRte);

                if (success) {
                    // 更新统计信息
                    connectionInfo.incrementSentMessageCount();
                    connectionInfo.updateLastActiveTime();
                    log.debug("📤 音频数据发送成功, 连接ID: {}, 数据长度: {}", connectionId, audioData.length());
                } else {
                    log.warn("⚠️ 音频数据发送失败, 连接ID: {}", connectionId);
                }

                return success;

            } catch (Exception e) {
                log.error("💥 音频数据发送异常, 连接ID: {}", connectionId, e);
                return false;
            }
        });
    }

    /**
     * 发送关闭消息
     *
     * @param connectionId 连接ID
     * @param reason       原因
     */
    public CompletableFuture<Boolean> sendCloseAsync(String connectionId, String reason) {
        ConnectionInfo connectionInfo = activeConnections.get(connectionId);

        if (connectionInfo == null || !connectionInfo.isActive()) {
            log.warn("⚠️ 连接不存在或未激活，无法发送关闭消息, 连接ID: {}", connectionId);
            return CompletableFuture.completedFuture(false);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 发送关闭数据到WebSocket
                boolean success = webSocketConnectionService.sendCloseData(connectionId, reason);

                if (success) {
                    log.debug("📤 关闭连接发送成功, 连接ID: {}, 原因: {}", connectionId, reason);

                } else {
                    log.warn("⚠️ 关闭连接发送失败, 连接ID: {}", connectionId);
                }

                return success;

            } catch (Exception e) {
                log.error("💥 关闭连接发送异常, 连接ID: {}", connectionId, e);
                return false;
            }
        });
    }


    /**
     * 获取连接信息
     *
     * @param connectionId 连接ID
     * @return 连接信息
     */
    public ConnectionInfo getConnectionInfo(String connectionId) {
        return activeConnections.get(connectionId);
    }

    /**
     * 获取所有活跃连接
     *
     * @return 活跃连接映射
     */
    public Map<String, ConnectionInfo> getAllActiveConnections() {
        return new ConcurrentHashMap<>(activeConnections);
    }

    /**
     * 检查连接是否活跃
     *
     * @param connectionId 连接ID
     * @return 是否活跃
     */
    public boolean isConnectionActive(String connectionId) {
        ConnectionInfo connectionInfo = activeConnections.get(connectionId);
        boolean infoActive = connectionInfo != null && connectionInfo.isActive();
        boolean wsActive = webSocketConnectionService.isConnectionActive(connectionId);
        return infoActive && wsActive;
    }

    /**
     * 更新连接状态
     *
     * @param connectionId 连接ID
     * @param status       新状态
     */
    public void updateConnectionStatus(String connectionId, String status) {
        ConnectionInfo connectionInfo = activeConnections.get(connectionId);
        if (connectionInfo != null) {
            connectionInfo.setStatus(status);
            connectionInfo.updateLastActiveTime();
            log.info("🔄 连接状态已更新, 连接ID: {}, 状态: {}", connectionId, status);
        } else {
            log.warn("⚠️ 连接不存在，无法更新状态, 连接ID: {}", connectionId);
        }
    }

    /**
     * 创建WebSocket事件处理器
     *
     * @param request  业务连接请求
     * @param callback 业务回调
     * @return WebSocket事件处理器
     */
    private WebSocketEventHandler createWebSocketEventHandler(BusinessConnectionRequest request, BusinessWebSocketCallback callback) {
        String connectionId = request.getConnectionId();

        return new WebSocketEventHandler() {
            @Override
            public void onConnected(String clientId) {
                ConnectionInfo connectionInfo = activeConnections.get(connectionId);
                if (connectionInfo != null) {
                    connectionInfo.setWebSocketSessionId(clientId);
                    connectionInfo.updateLastActiveTime();
                }
                // 注意：这里不触发业务回调，等待init消息处理完成后才触发
            }

            @Override
            public void onMessage(String message) {
                ConnectionInfo connectionInfo = activeConnections.get(connectionId);
                if (connectionInfo != null) {
                    connectionInfo.incrementReceivedMessageCount();
                    connectionInfo.updateLastActiveTime();
                }

                // 委托给消息处理服务，传入状态更新回调
                messageProcessingService.processMessage(connectionId, message, request, callback,
                        (connId, status) -> updateConnectionStatus(connId, status));
            }

            @Override
            public void onDisconnected(String clientId, int code, String reason) {
                log.info("🔌 WebSocket连接已断开, 连接ID: {}, 客户端ID: {}, 代码: {}, 原因: {}", connectionId, clientId, code, reason);

                ConnectionInfo connectionInfo = activeConnections.get(connectionId);
                if (connectionInfo != null) {
                    connectionInfo.setStatus("DISCONNECTED");
                    connectionInfo.setDisconnectTime(LocalDateTime.now());
                    connectionInfo.setDisconnectReason(reason);
                }

                // 触发业务回调
                callback.onDisconnected(request, connectionId, reason);

                // 清理资源
                activeConnections.remove(connectionId);
                callbackMap.remove(connectionId);
            }

            @Override
            public void onError(String clientId, String error) {
                log.error("💥 WebSocket连接错误, 连接ID: {}, 客户端ID: {}, 错误: {}", connectionId, clientId, error);

                ConnectionInfo connectionInfo = activeConnections.get(connectionId);
                if (connectionInfo != null) {
                    connectionInfo.setStatus("ERROR");
                    connectionInfo.setErrorMessage(error);
                }

                // 触发业务回调
                callback.onError(request, connectionId, error);
            }
        };
    }

}
