package com.goclouds.crm.platform.aiagent.wsbridge2.controller;

import com.goclouds.crm.platform.aiagent.wsbridge2.dto.BusinessConnectionRequest;
import com.goclouds.crm.platform.aiagent.wsbridge2.dto.ConnectionInfo;
import com.goclouds.crm.platform.aiagent.wsbridge2.callback.BusinessWebSocketCallback;
import com.goclouds.crm.platform.aiagent.wsbridge2.service.BusinessWebSocketBridgeService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.annotation.NoLogin;

import lombok.extern.slf4j.Slf4j;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.http.MediaType;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.HashMap;
import java.io.IOException;

/**
 * WebSocket Bridge 2.0 测试控制器
 * 提供完整的WebSocket连接测试功能和前端页面
 * 
 * 功能说明：
 * 1. 提供HTTP接口来测试WebSocket桥接服务
 * 2. 演示如何使用BusinessWebSocketBridgeService
 * 3. 方便本地开发和调试
 * 4. 提供完整的WebSocket连接测试功能
 * 5. 支持SSE实时推送TTS音频数据
 * 
 * API接口：
 * 1. POST /wsbridge2/test/connect - 创建WebSocket连接
 * 2. POST /wsbridge2/test/send-audio/{clientId} - 发送音频数据
 * 3. POST /wsbridge2/test/disconnect/{clientId} - 断开连接
 * 4. GET /wsbridge2/test/status/{clientId} - 查看连接状态
 * 5. GET /wsbridge2/test/audio-stream/{clientId} - SSE音频流
 * 
 * <AUTHOR> Agent
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/wsbridge2/test")
public class WebSocketBridge2TestController {
    
    @Autowired
    private BusinessWebSocketBridgeService businessWebSocketBridgeService;
    
    /**
     * SSE音频流发射器管理
     * Key: clientId, Value: SseEmitter
     */
    private final Map<String, SseEmitter> audioStreamEmitters = new ConcurrentHashMap<>();
    
    /**
     * 活跃连接信息管理
     * Key: clientId, Value: TestConnectionInfo
     */
    private final Map<String, TestConnectionInfo> activeTestConnections = new ConcurrentHashMap<>();
    
    /**
     * 建立WebSocket连接（测试接口）
     * 
     * @param request 测试连接请求参数
     * @return 连接结果
     */
    @NoLogin
    @PostMapping("/connect")
    public AjaxResult<?> connect(@RequestBody TestConnectionRequest request) {
        log.info("🚀 WebSocket连接请求: {}", request);
        
        try {
            // 参数验证
            if (request.getWorkOrderId() == null || request.getWorkOrderId().trim().isEmpty()) {
                log.error("🚫 工单ID不能为空");
                return AjaxResult.failure("工单ID不能为空");
            }
            
            String clientId = request.getWorkOrderId();
            
            // 检查是否已存在连接
            if (activeTestConnections.containsKey(clientId)) {
                log.warn("⚠️ 连接已存在，先断开旧连接, 客户端ID: {}", clientId);
                disconnectInternal(clientId, "连接已存在，先断开旧连接");
            }
            
            // 构建业务连接请求
            BusinessConnectionRequest businessRequest = BusinessConnectionRequest.builder()
                    .workOrderId(request.getWorkOrderId())
                    .aiAgentId(request.getAiAgentId() != null ? request.getAiAgentId() : "default_agent")
                    .companyId(request.getCompanyId() != null ? request.getCompanyId() : "default_company")
                    .channelId(request.getChannelId())
                    .channelTypeId(request.getChannelTypeId())
                    .sttLanguage("zh-CN")
                    .audioFormat("pcm")
                    .audioSampleRate(16000)
                    .userId(request.getUserId())
                    .businessType("WebSocket Bridge 2.0 测试连接")
                    .remark("WebSocket Bridge 2.0 测试连接")
                    .build();
            
            // 创建测试连接信息
            TestConnectionInfo testConnectionInfo = new TestConnectionInfo();
            testConnectionInfo.setClientId(clientId);
            testConnectionInfo.setStatus("CONNECTING");
            testConnectionInfo.setConnectTime(System.currentTimeMillis());
            testConnectionInfo.setRequest(request);
            activeTestConnections.put(clientId, testConnectionInfo);
            
            // 创建业务回调实现
            BusinessWebSocketCallback callback = createTestCallback(clientId, testConnectionInfo);
            
            // 异步建立连接
            CompletableFuture<Boolean> future = businessWebSocketBridgeService.connectAsync(businessRequest, callback);
            Boolean success = future.get(); // 等待连接完成
            
            if (success) {
                testConnectionInfo.setStatus("CONNECTED");
                log.info("✅返回给前端 WebSocket连接成功, 客户端ID: {}", clientId);
                return AjaxResult.ok(clientId, "连接成功" );
            } else {
                testConnectionInfo.setStatus("FAILED");
                activeTestConnections.remove(clientId);
                log.error("❌返回给前端 WebSocket连接失败, 客户端ID: {}", clientId);
                return AjaxResult.failure("连接失败");
            }
            
        } catch (Exception e) {
            log.error("💥 WebSocket连接建立异常", e);
            return AjaxResult.failure("连接建立异常: " + e.getMessage());
        }
    }
    
    /**
     * 发送音频数据（测试接口）
     * 
     * @param clientId 客户端ID
     * @param audioRequest 音频数据请求
     * @return 发送结果
     */
    @NoLogin
    @PostMapping("/send-audio/{clientId}")
    public AjaxResult<?> sendAudio(@PathVariable String clientId, @RequestBody AudioDataRequest audioRequest) {
        log.debug("📤 收到音频数据发送请求, 客户端ID: {}", clientId);
        
        try {
            TestConnectionInfo testConnectionInfo = activeTestConnections.get(clientId);
            if (testConnectionInfo == null) {
                return AjaxResult.failure("连接不存在");
            }
            
            if (!"CONNECTED".equals(testConnectionInfo.getStatus())) {
                return AjaxResult.failure("连接未建立");
            }
            
            String audioData = audioRequest.getAudioData();
            if (audioData == null || audioData.trim().isEmpty()) {
                return AjaxResult.failure("音频数据不能为空");
            }
            
            CompletableFuture<Boolean> future = businessWebSocketBridgeService.sendAudioAsync(clientId, audioData);
            Boolean success = future.get(); // 等待发送完成
            
            if (success) {
                testConnectionInfo.incrementSentCount();
                log.debug("✅ 音频数据发送成功, 客户端ID: {}", clientId);
                return AjaxResult.ok("音频数据发送成功");
            } else {
                log.warn("❌ 音频数据发送失败, 客户端ID: {}", clientId);
                return AjaxResult.failure("音频数据发送失败");
            }
        } catch (Exception e) {
            log.error("💥 音频数据发送异常, 客户端ID: {}", clientId, e);
            return AjaxResult.failure("音频数据发送异常: " + e.getMessage());
        }
    }
    
    /**
     * 断开WebSocket连接（测试接口）
     * 
     * @param clientId 客户端ID
     * @return 断开结果
     */
    @NoLogin
    @PostMapping("/disconnect/{clientId}")
    public AjaxResult<?> disconnect(@PathVariable String clientId) {
        log.info("🔌 收到WebSocket断开请求, 客户端ID: {}", clientId);
        
        try {
            boolean success = disconnectInternal(clientId, "主动断开");
            
            if (success) {
                log.info("✅ WebSocket连接断开成功, 客户端ID: {}", clientId);
                return AjaxResult.ok("连接断开成功");
            } else {
                log.error("❌ WebSocket连接断开失败, 客户端ID: {}", clientId);
                return AjaxResult.failure("连接断开失败");
            }
        } catch (Exception e) {
            log.error("💥 WebSocket连接断开异常, 客户端ID: {}", clientId, e);
            return AjaxResult.failure("连接断开异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取连接状态（测试接口）
     * 
     * @param clientId 客户端ID
     * @return 连接状态
     */
    @NoLogin
    @GetMapping("/status/{clientId}")
    public AjaxResult<?> getStatus(@PathVariable String clientId) {
        log.debug("🔍 获取连接状态, 客户端ID: {}", clientId);
        
        try {
            TestConnectionInfo testConnectionInfo = activeTestConnections.get(clientId);
            if (testConnectionInfo == null) {
                return AjaxResult.failure("连接不存在");
            }
            
            // 检查底层连接状态
            boolean isActive = businessWebSocketBridgeService.isConnectionActive(clientId);
            if (!isActive && "CONNECTED".equals(testConnectionInfo.getStatus())) {
                testConnectionInfo.setStatus("DISCONNECTED");
                testConnectionInfo.setDisconnectTime(System.currentTimeMillis());
            }
            
            return AjaxResult.ok(testConnectionInfo,"获取状态成功");
        } catch (Exception e) {
            log.error("💥 获取连接状态异常, 客户端ID: {}", clientId, e);
            return AjaxResult.failure("获取状态异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有连接状态（测试接口）
     * 
     * @return 所有连接状态
     */
    @NoLogin
    @GetMapping("/connections")
    public AjaxResult<?> getAllConnections() {
        log.debug("🔍 获取所有连接状态");
        
        try {
            return AjaxResult.ok(activeTestConnections, "获取连接列表成功");
        } catch (Exception e) {
            log.error("💥 获取连接列表异常", e);
            return AjaxResult.failure("获取连接列表异常: " + e.getMessage());
        }
    }
    
    /**
     * SSE音频流接口
     * 
     * @param clientId 客户端ID
     * @return SSE发射器
     */
    @NoLogin
    @GetMapping(value = "/audio-stream/{clientId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter audioStream(@PathVariable String clientId) {
        log.info("📡 建立SSE音频流连接, 客户端ID: {}", clientId);
        
        SseEmitter emitter = new SseEmitter(0L); // 无超时
        audioStreamEmitters.put(clientId, emitter);
        
        // 设置完成和超时回调
        emitter.onCompletion(() -> {
            log.info("📡 SSE音频流连接完成, 客户端ID: {}", clientId);
            audioStreamEmitters.remove(clientId);
        });
        
        emitter.onTimeout(() -> {
            log.warn("📡 SSE音频流连接超时, 客户端ID: {}", clientId);
            audioStreamEmitters.remove(clientId);
        });
        
        emitter.onError((ex) -> {
            log.error("📡 SSE音频流连接错误, 客户端ID: {}", clientId, ex);
            audioStreamEmitters.remove(clientId);
        });
        
        try {
            // 发送连接成功消息
            emitter.send(SseEmitter.event()
                    .name("connected")
                    .data("SSE音频流连接已建立"));
        } catch (IOException e) {
            log.error("💥 发送SSE连接消息失败, 客户端ID: {}", clientId, e);
        }
        
        return emitter;
    }

    // ========== 私有方法 ==========

    /**
     * 创建测试回调实现
     *
     * @param clientId 客户端ID
     * @param testConnectionInfo 测试连接信息
     * @return 业务回调
     */
    private BusinessWebSocketCallback createTestCallback(String clientId, TestConnectionInfo testConnectionInfo) {
        return new BusinessWebSocketCallback() {
            @Override
            public void onConnected(BusinessConnectionRequest req, String connectionId) {
                log.info("✅done WebSocket连接已建立, 客户端ID: {}", connectionId);
                testConnectionInfo.setStatus("CONNECTED");
                testConnectionInfo.setConnectTime(System.currentTimeMillis());

                // 推送连接成功消息到SSE
                pushToSSE(clientId, "connected", "WebSocket连接已建立");
            }

            @Override
            public void onDisconnected(BusinessConnectionRequest req, String connectionId, String reason) {
                log.info("🔌 WebSocket连接已断开, 客户端ID: {}", connectionId);
                testConnectionInfo.setStatus("DISCONNECTED");
                testConnectionInfo.setDisconnectTime(System.currentTimeMillis());
                testConnectionInfo.setDisconnectReason(reason);

                // 推送断开消息到SSE
                pushToSSE(clientId, "disconnected", "WebSocket连接已断开: " + reason);

                // 清理资源
                activeTestConnections.remove(clientId);
                audioStreamEmitters.remove(clientId);
            }

            @Override
            public void onError(BusinessConnectionRequest req, String connectionId, String error) {
                log.error("💥 WebSocket连接错误, 客户端ID: {}, 错误: {}", connectionId, error);
                testConnectionInfo.setStatus("ERROR");
                testConnectionInfo.setErrorMessage(error);

                // 推送错误消息到SSE
                pushToSSE(clientId, "error", "WebSocket连接错误: " + error);


            }

            @Override
            public void onTTSAudio(BusinessConnectionRequest req, String connectionId,
                                  String audioData, String format, Integer sampleRate, Double duration) {
                log.info("🔊 收到TTS音频, 时长: {}秒", duration);

                testConnectionInfo.incrementReceivedCount();

                // 推送TTS音频数据到SSE
                pushTTSAudioToSSE(clientId, audioData, format, sampleRate, duration);
            }

            @Override
            public void onTTSInterrupt(BusinessConnectionRequest req, String connectionId, String reason) {
                log.info("⏹️ TTS播放被打断: {}", reason);

                // 推送TTS打断消息到SSE
                pushToSSE(clientId, "tts-interrupt", "TTS播放被打断: " + reason);
            }

            @Override
            public void onSilenceTimeout(BusinessConnectionRequest req, String connectionId,
                                        String message) {
                log.info("🔇 用户静默超时: {}", message);

                // 推送静默超时消息到SSE
                Map<String, Object> timeoutData = new HashMap<>();
                timeoutData.put("message", message);
                timeoutData.put("timestamp", System.currentTimeMillis());
                pushToSSE(clientId, "silence-timeout", timeoutData);
            }

            @Override
            public void onASRResult(BusinessConnectionRequest req, String connectionId,
                                   String text, Boolean isFinal) {
                log.info("🎤 ASR识别: \"{}\" ({})", text, isFinal ? "最终" : "中间");

                // 推送ASR结果到SSE
                Map<String, Object> asrData = new HashMap<>();
                asrData.put("text", text);
                asrData.put("isFinal", isFinal);
                asrData.put("timestamp", System.currentTimeMillis());
                pushToSSE(clientId, "asr-result", asrData);
            }
        };
    }

    /**
     * 内部断开连接方法
     *
     * @param clientId 客户端ID
     * @return 断开结果
     */
    private boolean disconnectInternal(String clientId, String disconnectReason) {
        try {
            CompletableFuture<Boolean> future = businessWebSocketBridgeService.disconnectAsync(clientId, disconnectReason);
            boolean success = future.get(); // 等待断开完成

            // 更新测试连接信息
            TestConnectionInfo testConnectionInfo = activeTestConnections.get(clientId);
            if (testConnectionInfo != null) {
                testConnectionInfo.setStatus("DISCONNECTED");
                testConnectionInfo.setDisconnectTime(System.currentTimeMillis());
                testConnectionInfo.setDisconnectReason(disconnectReason);
            }

            // 清理SSE连接
            SseEmitter emitter = audioStreamEmitters.remove(clientId);
            if (emitter != null) {
                try {
                    emitter.send(SseEmitter.event()
                            .name("disconnected")
                            .data("连接已断开"));
                    emitter.complete();
                } catch (IOException e) {
                    log.warn("关闭SSE连接时发生异常", e);
                }
            }

            // 清理测试连接信息
            activeTestConnections.remove(clientId);

            return success;
        } catch (Exception e) {
            log.error("断开连接异常, 客户端ID: {}", clientId, e);
            return false;
        }
    }

    /**
     * 推送消息到SSE
     *
     * @param clientId 客户端ID
     * @param eventName 事件名称
     * @param data 数据
     */
    private void pushToSSE(String clientId, String eventName, Object data) {
        SseEmitter emitter = audioStreamEmitters.get(clientId);
        if (emitter == null) {
            log.debug("📡 没有找到客户端的SSE连接，跳过推送, 客户端ID: {}", clientId);
            return;
        }

        try {
            emitter.send(SseEmitter.event()
                    .name(eventName)
                    .data(data));
            log.debug("📤 SSE消息推送成功, 客户端ID: {}, 事件: {}", clientId, eventName);
        } catch (IOException e) {
            log.error("💥 SSE消息推送失败, 客户端ID: {}", clientId, e);
            // 移除失效的SSE连接
            audioStreamEmitters.remove(clientId);
            emitter.completeWithError(e);
        }
    }

    /**
     * 推送TTS音频数据到SSE
     *
     * @param clientId 客户端ID
     * @param audioData 音频数据
     * @param format 音频格式
     * @param sampleRate 采样率
     * @param duration 时长
     */
    private void pushTTSAudioToSSE(String clientId, String audioData, String format, Integer sampleRate, Double duration) {
        Map<String, Object> audioInfo = new HashMap<>();
        audioInfo.put("audioData", audioData);
        audioInfo.put("format", format);
        audioInfo.put("sampleRate", sampleRate);
        audioInfo.put("duration", duration);
        audioInfo.put("timestamp", System.currentTimeMillis());

        pushToSSE(clientId, "tts-audio", audioInfo);
    }

    /**
     * 测试页面访问
     *
     * @return 测试页面
     */
    @NoLogin
    @GetMapping("/page")
    public String testPage() {
        return "redirect:/websocket-bridge2-test.html";
    }

    // ========== 数据类定义 ==========

    /**
     * 测试连接请求
     */
    @Data
    public static class TestConnectionRequest {
        private String workOrderId;      // 工单ID（必填）
        private String aiAgentId;        // 智能体ID
        private String companyId;        // 公司ID
        private String channelId;        // 渠道ID
        private String channelTypeId;    // 渠道类型ID
        private String userId;           // 用户ID
        private String sessionId;        // 会话ID
        private String remark;           // 备注
        private String language;           // stt语言
        private String audioFormat;           // 音频格式
        private String audioSampleRate;           // 音频码率
    }

    /**
     * 音频数据请求
     */
    @Data
    public static class AudioDataRequest {
        private String audioData;        // Base64编码的音频数据
        private String format;           // 音频格式（可选）
        private Integer sampleRate;      // 采样率（可选）
    }

    /**
     * 测试连接信息
     */
    @Data
    public static class TestConnectionInfo {
        private String clientId;         // 客户端ID
        private String status;           // 连接状态
        private Long connectTime;        // 连接时间
        private Long disconnectTime;     // 断开时间
        private String disconnectReason; // 断开原因
        private String errorMessage;     // 错误信息
        private TestConnectionRequest request; // 原始请求
        private Long sentCount = 0L;     // 发送消息数
        private Long receivedCount = 0L; // 接收消息数

        /**
         * 增加发送计数
         */
        public void incrementSentCount() {
            this.sentCount++;
        }

        /**
         * 增加接收计数
         */
        public void incrementReceivedCount() {
            this.receivedCount++;
        }

        /**
         * 获取连接持续时间（毫秒）
         */
        public Long getConnectionDuration() {
            if (connectTime == null) {
                return 0L;
            }
            Long endTime = disconnectTime != null ? disconnectTime : System.currentTimeMillis();
            return endTime - connectTime;
        }
    }
}
