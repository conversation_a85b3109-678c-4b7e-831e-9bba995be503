package com.goclouds.crm.platform.aiagent.wsbridge2.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 关闭连接消息
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CloseMessage extends BaseMessage {

    /**
     * 关闭原因
     */
    @JSONField(name = "reason")
    private String reason;

    public CloseMessage() {
        super("close", null);
    }

    public CloseMessage(String clientId, String reason) {
        super("close", clientId);
        this.reason = reason;
    }
}
