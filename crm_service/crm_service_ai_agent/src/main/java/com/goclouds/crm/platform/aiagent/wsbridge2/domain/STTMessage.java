package com.goclouds.crm.platform.aiagent.wsbridge2.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 语音识别消息
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class STTMessage extends BaseMessage {

    /**
     * Base64编码的音频数据
     */
    @JSONField(name = "audio_data")
    private String audioData;
    @JSONField(name = "encoding")
    private String encoding;
    @JSONField(name = "sample_rate")
    private Long sampleRate;


    public STTMessage() {
        super("stt_audio", null);
    }

    public STTMessage(String clientId, String audioData) {
        super("stt_audio", clientId);
        this.audioData = audioData;
    }
}
