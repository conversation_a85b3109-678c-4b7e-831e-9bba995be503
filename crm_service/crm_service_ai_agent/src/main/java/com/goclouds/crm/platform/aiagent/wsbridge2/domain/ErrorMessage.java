package com.goclouds.crm.platform.aiagent.wsbridge2.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 错误消息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ErrorMessage extends BaseMessage {

    /**
     * 错误类型
     */
    @JSONField(name = "error_type")
    private String errorType;

    public ErrorMessage() {
        super("error", null);
    }

    public ErrorMessage(String clientId, String message, String errorType) {
        super("error", clientId);
        setMessage(message);
        setStatus("error");
        this.errorType = errorType;
    }
}
