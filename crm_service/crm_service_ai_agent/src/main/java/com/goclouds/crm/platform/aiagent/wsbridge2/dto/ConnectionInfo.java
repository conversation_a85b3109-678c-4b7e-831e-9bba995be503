package com.goclouds.crm.platform.aiagent.wsbridge2.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 连接信息类
 * 记录WebSocket连接的详细信息和状态
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConnectionInfo {
    
    /**
     * 连接ID
     */
    private String connectionId;
    
    /**
     * 业务连接请求参数
     */
    private BusinessConnectionRequest request;
    
    /**
     * 连接状态
     * CONNECTING - 连接中
     * CONNECTED - 已连接
     * DISCONNECTED - 已断开
     * ERROR - 错误状态
     * TIMEOUT - 超时断开
     */
    private String status;
    
    /**
     * WebSocket会话ID
     */
    private String webSocketSessionId;
    
    /**
     * 连接建立时间
     */
    private LocalDateTime connectTime;
    
    /**
     * 连接断开时间
     */
    private LocalDateTime disconnectTime;
    
    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;
    
    /**
     * 重连次数
     */
    private Integer reconnectCount;
    
    /**
     * 发送消息数量
     */
    private Long sentMessageCount;
    
    /**
     * 接收消息数量
     */
    private Long receivedMessageCount;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 断开原因
     */
    private String disconnectReason;
    
    /**
     * 扩展信息
     */
    private String extInfo;
    
    /**
     * 检查连接是否活跃
     * 
     * @return 是否活跃
     */
    public boolean isActive() {
        return "CONNECTED".equals(status);
    }
    
    /**
     * 检查连接是否已断开
     * 
     * @return 是否已断开
     */
    public boolean isDisconnected() {
        return "DISCONNECTED".equals(status) || "ERROR".equals(status) || "TIMEOUT".equals(status);
    }
    
    /**
     * 更新最后活跃时间
     */
    public void updateLastActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
    }
    
    /**
     * 增加发送消息计数
     */
    public void incrementSentMessageCount() {
        if (this.sentMessageCount == null) {
            this.sentMessageCount = 0L;
        }
        this.sentMessageCount++;
    }
    
    /**
     * 增加接收消息计数
     */
    public void incrementReceivedMessageCount() {
        if (this.receivedMessageCount == null) {
            this.receivedMessageCount = 0L;
        }
        this.receivedMessageCount++;
    }
    
    /**
     * 增加重连计数
     */
    public void incrementReconnectCount() {
        if (this.reconnectCount == null) {
            this.reconnectCount = 0;
        }
        this.reconnectCount++;
    }
    
    /**
     * 获取连接持续时间（秒）
     * 
     * @return 连接持续时间
     */
    public long getConnectionDurationSeconds() {
        if (connectTime == null) {
            return 0;
        }
        
        LocalDateTime endTime = disconnectTime != null ? disconnectTime : LocalDateTime.now();
        return java.time.Duration.between(connectTime, endTime).getSeconds();
    }
    
    /**
     * 转换为日志字符串
     * 
     * @return 日志字符串
     */
    public String toLogString() {
        return String.format("ConnectionInfo{connectionId='%s', status='%s', workOrderId='%s', connectTime=%s, duration=%ds}",
                connectionId, status, 
                request != null ? request.getWorkOrderId() : "unknown",
                connectTime, getConnectionDurationSeconds());
    }
}
