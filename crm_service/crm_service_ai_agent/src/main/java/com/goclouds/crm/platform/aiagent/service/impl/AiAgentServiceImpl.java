package com.goclouds.crm.platform.aiagent.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.aiagent.domain.*;
import com.goclouds.crm.platform.aiagent.domain.vo.*;
import com.goclouds.crm.platform.aiagent.domain.vo.component.AiAgentResult;
import com.goclouds.crm.platform.aiagent.flow.AiAgent;
import com.goclouds.crm.platform.aiagent.flow.FlowNode;
import com.goclouds.crm.platform.aiagent.flow.RunContext;
import com.goclouds.crm.platform.aiagent.flow.SessionAiAgent;
import com.goclouds.crm.platform.aiagent.mapper.AiAgentIntentMapper;
import com.goclouds.crm.platform.aiagent.mapper.CrmAiAgentDeployMapper;
import com.goclouds.crm.platform.aiagent.mapper.CrmAiAgentInfoMapper;
import com.goclouds.crm.platform.aiagent.mapper.CrmAiAgentVariableMapper;
import com.goclouds.crm.platform.aiagent.service.*;
import com.goclouds.crm.platform.aiagent.utils.S3Utils;
import com.goclouds.crm.platform.aiagent.utils.UtilsFunc;
import com.goclouds.crm.platform.aiagent.utils.InvokeServiceUtil;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.constant.RabbitMqConstants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.AiAgentChannelTypeEnum;
import com.goclouds.crm.platform.common.enums.ChargeItemServiceEnum;
import com.goclouds.crm.platform.common.utils.RedisCacheUtil;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.channel.ChannelClient;
import com.goclouds.crm.platform.openfeignClient.client.customer.CustomerClient;
import com.goclouds.crm.platform.openfeignClient.client.platform.S3Client;
import com.goclouds.crm.platform.openfeignClient.client.rescmgnt.RescmgntClient;
import com.goclouds.crm.platform.openfeignClient.client.system.CompanyClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.agent.CustomerInfoForAiAgent;
import com.goclouds.crm.platform.openfeignClient.domain.agent.CustomerInfoForTagsByCustomerId;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmChannelConfigVO;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.S3GetObjectParam;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.S3PutObjectParam;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.trans.TransUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;


@Service
@RequiredArgsConstructor
@Slf4j
public class AiAgentServiceImpl extends ServiceImpl<CrmAiAgentInfoMapper, CrmAiAgentInfo> implements AiAgentService {

    private final CrmAiAgentInfoMapper crmAiAgentInfoMapper;

    @Autowired
    private AiAgentIntentService aiAgentIntentService;

    @Autowired
    private CrmAiAgentDeployService crmAiAgentDeployService;

    private final CrmAiAgentDeployMapper crmAiAgentDeployMapper;

    private final AiAgentIntentMapper aiAgentIntentMapper;

    private final RabbitTemplate rabbitTemplate;

    private final CrmAiAgentVariableService crmAiAgentVariableService;

    private final CrmAiAgentVariableMapper crmAiAgentVariableMapper;

    private final CompanyClient companyClient;

    @Autowired
    private CustomerClient customerClient;

    private final RestHighLevelClient restHighLevelClient;

    @Resource
    private final RescmgntClient rescmgntClient;

    private final ChannelClient channelClient;


//    @Value("${s3.ai-agent-share-bucket-name}")
    private String bucketName;

//    @Value("${s3.ai-agent-share-region}")
    private String region;

//    @Value("${s3.ai-agent-share-access-key}")
    private String accessKey;

//    @Value("${s3.ai-agent-share-secret-key}")
    private String secretAccessKey;

    @Autowired
    private S3Client s3Client;

    @Override
    public void run(AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer) {
        log.info("\n==================== 智能体收到请求 ====================\n" +
                "会话ID: {}\n" +
                "请求信息: {}", 
                aiAgentExecRequest.getSessionId(),
                aiAgentExecRequest);

        RunContext runContext = null;
        // 保存用户输入
        if(!StringUtil.isEmpty(aiAgentExecRequest.getInput())){
            // 执行记录用户输入信息操作。
            RunContext runContext1 = recordUserInputToTicket(aiAgentExecRequest);
            // 如果为空表示是音频转录失败，直接退出不做处理
            if (null == runContext1) {
                log.info("保存用户输入失败直接退出");
                return;
            }
            // 作用：重新设置输入内容信息，如果是音频时将input内容替换为转录后的文本，其他类型的原样不动。
            aiAgentExecRequest.setInput(runContext1.getInput());
            aiAgentExecRequest.setInputType(runContext1.getInputType());
            runContext = runContext1;
        }
        // 获取当前会话
        SessionAiAgent sessionAiAgent = getSessionAiAgent(aiAgentExecRequest.getSessionId());

        // 会话存在的情况处理
        if (sessionAiAgent != null) {
            // 先把最新的客户输入保存到会话中
            if(runContext != null){
                sessionAiAgent.addSessionContext(runContext);
                String sessionKey = Constants.AI_AGENT_SESSION.concat(aiAgentExecRequest.getSessionId());
                RedisCacheUtil.setCacheObject(sessionKey, sessionAiAgent);
            }
            // 处理动态意图识别会话
            if (handleDynamicIntentRecognitionSession(sessionAiAgent, aiAgentExecRequest, sseConsumer)) {
                sseConsumer.consume("",true);
                return;
            }
            
            // 处理正在运行的智能体会话
            AiAgent runningAiAgent = getRunningAiAgent(sessionAiAgent);
            if (runningAiAgent != null) {
                if (handleRunningAgent(runningAiAgent, sessionAiAgent, aiAgentExecRequest, sseConsumer)) {
                    sseConsumer.consume("",true);
                    return;
                }
            } else {
                // 无智能体，只能是动态意图识别完，没识别到，直接到兜底
                if(executeFallbackAgent(null, sessionAiAgent, aiAgentExecRequest, sseConsumer)){
                    sseConsumer.consume("",true);
                    return;
                }
            }
        }
        // 创建新会话
        sessionAiAgent = new SessionAiAgent();
        sessionAiAgent.setCompanyId(aiAgentExecRequest.getCompanyId());
        sessionAiAgent.setChannelTypeId(aiAgentExecRequest.getChannelTypeId());
        sessionAiAgent.setSessionId(aiAgentExecRequest.getSessionId());
        if(runContext != null){
            sessionAiAgent.addSessionContext(runContext);
        }
        createSessionAiAgent(sessionAiAgent);
        // 创建工单
        createTicket(aiAgentExecRequest);

        // 新会话处理：初始化欢迎智能体 只有WEB在线聊天、APP在线聊天、Shopify、微信小程序与企业进行沟通时 才走欢迎语，
        if(Objects.equals(aiAgentExecRequest.getChannelId(), "8") || Objects.equals(aiAgentExecRequest.getChannelId(), "9") || Objects.equals(aiAgentExecRequest.getChannelId(), "22") || Objects.equals(aiAgentExecRequest.getChannelId(), "21")){
            handleNewSession(aiAgentExecRequest, sessionAiAgent, sseConsumer);
        } else {
            log.info("\n==================== 处理新会话-非WEB在线聊天、APP在线聊天、Shopify、微信小程序 ====================\n" +
                    "请求信息: {}", 
                    aiAgentExecRequest);
            handleIntentRecognition(null, sessionAiAgent, aiAgentExecRequest, sseConsumer);
        }

        sseConsumer.consume("",true);
    }
    
    /**
     * 获取当前会话信息
     * @param sessionId 会话ID
     * @return SessionAiAgent 会话信息
     */
    private SessionAiAgent getSessionAiAgent(String sessionId) {
        String sessionKey = Constants.AI_AGENT_SESSION.concat(sessionId);
        Object sessionAiAgentObj = RedisCacheUtil.getCacheObject(sessionKey);
        return sessionAiAgentObj != null ? (SessionAiAgent) sessionAiAgentObj : null;
    }
    
    /**
     * 获取当前正在运行的智能体
     * @param sessionAiAgent 会话信息
     * @return AiAgent 正在运行的智能体
     */
    private AiAgent getRunningAiAgent(SessionAiAgent sessionAiAgent) {
        if(sessionAiAgent.lastAiAgent() == null){
            return null;
        }
        String lastAiAgentExecId = sessionAiAgent.lastAiAgent();
        String runningAgentKey = Constants.AI_AGENT_RUN.concat(lastAiAgentExecId);
        Object runningAiAgentObj = RedisCacheUtil.getCacheObject(runningAgentKey);
        return runningAiAgentObj != null ? (AiAgent) runningAiAgentObj : null;
    }
    
    /**
     * 处理动态意图识别会话
     * @param sessionAiAgent 会话信息
     * @param aiAgentExecRequest 请求信息
     * @param sseConsumer 响应消费者
     * @return 是否已处理完成
     */
    private boolean handleDynamicIntentRecognitionSession(SessionAiAgent sessionAiAgent, AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer) {

        // 检查当前会话是否在进行动态意图识别
        if (sessionAiAgent.getIntentId() == null) {
            return false;
        }
        
        log.info("\n==================== 继续动态意图识别会话 ====================\n" +
        "会话ID: {}\n" +
        // "会话信息: {}", 
        sessionAiAgent.getSessionId());

        // 调用意图识别服务
        log.info("\n==================== 调用意图识别服务 ====================\n");
        IntentRecognitionPy intentRecognitionPy = aiAgentIntentService.intentRecognition(
                sessionAiAgent.getCompanyId(), 
                aiAgentExecRequest.getInput(),
                sessionAiAgent.getIntentId(),
                sessionAiAgent.getIntentContexts(),
                sessionAiAgent.getRequestId()
        );

        if (intentRecognitionPy == null) {
            // 刷新上下文
            String sessionKey = Constants.AI_AGENT_SESSION.concat(aiAgentExecRequest.getSessionId());
            RedisCacheUtil.setCacheObject(sessionKey, sessionAiAgent);
            return false;
        }
        
        // 继续执行动态意图识别流程
        boolean isFinish = dynamicIntentRecognition(intentRecognitionPy, sessionAiAgent, aiAgentExecRequest, sseConsumer);
        if (!isFinish) {
            // 意图识别未完成，刷新会话上下文
            String sessionKey = Constants.AI_AGENT_SESSION.concat(aiAgentExecRequest.getSessionId());
            RedisCacheUtil.setCacheObject(sessionKey, sessionAiAgent);
            return true;
        }
        //AIGC计费相关
        InvokeServiceUtil instances = InvokeServiceUtil.getInstance();
        log.info("动态意图识别 AIGC计费开始====");
        instances.sendBillingAlarm(aiAgentExecRequest.getCompanyId(), aiAgentExecRequest.getChannelId(), aiAgentExecRequest.getSourceChannelId(), ChargeItemServiceEnum.AI_AGENT_INTENT_RECOGNITION);

        // 意图识别完成，执行对应的智能体
        return executeIntentAgent(intentRecognitionPy, sessionAiAgent, aiAgentExecRequest, sseConsumer);
    }
    
    /**
     * 执行意图对应的智能体
     * @param intentRecognitionPy 意图识别结果
     * @param sessionAiAgent 会话信息
     * @param aiAgentExecRequest 请求信息
     * @param sseConsumer 响应消费者
     * @return 是否执行成功
     */
    private boolean executeIntentAgent(IntentRecognitionPy intentRecognitionPy, SessionAiAgent sessionAiAgent, AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer) {
        log.info("\n==================== 执行意图对应的智能体 ====================\n" +
                "意图ID: {}\n" +
                "意图名称: {}\n" +
                "会话ID: {}", 
                intentRecognitionPy.getIntentId(),
                intentRecognitionPy.getIntentName(),
                sessionAiAgent.getSessionId());
        AiAgent intentAiAgent = getIntentAiAgent(intentRecognitionPy.getIntentId(), aiAgentExecRequest, sessionAiAgent.isTest());
        if (intentAiAgent == null) {
            log.info("\n==================== 意图对应的智能体不存在 ====================\n");
//            sseConsumer.consume(MessageUtils.get("agent.not.found"), false);
            return false;
        }
        intentAiAgent.setIntentId(intentRecognitionPy.getIntentId());
        // 正常运行发找到意图的mq消息
        if(!sessionAiAgent.isTest()){
           // 找到意图后，发送识别出意图的mq
           sendIntentRecognitionMq(intentRecognitionPy.getIntentId(), intentRecognitionPy.getIntentName(), intentAiAgent.getAiAgentId(), intentAiAgent.getAiAgentName(), sessionAiAgent);
        }
        try {
            sessionAiAgent.setIntentId(null);
            sessionAiAgent.setIntentContexts(null);
            switchAiAgent(sessionAiAgent, aiAgentExecRequest, intentAiAgent);
            if(intentRecognitionPy.getScriptType() == 2){
                intentAiAgent.setIntentAttribute((JSONObject) JSONObject.toJSON(intentRecognitionPy.getResult()));
                log.info("意图识别结果：{}", JSONObject.toJSON(intentRecognitionPy.getResult()));
            }
            intentAiAgent.runStream(rabbitTemplate, sseConsumer, sessionAiAgent);
            updateSessionAiAgent(sessionAiAgent, intentAiAgent);
            return true;
        } catch (Exception e) {
            log.error("执行意图智能体异常", e);
            sseConsumer.consume(MessageUtils.get("agent.not.found"), true);
            return true;
        }
    }
    
    /**
     * 处理正在运行的智能体
     * @param runningAiAgent 正在运行的智能体
     * @param sessionAiAgent 会话信息
     * @param aiAgentExecRequest 请求信息
     * @param sseConsumer 响应消费者
     * @return 是否已处理完成
     */
    private boolean handleRunningAgent(AiAgent runningAiAgent, SessionAiAgent sessionAiAgent, AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer) {
        log.info("\n==================== 处理正在运行的智能体 ====================\n" +
                "智能体ID: {}\n" +
                "智能体名称: {}\n" +
                "会话ID: {}\n",
                runningAiAgent.getAiAgentId(),
                runningAiAgent.getAiAgentName(),
                sessionAiAgent.getSessionId());
        // 如果有下一个节点，继续执行当前智能体
        if (runningAiAgent.getNextNode() != null) {
            try {
//                sessionAiAgent.getSessionContexts().add(Optional.ofNullable(sessionAiAgent.getSessionContexts()).orElse(new ArrayList<>()));
                runningAiAgent.runStream(rabbitTemplate, sseConsumer, sessionAiAgent);
                updateSessionAiAgent(sessionAiAgent, runningAiAgent);
                // 非askquestion的节点，运行完直接返回
                log.info("检查状态：{},{}", runningAiAgent.getCurrentNode().getComponentType(), runningAiAgent.getNextNode());
                if(!runningAiAgent.getCurrentNode().getComponentType().contains("AskQuestion")){
                    return true;
                }else {
                    if(runningAiAgent.getNextNode() != null && runningAiAgent.getNextNode().getComponentType().contains("AskQuestion")){
                        return true;
                    }
                }
                // 下一节点不为空，但run结束了，等下一次调用
//                if(runningAiAgent.getNextNode() != null){

//                }
            } catch (Exception e) {
                log.error("执行智能体节点异常", e);
                sseConsumer.consume(MessageUtils.get("agent.not.found"), true);
                return true;
            }
        } else {
            // 没下一个节点，要把当前用户输入加入到会话上下文中
            log.info("调用用户输入 handleRunningAgent");
//            RunContext runContext = runningAiAgent.addUserInput(aiAgentExecRequest.getInput(), aiAgentExecRequest.getInputType(), RunContext.ROLE_USER);
//            sessionAiAgent.addSessionContext(runContext);
            updateSessionAiAgent(sessionAiAgent, runningAiAgent);
        }
        // 当前智能体流程结束，进行意图识别
        return handleIntentRecognition(runningAiAgent, sessionAiAgent, aiAgentExecRequest, sseConsumer);
    }
    
    /**
     * 对话结束后进行意图识别
     * @param runningAiAgent 正在运行的智能体
     * @param sessionAiAgent 会话信息
     * @param aiAgentExecRequest 请求信息
     * @param sseConsumer 响应消费者
     * @return 是否已处理完成
     */
    private boolean handleIntentRecognition(AiAgent runningAiAgent, SessionAiAgent sessionAiAgent, AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer) {
        log.info("\n==================== 当前智能体没有下一个节点，开始意图识别 ====================\n" +
                "智能体ID: {}\n" +
                "智能体名称: {}\n" +
                "会话ID: {}\n",
                runningAiAgent == null ? "无" : runningAiAgent.getAiAgentId(),
                runningAiAgent == null ? "无" : runningAiAgent.getAiAgentName(),
                sessionAiAgent.getSessionId());
        String lastUserInput = "";
        if(runningAiAgent != null){
            lastUserInput = runningAiAgent.lastUserInput().getInput();
        } else {
            lastUserInput = aiAgentExecRequest.getInput();
        }
        log.info("\n==================== 调用意图识别服务 ====================\n");
        // 调用意图识别服务
        IntentRecognitionPy intentRecognitionPy = aiAgentIntentService.intentRecognition(
                sessionAiAgent.getCompanyId(), 
                lastUserInput,
                "",
                null, null
        );
        if (intentRecognitionPy != null && intentRecognitionPy.getIsGetIntent() == 1) {
            // 测试时，只有找到的意图对应的ai agent id与当前ai agent id一致，运行这个智能体
            if(sessionAiAgent.isTest()){
               // 获取命中意图的智能体id
               CrmAiAgentInfo aiAgentInfoByIntentId = aiAgentIntentMapper.getTestAiAgentInfoByIntentId(intentRecognitionPy.getIntentId(), AiAgent.AI_AGENT_TYPE_NORMAL, aiAgentExecRequest.getChannelTypeId());;
               if(aiAgentInfoByIntentId == null || !aiAgentInfoByIntentId.getAiAgentId().equals(aiAgentExecRequest.getAiAgentId())){
                 AiAgentResult aiAgentResult = new AiAgentResult();
                 aiAgentResult.setActionType(AiAgentResult.ActionType.MESSAGE);
                 aiAgentResult.setContentType(AiAgentResult.ContentType.TEXT);
                 aiAgentResult.setContent(MessageUtils.get("aiagent.test.flow.intent.mapping.fail"));
                 aiAgentResult.setFinish(true);
                 sseConsumer.consume(JSONObject.toJSONString(aiAgentResult), false);
                 return true;
               }
            }
            // 静态意图处理
            if (intentRecognitionPy.getScriptType() == 1) {
                if (executeStaticIntentAgent(intentRecognitionPy, sessionAiAgent, aiAgentExecRequest, sseConsumer)) {
                    return true;
                }
            } 
            // 动态意图处理
            else if (intentRecognitionPy.getScriptType() == 2) {
                // 保存requestId
                sessionAiAgent.setRequestId(intentRecognitionPy.getRequestId());
                if (handleDynamicIntent(intentRecognitionPy, sessionAiAgent, aiAgentExecRequest, sseConsumer)) {
                    return true;
                }
            }
        }
        // 测试时，直接返回true，不兜底去了
        if(sessionAiAgent.isTest()){
            return true;
        }
        // 没有识别到意图或意图处理失败，使用兜底智能体
        return executeFallbackAgent(runningAiAgent, sessionAiAgent, aiAgentExecRequest, sseConsumer);
    }
    
    /**
     * 执行静态意图对应的智能体
     */
    private boolean executeStaticIntentAgent(IntentRecognitionPy intentRecognitionPy, SessionAiAgent sessionAiAgent, AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer) {
        log.info("\n==================== 执行静态意图对应的智能体 ====================\n" +
                "意图ID: {}\n" +
                "意图名称: {}\n" +
                "会话ID: {}" +
                "session中的上下文: {}",
                intentRecognitionPy.getIntentId(),
                intentRecognitionPy.getIntentName(),
                sessionAiAgent.getSessionId(),
                sessionAiAgent.getSessionContexts());
        AiAgent intentAiAgent = getIntentAiAgent(intentRecognitionPy.getIntentId(), aiAgentExecRequest, sessionAiAgent.isTest());
        if (intentAiAgent == null) {
            log.info("\n==================== 静态意图对应的智能体不存在 ====================\n" +
                    "请求信息: {}", 
                    aiAgentExecRequest);
            return false;
        }
        // 找到意图后，发送识别出意图的mq
        sendIntentRecognitionMq(intentRecognitionPy.getIntentId(), intentRecognitionPy.getIntentName(), intentAiAgent.getAiAgentId(), intentAiAgent.getAiAgentName(), sessionAiAgent);
        try {
            intentAiAgent.setIntentId(intentRecognitionPy.getIntentId());
            switchAiAgent(sessionAiAgent, aiAgentExecRequest, intentAiAgent);
            intentAiAgent.runStream(rabbitTemplate, sseConsumer, sessionAiAgent);
            updateSessionAiAgent(sessionAiAgent, intentAiAgent);
            return true;
        } catch (Exception e) {
            log.error("执行静态意图智能体异常", e);
            // 刷新session保存上下文
            String sessionKey = Constants.AI_AGENT_SESSION.concat(aiAgentExecRequest.getSessionId());
            RedisCacheUtil.setCacheObject(sessionKey, sessionAiAgent);
            sseConsumer.consume(MessageUtils.get("agent.not.found"), true);
            return false;
        }
    }
    
    /**
     * 处理动态意图
     */
    private boolean handleDynamicIntent(IntentRecognitionPy intentRecognitionPy, SessionAiAgent sessionAiAgent, AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer) {
        log.info("\n==================== 处理动态意图 ====================\n" +
                "意图ID: {}\n" +
                "意图名称: {}\n" +
                "会话ID: {}", 
                intentRecognitionPy.getIntentId(),
                intentRecognitionPy.getIntentName(),
                sessionAiAgent.getSessionId());
        boolean isFinish = dynamicIntentRecognition(intentRecognitionPy, sessionAiAgent, aiAgentExecRequest, sseConsumer);
        if (isFinish) {
            log.info("\n==================== 动态意图识别完成 ====================\n" +
                    "意图ID: {}\n" +
                    "意图名称: {}\n" +
                    "识别结果: {}", 
                    intentRecognitionPy.getIntentId(),
                    intentRecognitionPy.getIntentName(),
                    intentRecognitionPy.getResult()); 
            AiAgent intentAiAgent = getIntentAiAgent(intentRecognitionPy.getIntentId(), aiAgentExecRequest, sessionAiAgent.isTest());

            if (intentAiAgent != null) {
                // 找到意图后，发送识别出意图的mq
                sendIntentRecognitionMq(intentRecognitionPy.getIntentId(), intentRecognitionPy.getIntentName(), intentAiAgent.getAiAgentId(), intentAiAgent.getAiAgentName(), sessionAiAgent);
                sessionAiAgent.setIntentId(null);
                sessionAiAgent.setIntentContexts(null);
                try {
                    switchAiAgent(sessionAiAgent, aiAgentExecRequest, intentAiAgent);
                    intentAiAgent.setIntentAttribute((JSONObject) JSONObject.toJSON(intentRecognitionPy.getResult()));
                    intentAiAgent.runStream(rabbitTemplate, sseConsumer, sessionAiAgent);
                    updateSessionAiAgent(sessionAiAgent, intentAiAgent);
                    return true;
                } catch (Exception e) {
                    log.error("执行动态意图智能体异常", e);
                    // 刷新session保存上下文
                    String sessionKey = Constants.AI_AGENT_SESSION.concat(aiAgentExecRequest.getSessionId());
                    RedisCacheUtil.setCacheObject(sessionKey, sessionAiAgent);
                    sseConsumer.consume(MessageUtils.get("agent.not.found"), true);
                }
            }
        } else {
            // 刷新session保存上下文
            String sessionKey = Constants.AI_AGENT_SESSION.concat(aiAgentExecRequest.getSessionId());
            RedisCacheUtil.setCacheObject(sessionKey, sessionAiAgent);
            return true;
        }
        return false;
    }
    
    /**
     * 执行兜底智能体
     */
    private boolean executeFallbackAgent(AiAgent runningAiAgent, SessionAiAgent sessionAiAgent, AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer) {
        log.info("\n==================== 执行兜底智能体 ====================\n" +
                "会话ID: {}", 
                sessionAiAgent.getSessionId());
        AiAgent fallbackAiAgent = getFallbackAiAgent(aiAgentExecRequest);
        if (fallbackAiAgent == null) {
            return false;
        }
        try {
            copyAiAgent(runningAiAgent, fallbackAiAgent);
//            fallbackAiAgent.setRunContexts(Optional.ofNullable(sessionAiAgent.getSessionContexts()).orElse(new ArrayList<>()));
            fallbackAiAgent.runStream(rabbitTemplate, sseConsumer, sessionAiAgent);
            updateSessionAiAgent(sessionAiAgent, fallbackAiAgent);
            return true;
        } catch (Exception e) {
            log.error("执行兜底智能体异常", e);
            sseConsumer.consume(MessageUtils.get("agent.not.found"), true);
            return false;
        }
    }
    
    /**
     * 处理新会话
     * @param aiAgentExecRequest 请求信息
     * @param sseConsumer 响应消费者
     */
    private void handleNewSession(AiAgentExecRequest aiAgentExecRequest, SessionAiAgent sessionAiAgent, ResponseBodyConsumer sseConsumer) {
        log.info("\n==================== 处理新会话-欢迎语 ====================\n" +
                "请求信息: {}", 
                aiAgentExecRequest);
        // 查询并执行欢迎智能体
        AiAgent welcomeAiAgent = getWelcomeAiAgent(aiAgentExecRequest);
        if (welcomeAiAgent == null) {
            return;
        }
        
        try {
//            if (null != sessionAiAgent) {
//                welcomeAiAgent.setRunContexts(Optional.ofNullable(sessionAiAgent.getSessionContexts()).orElse(new ArrayList<>()));
//            }
            welcomeAiAgent.runStream(rabbitTemplate, sseConsumer, sessionAiAgent);
            updateSessionAiAgent(sessionAiAgent, welcomeAiAgent);
        } catch (Exception e) {
            log.error("执行欢迎智能体异常", e);
            sseConsumer.consume(MessageUtils.get("agent.not.found"), true);
        }
    }

    /**
     * 动态意图判断
     * @param intentRecognitionPy
     * @param sessionAiAgent
     * @param aiAgentExecRequest
     * @param sseConsumer
     * @return
     */
    private Boolean dynamicIntentRecognition(IntentRecognitionPy intentRecognitionPy,SessionAiAgent sessionAiAgent, AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer){
        // 先判断动态意图结果，是否一次性拿到所有变量
        Object result = intentRecognitionPy.getResult();
        // String就是没拿到，需要继续对话
        if(result instanceof String){
            String resultStr = (String) result;
            // 记录对话上下文 - 使用会话中最后一个用户输入的RunContext，而不是创建新的
            RunContext userRunContext = sessionAiAgent.lastUserInput();
            // 如果没有用户上下文，才创建新的
            if(userRunContext == null) {
                userRunContext = new RunContext(aiAgentExecRequest.getInput(), RunContext.ROLE_USER, RunContext.INPUT_TYPE_TEXT);
            }
            sessionAiAgent.addIntentContext(userRunContext);
            RunContext intentContext = new RunContext(resultStr, RunContext.ROLE_BOT, RunContext.INPUT_TYPE_TEXT);
            sessionAiAgent.addIntentContext(intentContext);
            sessionAiAgent.setIntentId(intentRecognitionPy.getIntentId());
            sessionAiAgent.addSessionContext(intentContext);
            // 返回结果
            AiAgentResult aiAgentResult = new AiAgentResult();
            aiAgentResult.setActionType(AiAgentResult.ActionType.MESSAGE);
            aiAgentResult.setContentType(AiAgentResult.ContentType.TEXT);
            aiAgentResult.setContent(resultStr);
            aiAgentResult.setFinish(true);
            aiAgentResult.setMsgId(intentContext.getMsgId());
            sseConsumer.consume(JSONObject.toJSONString(aiAgentResult), false);
            // 保存工单回复
            replyTicket(rabbitTemplate, aiAgentExecRequest, intentContext, 3, null);
            return false;
        }
        return true;
    }

    /**
     * 识别到意图切到新的智能体
     * @param
     * @param sessionAiAgent
     * @param aiAgentExecRequest
     * @param
     * @return
     */ 
    private void switchAiAgent(SessionAiAgent sessionAiAgent, AiAgentExecRequest aiAgentExecRequest, AiAgent intentAiAgent){
        // 获取上一个智能体
        if(sessionAiAgent.lastAiAgent() != null){
            String lastAiAgentExecId = sessionAiAgent.lastAiAgent();
            String runningAgentKey = Constants.AI_AGENT_RUN.concat(lastAiAgentExecId);
            Object runningAiAgentObj = RedisCacheUtil.getCacheObject(runningAgentKey);
            AiAgent runningAiAgent = null;
            if(runningAiAgentObj != null){
                runningAiAgent = (AiAgent) runningAiAgentObj;
                // 复制智能体
                copyAiAgent(runningAiAgent, intentAiAgent);
            }
//            intentLog(intentAiAgent.getIntentId(), intentAiAgent);
            log.info("保存意图日志 intentAiAgent:{}",intentAiAgent);
            intentLog(intentAiAgent.getIntentId(), intentAiAgent);
            updateSessionAiAgent(sessionAiAgent, intentAiAgent);
        } else {
            log.info("调用用户输入 switchAiAgent");
            // 意图交互存log
            RunContext runContext = intentAiAgent.addUserInput(aiAgentExecRequest.getInput(), aiAgentExecRequest.getInputType(), RunContext.ROLE_USER);
            sessionAiAgent.addSessionContext(runContext);
            updateSessionAiAgent(sessionAiAgent, intentAiAgent);
            intentLog(intentAiAgent.getIntentId(), runContext, aiAgentExecRequest.getSessionId(), aiAgentExecRequest.getCompanyId());
        }
    }

    /**
     * 执行测试流
     * @param aiAgentExecRequest
     * @param sseConsumer
     */
    @Override
    public void runTestStream(AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer) {
        // 测试默认渠道
        aiAgentExecRequest.setChannelId("8");
        CrmAiAgentInfo aiAgentInfo = crmAiAgentInfoMapper.selectById(aiAgentExecRequest.getAiAgentId());
        if (aiAgentInfo == null) {
            sseConsumer.consume(MessageUtils.get("agent.not.found"),true);
        }
        if(aiAgentInfo.getAiAgentType() == AiAgent.AI_AGENT_TYPE_NORMAL && StringUtil.isNotEmpty(aiAgentExecRequest.getInput())){
            runTestStreamIntent(aiAgentExecRequest, sseConsumer);
        }else{
            runTestStreamOther(aiAgentExecRequest, sseConsumer);
        }
    }

    /**
     * 执行测试流意图-意图识别
     * @param aiAgentExecRequest
     * @param sseConsumer
     */
    private void runTestStreamIntent(AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer) {
        // 保存用户输入
        RunContext runContext = null;
        if(!StringUtil.isEmpty(aiAgentExecRequest.getInput())){
            runContext = new RunContext(aiAgentExecRequest.getInput(), RunContext.ROLE_USER, aiAgentExecRequest.getInputType());
        }

        //查询当前会话是否存在
        String sessionKey = Constants.AI_AGENT_SESSION.concat(aiAgentExecRequest.getSessionId());
        Object sessionAiAgentObj = RedisCacheUtil.getCacheObject(sessionKey);
        SessionAiAgent sessionAiAgent = null;
        if(sessionAiAgentObj != null){
            sessionAiAgent = (SessionAiAgent) sessionAiAgentObj;
        }
       
        if(sessionAiAgent != null){
            // 执行智能体
            // 检查当前会话是否正在运行智能体
            if(runContext != null){
                sessionAiAgent.addSessionContext(runContext);
                RedisCacheUtil.setCacheObject(sessionKey, sessionAiAgent);
            }

            // 处理动态意图识别会话
            if (handleDynamicIntentRecognitionSession(sessionAiAgent, aiAgentExecRequest, sseConsumer)) {
                sseConsumer.consume("",true);
                return;
            }

            String lastAiAgentExecId = sessionAiAgent.lastAiAgent();
            String runningAgentKey = Constants.AI_AGENT_RUN.concat(lastAiAgentExecId);
            Object runningAiAgentObj = RedisCacheUtil.getCacheObject(runningAgentKey);
              AiAgent runningAiAgent = null;
            if(runningAiAgentObj != null){
                runningAiAgent = (AiAgent) runningAiAgentObj;
            }
            if (runningAiAgent != null) {
                // 当前智能体有节点还没有执行，就继续执行
                if(runningAiAgent.getNextNode() != null){
                    try {
//                        runningAiAgent.setRunContexts(Optional.ofNullable(sessionAiAgent.getSessionContexts()).orElse(new ArrayList<>()));
                        runningAiAgent.runStream(rabbitTemplate,true,sseConsumer, sessionAiAgent);
                        updateSessionAiAgent(sessionAiAgent, runningAiAgent);
                        // 最终断开sse
                        sseConsumer.consume("",true);
                        return;
                    } catch (Exception e) {
                        log.error("执行智能体异常", e);
                        return;
                    }
                }
            }
        }
        // 创建新的会话
        SessionAiAgent newSessionAiAgent = new SessionAiAgent();
        newSessionAiAgent.setCompanyId(aiAgentExecRequest.getCompanyId());
        newSessionAiAgent.setChannelTypeId(aiAgentExecRequest.getChannelTypeId());
        newSessionAiAgent.setSessionId(aiAgentExecRequest.getSessionId());
        newSessionAiAgent.setTest(true);
        if(runContext != null){
            newSessionAiAgent.addSessionContext(runContext);
        }
        createSessionAiAgent(newSessionAiAgent);
        
        // 先判断意图识别
        handleIntentRecognition(null, newSessionAiAgent, aiAgentExecRequest, sseConsumer);
        sseConsumer.consume("", true);
   }

   /**
    * 执行测试流其他-欢迎语/兜底/没选开始节点的意图
    * @param aiAgentExecRequest
    * @param sseConsumer
    */
   private void runTestStreamOther(AiAgentExecRequest aiAgentExecRequest, ResponseBodyConsumer sseConsumer){
         // 保存用户输入
         RunContext runContext = null;
         if(!StringUtil.isEmpty(aiAgentExecRequest.getInput())){
             runContext = new RunContext(aiAgentExecRequest.getInput(), RunContext.ROLE_USER, aiAgentExecRequest.getInputType());
         }
        String sessionKey = Constants.AI_AGENT_SESSION.concat(aiAgentExecRequest.getSessionId());
        Object sessionAiAgentObj = RedisCacheUtil.getCacheObject(sessionKey);
        SessionAiAgent sessionAiAgent = null;
        if(sessionAiAgentObj != null){
            sessionAiAgent = (SessionAiAgent) sessionAiAgentObj;
        }
       
        if(sessionAiAgent != null){
            if(runContext != null){
                sessionAiAgent.addSessionContext(runContext);
                sessionKey = Constants.AI_AGENT_SESSION.concat(aiAgentExecRequest.getSessionId());
                RedisCacheUtil.setCacheObject(sessionKey, sessionAiAgent);
            }
            // 检查当前会话是否正在运行智能体
            String lastAiAgentExecId = sessionAiAgent.lastAiAgent();
            String runningAgentKey = Constants.AI_AGENT_RUN.concat(lastAiAgentExecId);
            Object runningAiAgentObj = RedisCacheUtil.getCacheObject(runningAgentKey);
            AiAgent runningAiAgent = null;
            if(runningAiAgentObj != null){
                runningAiAgent = (AiAgent) runningAiAgentObj;
            }
            if (runningAiAgent != null) {
                // 当前智能体有节点还没有执行，就继续执行
                if(runningAiAgent.getNextNode() != null){
                    try {
                         runningAiAgent.runStream(rabbitTemplate,true,sseConsumer, sessionAiAgent);
                         return;
                    } catch (Exception e) {
                        log.error("执行智能体异常", e);
                    }
                }
            }
        }
        CrmAiAgentInfo aiAgentInfo = crmAiAgentInfoMapper.selectById(aiAgentExecRequest.getAiAgentId());
        if (aiAgentInfo == null) {
            sseConsumer.consume(MessageUtils.get("agent.not.found"),true);
        }
        AiAgent aiAgent = initAiAgent(aiAgentInfo, aiAgentExecRequest);
        try {
            // 创建session并保存
            sessionAiAgent = new SessionAiAgent();
            sessionAiAgent.setCompanyId(aiAgentExecRequest.getCompanyId());
            sessionAiAgent.setChannelTypeId(aiAgentExecRequest.getChannelTypeId());
            sessionAiAgent.setSessionId(aiAgentExecRequest.getSessionId());
            sessionAiAgent.setTest(true);
            if(runContext != null){
                sessionAiAgent.addSessionContext(runContext);
            }
            createSessionAiAgent(sessionAiAgent);
            aiAgent.runStream(rabbitTemplate,true,sseConsumer,sessionAiAgent);
            updateSessionAiAgent(sessionAiAgent, aiAgent);
        } catch (Exception e) {
            log.error("执行智能体异常", e);
        }
        sseConsumer.consume("", true);
   }

    @Override
    public AjaxResult<Integer> aiAgentCount(String companyId) {

        return AjaxResult.ok(Math.toIntExact(this.baseMapper.selectCount(
                new LambdaQueryWrapper<CrmAiAgentInfo>()
                        .eq(CrmAiAgentInfo::getCompanyId, companyId)
                        .eq(CrmAiAgentInfo::getDataStatus, 1)
                        .eq(CrmAiAgentInfo::getChannelTypeId,1))));
    }

    @Override
    public AjaxResult<String> saveAgentInfo(AiAgentInfoVo agent) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String userId = SecurityUtil.getLoginUser().getUserId();
        Date now = new Date();

        // 1. 校验绑定关系是否已存在
        if (StringUtil.isNotEmpty(agent.getIntentionId())) {
            LambdaQueryWrapper<CrmAiAgentInfo> queryWrapperChannel = new LambdaQueryWrapper<CrmAiAgentInfo>()
                    .eq(CrmAiAgentInfo::getIntentionId, agent.getIntentionId())
                    .eq(CrmAiAgentInfo::getDataStatus, 1)
                    .like(CrmAiAgentInfo::getChannelTypeId, agent.getChannelTypeId())
                    .eq(agent.getAiAgentId() != null, CrmAiAgentInfo::getAiAgentId, agent.getAiAgentId());

            if (this.baseMapper.selectCount(queryWrapperChannel) > 0) {
                return AjaxResult.failure(MessageUtils.get("agent.binding.failure"));
            }
        }

        // 2. 校验智能体名称是否重复
        LambdaQueryWrapper<CrmAiAgentInfo> queryWrapper = new LambdaQueryWrapper<CrmAiAgentInfo>()
                .eq(CrmAiAgentInfo::getCompanyId, companyId)
                .eq(CrmAiAgentInfo::getAiAgentName, agent.getAiAgentName())
                .eq(CrmAiAgentInfo::getDataStatus, 1)
                .ne(agent.getAiAgentId() != null, CrmAiAgentInfo::getAiAgentId, agent.getAiAgentId());

        if (this.baseMapper.selectCount(queryWrapper) > 0) {
            return AjaxResult.failure(MessageUtils.get("agent.name.duplicate"));
        }

        // 2.2. 判断智能体类型是否是电话类型，如果是校验绑定的电话是否可用
        if (agent.getChannelTypeId() != null && agent.getChannelTypeId() == 3 && StringUtil.isNotEmpty(agent.getSystemPhone())) {
            // 拆分多个号码
            List<String> phoneList = Arrays.stream(agent.getSystemPhone().split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            if (!phoneList.isEmpty()) {
                // 查询是否有其他智能体已绑定这些号码
                LambdaQueryWrapper<CrmAiAgentInfo> phoneQuery = new LambdaQueryWrapper<CrmAiAgentInfo>()
                        .eq(CrmAiAgentInfo::getCompanyId, companyId)
                        .eq(CrmAiAgentInfo::getDataStatus, 1)
                        .eq(CrmAiAgentInfo::getChannelTypeId, 3)
                        .ne(agent.getAiAgentId() != null, CrmAiAgentInfo::getAiAgentId, agent.getAiAgentId())
                        .isNotNull(CrmAiAgentInfo::getSystemPhone);

                List<CrmAiAgentInfo> existAgents = this.baseMapper.selectList(phoneQuery);
                for (CrmAiAgentInfo exist : existAgents) {
                    if (StringUtil.isNotEmpty(exist.getSystemPhone())) {
                        List<String> existPhones = Arrays.stream(exist.getSystemPhone().split(","))
                                .map(String::trim)
                                .filter(StringUtils::isNotEmpty)
                                .collect(Collectors.toList());
                        for (String phone : phoneList) {
                            if (existPhones.contains(phone)) {
                                return AjaxResult.failure(MessageUtils.get("agent.voice.binding.phone"),phone);
                            }
                        }
                    }
                }
            }
        }

        // 3. 复制属性
        CrmAiAgentInfo crmAiAgentInfo = new CrmAiAgentInfo();
        BeanUtils.copyProperties(agent, crmAiAgentInfo);

        // 4. 更新或插入操作
        if (crmAiAgentInfo.getAiAgentId() != null) {
            // 更新现有智能体
            if (this.baseMapper.selectById(crmAiAgentInfo.getAiAgentId()) != null) {
                crmAiAgentInfo.setModifyTime(now);
                crmAiAgentInfo.setModifier(userId);
                this.crmAiAgentInfoMapper.updateById(crmAiAgentInfo);
            }
        } else {
            // 新建智能体
            crmAiAgentInfo.setAiAgentId(UUID.randomUUID().toString().replace("-", ""));
        }

        // 5. 检查是否超过最大代理数量
        Integer aiAgentCount = this.baseMapper.selectAiAgentCount(companyId);
        LambdaQueryWrapper<CrmAiAgentInfo> countQuery = new LambdaQueryWrapper<CrmAiAgentInfo>()
                .eq(CrmAiAgentInfo::getCompanyId, companyId)
                .eq(CrmAiAgentInfo::getDataStatus, 1);

        Long currentAgentCount = this.baseMapper.selectCount(countQuery);
        if (currentAgentCount >= aiAgentCount) {
            return AjaxResult.failure(MessageUtils.get("agent.create.beyond"));
        }

        // 6. 插入新的智能体数据
        crmAiAgentInfo.setCompanyId(companyId);
        crmAiAgentInfo.setCreateTime(now);
        crmAiAgentInfo.setCreator(userId);
        crmAiAgentInfo.setAiAgentType(1);
        this.crmAiAgentInfoMapper.insert(crmAiAgentInfo);

        // 7. 异常处理: 生成默认变量
        try {
            generateDefaultVariables(crmAiAgentInfo.getAiAgentId());
        } catch (Exception e) {
            log.error("生成默认变量失败", e);
        }

        String redisKeyPrefix = Constants.AI_AGENT_SHARE;
        String redisKey = redisKeyPrefix + crmAiAgentInfo.getAiAgentId();
        RedisCacheUtil.deleteObject(redisKey);
        return AjaxResult.ok(crmAiAgentInfo.getAiAgentId());
    }


    // 用于校验渠道ID是否与已存在记录匹配的方法
    private boolean isValidChannelIds(List<CrmAiAgentInfo> aiAgentInfos, String channelIdsParam) {
        // 将传入的渠道ID字符串按逗号分割成列表
        List<String> channelIdsList = Arrays.asList(channelIdsParam.split(","));

        // 遍历aiAgentInfos，判断每个channelIds是否包含传入的渠道值
        for (CrmAiAgentInfo aiAgentInfo : aiAgentInfos) {
            // 获取每个CrmAiAgentInfo的channelIds并按逗号分隔
            List<String> agentChannelIds = Arrays.asList(aiAgentInfo.getChannelIds().split(","));

            // 判断agentChannelIds是否和传入的channelIdsList有交集
            if (!Collections.disjoint(agentChannelIds, channelIdsList)) {
                // 如果有交集，返回true
                return true;
            }
        }
        // 如果没有任何交集，返回false
        return false;
    }

    private void generateDefaultVariables (String aiAgentId) {
        String userId = SecurityUtil.getLoginUser().getUserId();
        // 检查是否已存在内置变量
        Long count = crmAiAgentVariableMapper.selectCount(new LambdaQueryWrapper<CrmAiAgentVariable>()
                .eq(CrmAiAgentVariable::getAiAgentId, aiAgentId)
                .eq(CrmAiAgentVariable::getVariableType, 1));

        if (count == 0) {
            List<CrmAiAgentVariable> variables = new ArrayList<>();
            // 获取 DefinedAttr 类的所有字段
            Field[] fields = DefinedAttr.class.getDeclaredFields();

            // 遍历字段并生成对应的内置变量
            for (Field field : fields) {

                CrmAiAgentVariable variable = new CrmAiAgentVariable();
                variable.setVariableId(UUID.randomUUID().toString().replace("-", ""));
                variable.setAiAgentId(aiAgentId);
                variable.setVariableName(field.getName());
                variable.setVariableType(1);  // 设置为系统内置变量
                variable.setCreator(userId);
                variable.setCreateTime(new Date());
                // 获取字段类型
                Class<?> fieldType = field.getType();
                String dataType = determineDataType(fieldType);
                variable.setDataType(dataType);
                variable.setDataStatus(1);    // 状态为正常
                variable.setCreateTime(new Date());
                variables.add(variable);
            }

            // 批量保存内置变量
            crmAiAgentVariableService.saveBatch(variables);
        }
    }

    @Override
    public AjaxResult<AiAgentInfoVo> getAgentById(String agentId) {

        // 查询智能体基础信息
        CrmAiAgentInfo agentInfo = crmAiAgentInfoMapper.selectById(agentId);

        if (agentInfo == null) {
            return AjaxResult.failure(MessageUtils.get("agent.not.found"));
        }

        // 复制属性到 VO 对象
        AiAgentInfoVo aiAgentInfoVo = new AiAgentInfoVo();
        BeanUtils.copyProperties(agentInfo, aiAgentInfoVo);

        // 查询最新部署信息
        CrmAiAgentDeploy deploy = crmAiAgentDeployMapper.selectOne(
                new LambdaQueryWrapper<CrmAiAgentDeploy>()
                        .eq(CrmAiAgentDeploy::getAiAgentId, agentId)
                        .orderByDesc(CrmAiAgentDeploy::getModifyTime)
                        .last("LIMIT 1")
        );

        if(Objects.nonNull(deploy)) {
            aiAgentInfoVo.setAiAgentInfo(crmAiAgentDeployService.getJsonObject(deploy));
            aiAgentInfoVo.setDeployStatus(deploy.getDeployStatus() != null ? deploy.getDeployStatus() : 0);
        }
        if(agentInfo.getIntentionId() != null) {
            AiAgentIntent agentIntent =aiAgentIntentMapper.selectById(agentInfo.getIntentionId());
            if(Objects.nonNull(agentIntent) && agentIntent.getIntentName()!=null) {
            aiAgentInfoVo.setIntentName(agentIntent.getIntentName());
            }
        }

        return AjaxResult.ok(aiAgentInfoVo);
    }

    @Override
    public AjaxResult<List<AiAgentChannelCountVo>> agentCount(AiAgentInfoVo agentInfoVo) {
        agentInfoVo.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
        List<AiAgentInfoVo> crmAiAgentInfos = this.crmAiAgentInfoMapper.selectLists(agentInfoVo);

        Map<Integer, Long> channelTypeCountMap = crmAiAgentInfos.stream()
                .collect(Collectors.groupingBy(AiAgentInfoVo::getChannelTypeId, Collectors.counting()));

        Map<Integer, Long> defaultChannelTypeCountMap = Arrays.stream(AiAgentChannelTypeEnum.values())
                .collect(Collectors.toMap(AiAgentChannelTypeEnum::getCode, type -> 0L));

        defaultChannelTypeCountMap.putAll(channelTypeCountMap);

        // 将 channelTypeId 转换为对应的名称，并构造返回结果
        List<AiAgentChannelCountVo> resultList = defaultChannelTypeCountMap.entrySet().stream()
                .map(entry -> {
                    AiAgentChannelCountVo countVo = new AiAgentChannelCountVo();
                    AiAgentChannelTypeEnum channelType = AiAgentChannelTypeEnum.fromCode(entry.getKey());
                    countVo.setChannelTypeName(channelType.getDescription());
                    countVo.setChannelTypeId(String.valueOf(entry.getKey()));
                    countVo.setCount(entry.getValue().intValue());
                    return countVo;
                })
                .collect(Collectors.toList());

        return AjaxResult.ok(resultList);
    }

    @Override
    public AjaxResult<Boolean> deleteAgentById(String agentId) {

        CrmAiAgentInfo updateInfo = new CrmAiAgentInfo();
        updateInfo.setDataStatus(0);

        LambdaUpdateWrapper<CrmAiAgentInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CrmAiAgentInfo::getAiAgentId, agentId);

        this.crmAiAgentInfoMapper.update(updateInfo, updateWrapper);

        return AjaxResult.ok(true);
    }

    @Override
    public AjaxResult<Boolean> updateStatus(String agentId) {
        // 查询当前状态
        CrmAiAgentInfo currentInfo = crmAiAgentInfoMapper.selectOne(
                new LambdaQueryWrapper<CrmAiAgentInfo>()
                        .eq(CrmAiAgentInfo::getAiAgentId, agentId)
        );

        if (Objects.isNull(currentInfo)) {
            return AjaxResult.failure("未找到对应的智能体信息");
        }

        // 切换状态
        int newStatus = currentInfo.getAiAgentStatus() == 1 ? 0 : 1;

        currentInfo.setAiAgentStatus(newStatus);
        crmAiAgentInfoMapper.updateById(currentInfo);

        return AjaxResult.ok(true);
    }

    @Override
    public AjaxResult<AiAgentInfoVo> copyAiagent(String aiAgentId) {
        if (StringUtil.isEmpty(aiAgentId)) {
            return AjaxResult.failure(MessageUtils.get("agent.not.found"));
        }
        Integer aiAgentCount=this.baseMapper.selectAiAgentCount(SecurityUtil.getLoginUser().getCompanyId());

        LambdaQueryWrapper<CrmAiAgentInfo> countQuery = new LambdaQueryWrapper<CrmAiAgentInfo>()
                .eq(CrmAiAgentInfo::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                .eq(CrmAiAgentInfo::getDataStatus, 1);
        Long currentAgentCount = this.baseMapper.selectCount(countQuery);
        if(currentAgentCount>=aiAgentCount){
            return AjaxResult.failure(MessageUtils.get("agent.create.beyond"));

        }
        CrmAiAgentInfo crmAiAgentInfo = crmAiAgentInfoMapper.selectById(aiAgentId);
        if (crmAiAgentInfo == null) {
            return AjaxResult.failure(MessageUtils.get("agent.not.found"));
        }

        AiAgentInfoVo aiAgentInfoVo = new AiAgentInfoVo();
        BeanUtils.copyProperties(crmAiAgentInfo, aiAgentInfoVo);
        aiAgentInfoVo.setAiAgentName(crmAiAgentInfo.getAiAgentName()+"-copy");
        aiAgentInfoVo.setAiAgentId(UUID.randomUUID().toString().replace("-", ""));
        aiAgentInfoVo.setIntentName(null);
        aiAgentInfoVo.setIntentionId(null);
        // 查询最新部署信息
        CrmAiAgentDeploy deploy = crmAiAgentDeployMapper.selectOne(
                new LambdaQueryWrapper<CrmAiAgentDeploy>()
                        .eq(CrmAiAgentDeploy::getAiAgentId, aiAgentId)
                        .orderByDesc(CrmAiAgentDeploy::getModifyTime)
                        .last("LIMIT 1")
        );

        if(Objects.nonNull(deploy)) {
            aiAgentInfoVo.setAiAgentInfo(crmAiAgentDeployService.getJsonObject(deploy));
        }else{
            aiAgentInfoVo.setAiAgentInfo(null);
        }
        List<CrmAiAgentVariable> crmAiAgentVariableList = crmAiAgentVariableMapper.selectList(new LambdaQueryWrapper<CrmAiAgentVariable>()
                .eq(CrmAiAgentVariable::getAiAgentId, aiAgentId)
                .eq(CrmAiAgentVariable::getDataStatus, 1));

        if (!crmAiAgentVariableList.isEmpty()) {
            crmAiAgentVariableList.forEach(variable -> {
                variable.setAiAgentId(aiAgentInfoVo.getAiAgentId());
                variable.setVariableId(UUID.randomUUID().toString().replace("-", ""));
            });
            crmAiAgentVariableService.saveBatch(crmAiAgentVariableList);
        }
        generateDefaultVariables(crmAiAgentInfo.getAiAgentId());

        return AjaxResult.ok(aiAgentInfoVo);
    }

    @Override
    public AjaxResult<AiAgentInfoVo> createDefaultAndEnsure(String companyId) {
        // 遍历所有渠道类型
        for (AiAgentChannelTypeEnum channelEnum : AiAgentChannelTypeEnum.values()) {
            if (channelEnum == AiAgentChannelTypeEnum.CHAT) {
                createAiAgentIfNotExists(companyId, 2, channelEnum.getCode());  // 欢迎语智能体
                createAiAgentIfNotExists(companyId, 3, channelEnum.getCode());  // 保底智能体
            } else {
                createAiAgentIfNotExists(companyId, 3, channelEnum.getCode());  // 其他渠道类型仅保底智能体
            }
        }

        return AjaxResult.ok();
    }

    @Override
    public void updateChannel(String aiAgentId, String s) {
        CrmAiAgentInfo crmAiAgentInfo = new CrmAiAgentInfo();
        crmAiAgentInfo.setAiAgentId(aiAgentId);
        crmAiAgentInfo.setChannelIds(s);
        this.updateById(crmAiAgentInfo);
    }

    @Override
    public void updateName(String aiAgentId, String name) {
        CrmAiAgentInfo crmAiAgentInfo = new CrmAiAgentInfo();
        crmAiAgentInfo.setAiAgentId(aiAgentId);
        crmAiAgentInfo.setAiAgentName(name);
        generateDefaultVariables(crmAiAgentInfo.getAiAgentId());

        this.updateById(crmAiAgentInfo);
    }

    @Override
    public void updateIntentId(String aiAgentId, String intentId) {
        CrmAiAgentInfo crmAiAgentInfo = new CrmAiAgentInfo();
        crmAiAgentInfo.setAiAgentId(aiAgentId);
        crmAiAgentInfo.setIntentionId(intentId);
        this.updateById(crmAiAgentInfo);
    }

    private void createAiAgentIfNotExists(String companyId, int aiAgentType, int channelType) {
        LambdaQueryWrapper<CrmAiAgentInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAiAgentInfo::getCompanyId, companyId)
                .eq(CrmAiAgentInfo::getDataStatus, 1)
                .eq(CrmAiAgentInfo::getAiAgentType, aiAgentType)
                .eq(CrmAiAgentInfo::getChannelTypeId, channelType);

        Long count = this.crmAiAgentInfoMapper.selectCount(queryWrapper);
        if (count == 0) {
            CrmAiAgentInfo crmAiAgentInfo = new CrmAiAgentInfo();
            crmAiAgentInfo.setDataStatus(0);
            crmAiAgentInfo.setAiAgentType(aiAgentType);
            crmAiAgentInfo.setAiAgentStatus(0);
            crmAiAgentInfo.setChannelTypeId(channelType);
            crmAiAgentInfo.setCreateTime(new Date());
            crmAiAgentInfo.setCompanyId(companyId);
            crmAiAgentInfo.setAiAgentId(UUID.randomUUID().toString().replace("-", ""));
            // 设置默认名称
            if (aiAgentType == 2 ) {
                crmAiAgentInfo.setAiAgentName("Default welcome message");
            } else if (aiAgentType == 3) {
                crmAiAgentInfo.setAiAgentName("Default Aiagent");
            }

            this.crmAiAgentInfoMapper.insert(crmAiAgentInfo);
            generateDefaultVariables(crmAiAgentInfo.getAiAgentId());
        }
    }

    public AjaxResult<IPage<AiAgentInfoVo>> queryAgentInfoPages(IPage<Object> pageParam, AiAgentInfoVo agentInfoVo) {
        if (agentInfoVo == null) {
            agentInfoVo = new AiAgentInfoVo();
        }
        agentInfoVo.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
        IPage<AiAgentInfoVo> resultPage = this.baseMapper.selectPages(new Page<>(pageParam.getCurrent(), pageParam.getSize()), agentInfoVo);

        // 遍历查询到的结果，并查询部署信息
        resultPage.getRecords().forEach(vo -> {
            if(vo.getAiAgentType()==2||vo.getAiAgentType()==3){
                vo.setAiAgentName(TransUtil.trans(vo.getAiAgentName()));
            }
            // 查询所有部署信息
            LambdaQueryWrapper<CrmAiAgentDeploy> deployWrapper = new LambdaQueryWrapper<CrmAiAgentDeploy>()
                    .eq(CrmAiAgentDeploy::getAiAgentId, vo.getAiAgentId())
                    .orderByDesc(CrmAiAgentDeploy::getCreateTime);

            List<CrmAiAgentDeploy> crmAiAgentDeploy = crmAiAgentDeployMapper.selectList(deployWrapper);

            boolean isDeployed = crmAiAgentDeploy.stream()
                    .anyMatch(deploy -> deploy.getDeployStatus() == 1);

            vo.setIsDeploy(isDeployed);

            CrmAiAgentDeployVo crmAiAgentDeployVo = new CrmAiAgentDeployVo();

            if (!crmAiAgentDeploy.isEmpty()) {

                BeanUtils.copyProperties(crmAiAgentDeploy.get(0), crmAiAgentDeployVo);
                vo.setDeployStatus(crmAiAgentDeploy.get(0).getDeployStatus());
            } else {
                crmAiAgentDeployVo.setDeployStatus(0);
                vo.setDeployStatus(0);
            }
            vo.setCrmAiAgentDeployVo(crmAiAgentDeployVo);

            String index = Constants.AI_AGENT_EXEC_LOG_INDEX + SecurityUtil.getLoginUser().getCompanyId();
            boolean indexExists = headIndexExists(index);

            if (indexExists) {
                try {
                    SearchRequest searchRequest = new SearchRequest(index);
                    SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

                    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                    boolQuery.must(QueryBuilders.termQuery("aiAgentId", vo.getAiAgentId()));
                    boolQuery.must(QueryBuilders.termQuery("isTest", false));

                    searchSourceBuilder.query(boolQuery);
                    

                    // 按 aiAgentExecId 聚合
                    TermsAggregationBuilder aggregationBuilder = AggregationBuilders
                            .terms("group_by_aiAgentExecId")
                            .field("aiAgentExecId.keyword")
                            .size(10000);

                    searchSourceBuilder.aggregation(aggregationBuilder);
                    searchRequest.source(searchSourceBuilder);

                    SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

                    Terms terms = searchResponse.getAggregations().get("group_by_aiAgentExecId");

                    int totalUsageCount = terms.getBuckets().size();
                    vo.setAgentUsageCount(totalUsageCount);

                } catch (IOException e) {
                    log.error("查询智能体使用次数异常", e);
                    vo.setAgentUsageCount(0);
                }
            } else {
                vo.setAgentUsageCount(0);
            }


        });

        return AjaxResult.ok(resultPage);
    }

    /**
     * 查询公司配置的对应的welcome智能体,并执行
     * @return
     */
    private AiAgent getWelcomeAiAgent(AiAgentExecRequest aiAgentExecRequest){
        // 查询公司配置的对应的welcome智能体
        CrmAiAgentInfo welcomeAiAgentInfo = crmAiAgentInfoMapper.getAiAgentInfo(aiAgentExecRequest.getCompanyId(), AiAgent.AI_AGENT_TYPE_WELCOME, aiAgentExecRequest.getChannelTypeId());
        // 执行welcome智能体
        if(welcomeAiAgentInfo != null){
            // 先创建欢迎语智能体,再创建会话
            return initAiAgent(welcomeAiAgentInfo, aiAgentExecRequest);
        }
        return null;
    }

    /**
     * 查询公司配置的对应的保底智能体,并执行
     * @return
     */
    private AiAgent getFallbackAiAgent(AiAgentExecRequest aiAgentExecRequest){
        // 查询公司配置的对应的保底智能体
        CrmAiAgentInfo fallbackAiAgentInfo = crmAiAgentInfoMapper.getAiAgentInfo(aiAgentExecRequest.getCompanyId(), AiAgent.AI_AGENT_TYPE_FALLBACK, aiAgentExecRequest.getChannelTypeId());
        // 执行保底智能体
        if(fallbackAiAgentInfo != null){
            // 先创建保底智能体,再创建会话
            AiAgent fallbackAiAgent = initAiAgent(fallbackAiAgentInfo, aiAgentExecRequest);
            return fallbackAiAgent;
        }
        return null;
    }

    /**
     * 所有智能体初始化都走这
     * @param aiAgentInfo
     * @param aiAgentExecRequest
     * @return
     */
    private AiAgent initAiAgent(CrmAiAgentInfo aiAgentInfo,AiAgentExecRequest aiAgentExecRequest){
        // 获取当前智能体部署的json
        JSONObject deployJson;
        // 测试直接用部署的版本
        if(StringUtil.isNotEmpty(aiAgentExecRequest.getDeployId())){
            deployJson = crmAiAgentDeployService.getDeployDetails(aiAgentExecRequest.getDeployId());
        } else {
            // 正式用aiagent的版本
            deployJson = crmAiAgentDeployService.getDeployJson(aiAgentInfo.getAiAgentId());
        }
        List<FlowNode> flowNodes = new ArrayList<>();
        for (int i = 0; i < deployJson.getJSONArray("nodesList").size(); i++) {
            JSONObject nodeObject = deployJson.getJSONArray("nodesList").getJSONObject(i);
            JSONObject dataObject = nodeObject.getJSONObject("data");
            FlowNode flowNode = new FlowNode(dataObject);
            flowNodes.add(flowNode);
        }
        // 所有node存到redis
        for (FlowNode flowNode : flowNodes) {
            RedisCacheUtil.setCacheObject(Constants.AI_AGENT_NODE.concat(flowNode.getNodeId()), flowNode);
        }

        // 获取客户信息存储至内置客户信息变量字段
        CustomerInfoForAiAgent customerInfo = null;
        try {
            // 获取chat中的指纹id
            String fingerprintId = aiAgentExecRequest.getFingerprintId();
            // 获取社媒中的用户唯一信息
            String userContact = Optional.ofNullable(aiAgentExecRequest.getDefinedAttr())
                    .map(DefinedAttr::getUserContact)
                    .orElse(null);
            R<CustomerInfoForAiAgent> customerInfoForAiAgent = customerClient.getCustomerInfoForAiAgent(aiAgentExecRequest.getCompanyId(), Integer.valueOf(aiAgentExecRequest.getChannelId()), fingerprintId, userContact);
            log.info("AIAgent初始化变量 - 客户信息：{}", JSONObject.toJSONString(customerInfoForAiAgent));
            // 如果请求成功
            if (customerInfoForAiAgent.getCode() == R.SUCCESS) {
                customerInfo = customerInfoForAiAgent.getData();
                log.info("AIAgent初始化变量 - 获取内置变量的客户信息成功：{}", JSONObject.toJSONString(customerInfo));
            }
        } catch (Exception e) {
            log.info("AIAgent初始化变量 - 获取内置变量的客户信息异常：", e);
        }

        AiAgent aiAgent = new AiAgent(aiAgentInfo, aiAgentExecRequest,flowNodes.get(0));

        // 内置变量赋值
        List<AiAgentVariableVo> crmAiAgentVariableList = crmAiAgentVariableService.getVariableInfo(aiAgentInfo.getAiAgentId(), null, aiAgentExecRequest.getCompanyId());
        for(AiAgentVariableVo crmAiAgentVariable : crmAiAgentVariableList){
            // 先设置变量类型
            // 变量类型（1.系统内置变量，2当前会话变量，3当前智能体变量，4全局变量）
            if(crmAiAgentVariable.getVariableType() == 1){
                // 系统内置变量
                String variableName = AiAgent.SYSTEM_VARIABLES_PREFIX.concat(crmAiAgentVariable.getVariableName());
                String defaultValue = crmAiAgentVariable.getDefaultValue();
//                if (customerInfo != null) {
//                    // 匹配客户信息的内置变量并赋值
//                    defaultValue = resolveSystemVariableValue(crmAiAgentVariable.getVariableName(), customerInfo);
//                }
                aiAgent.getVariableTypeMap().put(variableName, crmAiAgentVariable.getDataType());
                aiAgent.updateVariable(variableName, defaultValue);
            }else if(crmAiAgentVariable.getVariableType() == 2){
                // 当前会话变量
                String variableName = AiAgent.SESSION_VARIABLES_PREFIX.concat(crmAiAgentVariable.getVariableName());
                aiAgent.getVariableTypeMap().put(variableName, crmAiAgentVariable.getDataType());
                aiAgent.updateVariable(variableName, crmAiAgentVariable.getDefaultValue());
            }else if(crmAiAgentVariable.getVariableType() == 3){
                // 当前智能体变量
                String variableName = AiAgent.AI_AGENT_VARIABLES_PREFIX.concat(crmAiAgentVariable.getVariableName());
                aiAgent.getVariableTypeMap().put(variableName, crmAiAgentVariable.getDataType());
                aiAgent.updateVariable(variableName, crmAiAgentVariable.getDefaultValue());
            }else if(crmAiAgentVariable.getVariableType() == 4){
                // 全局变量
                String variableName = AiAgent.GLOBAL_VARIABLES_PREFIX.concat(crmAiAgentVariable.getVariableName());
                aiAgent.getVariableTypeMap().put(variableName, crmAiAgentVariable.getDataType());
                aiAgent.updateVariable(variableName, crmAiAgentVariable.getDefaultValue());
            }
        }
        R<CrmChannelConfigVO> channelConfig = channelClient.queryChannelConfig(aiAgentExecRequest.getSourceChannelId());
       String chanelName="";
        if (channelConfig.getCode() == AjaxResult.SUCCESS) {
            chanelName=channelConfig.getData().getName();
        }        // 初始化传参变量
        aiAgent.initVariables(aiAgentExecRequest.getDefinedAttr(), aiAgentExecRequest.getCustomerAttr(),crmAiAgentVariableList,customerInfo, aiAgentExecRequest.getChannelId(), aiAgentExecRequest.getSourceChannelId(),chanelName);
        return aiAgent;
    }

    // 解析系统内置变量的值
    private String resolveSystemVariableValue(String variableName, CustomerInfoForAiAgent customerInfo) {
        switch (variableName) {
            case "customerTags": // 客户标签
                List<CustomerInfoForTagsByCustomerId> tags = customerInfo.getCustomerTags();
                log.info("customerTags -> {}",  JSONObject.toJSONString(tags));
                if (tags != null) {
                    // 获取tagContent字段组成列表并转JSONString
                    List<String> tagContents = tags.stream()
                            .map(CustomerInfoForTagsByCustomerId::getTagContent)
                            .collect(Collectors.toList());
                    return JSONArray.toJSONString(tagContents);
                }
                break;
            case "customerPreferredLanguage": // 客户偏好语言
                return safeTrim(customerInfo.getCustomerPreferredLanguage());
            case "customerEmailAddress": // 客户邮箱
                return safeTrim(customerInfo.getCustomerEmailAddress());
            case "customerPhoneNumber": // 客户电话
                return safeTrim(customerInfo.getTelephonePrefixId()) + safeTrim(customerInfo.getTelephone());
            case "customerCountry": // 客户国家
                return customerInfo.getCustomerCountryName();
            case "customerGrade": // 客户等级
                return customerInfo.getCustomerGradeName();
        }
        return "";
    }
    // 安全 trim 方法
    private String safeTrim(String input) {
        return input != null ? input.trim() : "";
    }

    /**
     * 保存session到redis
     * @param aiAgent
     */
    private void updateSessionAiAgent(SessionAiAgent sessionAiAgent, AiAgent aiAgent){
        //log.info("updateSessionAiAgent 更新 sessionAiAgent 上下文 :{}", sessionAiAgent.getSessionContexts());
        // 会话加进去新的aiagent
        if(sessionAiAgent.getAiAgents() == null){
            sessionAiAgent.setAiAgents(new ArrayList<>());
        }
        List<AiAgent> aiAgents = sessionAiAgent.getAiAgents();
        // 创建一个新的 ArrayList 并将原列表元素复制进去
        List<AiAgent> mutableAiAgents = new ArrayList<>(aiAgents);
        // 向新列表中添加 intentAiAgent
        mutableAiAgents.add(aiAgent);
        // 将更新后的列表设置回 sessionAiAgent
        sessionAiAgent.setAiAgents(mutableAiAgents);
        RunContext runContext = aiAgent.lastUserInput();
        if (runContext != null) {
            aiAgent.addUserInput(runContext.getInput(),runContext.getInputType(),runContext.getRole());
        }

        // 会话上下文加进去新的aiagent
//        sessionAiAgent.setSessionContexts(Optional.ofNullable(aiAgent.getRunContexts()).orElse(new ArrayList<>()));
        // 会话上下文加进去新的aiagent
        String sessionKey = Constants.AI_AGENT_SESSION.concat(sessionAiAgent.getSessionId());
        RedisCacheUtil.setCacheObject(sessionKey, sessionAiAgent);
    }

    /**
     * 创建session到redis
     */
    private void createSessionAiAgent(SessionAiAgent sessionAiAgent){
        // 创建session
        String sessionKey = Constants.AI_AGENT_SESSION.concat(sessionAiAgent.getSessionId());
        RedisCacheUtil.setCacheObject(sessionKey, sessionAiAgent);
    }


    /**
     * 获取意图绑定的aiagent
     * @return
     */
    private AiAgent getIntentAiAgent(String intentId, AiAgentExecRequest aiAgentExecRequest, boolean test){
        CrmAiAgentInfo aiAgentInfo = null;
        if(test){
            aiAgentInfo = aiAgentIntentService.getAiAgentInfo(intentId, AiAgent.AI_AGENT_TYPE_NORMAL, aiAgentExecRequest.getChannelTypeId());
        } else {
            aiAgentInfo = aiAgentIntentService.getAiAgentInfo(intentId, AiAgent.AI_AGENT_TYPE_NORMAL, aiAgentExecRequest.getChannelTypeId(),aiAgentExecRequest.getChannelId());
        }
        log.info("获取意图绑定的aiagent：{}", aiAgentInfo);
        if(aiAgentInfo != null){
            return initAiAgent(aiAgentInfo,aiAgentExecRequest);
        }
        return null;
    }
    

    /**
     * 创建工单，发mq
     */
    private void createTicket(AiAgent aiAgent){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("workOrderId", aiAgent.getSessionId());
        jsonObject.put("channelId", aiAgent.getSourceChannelId());
        jsonObject.put("channelTypeId", aiAgent.getChannelId());
        jsonObject.put("autoType", 1);
        jsonObject.put("chatVoice", 0);
        jsonObject.put("companyId", aiAgent.getCompanyId());
        jsonObject.put("customerContactInfo", aiAgent.getSystemVariables().get("userContact"));
        jsonObject.put("chatUserName", aiAgent.getSystemVariables().get("userName"));
        jsonObject.put("memberId", aiAgent.getSystemVariables().get("userId"));
        jsonObject.put("chatMember", aiAgent.getSystemVariables().get("userId"));
        jsonObject.put("phoneNumber", aiAgent.getSystemVariables().get("userMobile"));
        rabbitTemplate.convertAndSend(RabbitMqConstants.SEND_MESSAGE_EXCHANGE, RabbitMqConstants.KNOWLEDGE_AUTO_CREATE_TICKET_ROUTING_KEY, jsonObject.toJSONString());
    }

        /**
     * 创建工单，发mq
     */
    private void createTicket(AiAgentExecRequest aiAgentExecRequest){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("workOrderId", aiAgentExecRequest.getSessionId());
        jsonObject.put("channelId", aiAgentExecRequest.getSourceChannelId());
        jsonObject.put("channelTypeId", aiAgentExecRequest.getChannelId());
        jsonObject.put("autoType", 1);
        jsonObject.put("chatVoice", 0);
        jsonObject.put("companyId", aiAgentExecRequest.getCompanyId());
        jsonObject.put("fingerprintId", aiAgentExecRequest.getFingerprintId());

        if(aiAgentExecRequest.getDefinedAttr() != null){
            String customerContactInfo = aiAgentExecRequest.getDefinedAttr().getUserContact();
            String chatUserName = aiAgentExecRequest.getDefinedAttr().getUserName();
            String memberId = aiAgentExecRequest.getDefinedAttr().getUserId();
            String chatMember = aiAgentExecRequest.getDefinedAttr().getUserId();
            String phoneNumber = aiAgentExecRequest.getDefinedAttr().getUserMobile();
            jsonObject.put("customerContactInfo", customerContactInfo);
            jsonObject.put("chatUserName", chatUserName);
            jsonObject.put("memberId", memberId);
            jsonObject.put("chatMember", chatMember);
            jsonObject.put("phoneNumber", phoneNumber);
        } else {
            jsonObject.put("customerContactInfo", null);
            jsonObject.put("chatUserName", null);
            jsonObject.put("memberId", null);
            jsonObject.put("chatMember", null);
            jsonObject.put("phoneNumber", null);
        }
        rabbitTemplate.convertAndSend(RabbitMqConstants.SEND_MESSAGE_EXCHANGE, RabbitMqConstants.KNOWLEDGE_AUTO_CREATE_TICKET_ROUTING_KEY, jsonObject.toJSONString());
    }




    private String determineDataType(Class<?> fieldType) {
        if (fieldType == String.class) {
            return "string";
        } else if (fieldType == int.class || fieldType == Integer.class) {
            return "int";
        } else if (fieldType == long.class || fieldType == Long.class) {
            return "long";
        } else if (fieldType == boolean.class || fieldType == Boolean.class) {
            return "boolean";
        } else if (fieldType == double.class || fieldType == Double.class) {
            return "double";
        } else if (fieldType == float.class || fieldType == Float.class) {
            return "float";
        } else if (fieldType == JSON.class || fieldType == JSONObject.class) {
            return "JSON";
        } else if (fieldType == List.class) {
            return "LIST";
        }
        return "string";
    }

    /**
     * 复制aiagent
     */
    private AiAgent copyAiAgent(AiAgent beforeAiAgent, AiAgent afterAiAgent){
        if(beforeAiAgent == null){
            return afterAiAgent;
        }
//        afterAiAgent.setRunContexts(beforeAiAgent.getRunContexts());
        afterAiAgent.setSessionVariables(beforeAiAgent.getSessionVariables());
//        afterAiAgent.setSystemVariables(beforeAiAgent.getSystemVariables());
        afterAiAgent.setGlobalVariables(beforeAiAgent.getGlobalVariables());
        return afterAiAgent;
    }

    /**
     * 意图log
     */
   private void intentLog(String intentId, AiAgent aiAgent){
       JSONObject jsonObject = new JSONObject();
       RunContext runContext = aiAgent.lastUserInput();
       jsonObject.put("intentId", intentId);
       jsonObject.put("sessionId", aiAgent.getSessionId());
       jsonObject.put("companyId", aiAgent.getCompanyId());
    //    jsonObject.put("intentName", aiAgent.getIntentName());
    //    jsonObject.put("language", jsonObject.getString("language"));
    //    jsonObject.put("scriptType", jsonObject.getString("scriptType"));
       jsonObject.put("input", runContext.getInput());
       jsonObject.put("inputRole", runContext.getRole());
       jsonObject.put("inputType", runContext.getInputType());
       log.info("保存意图日志 intentLog{}",jsonObject);
       rabbitTemplate.convertAndSend(RabbitMqConstants.AI_AGENT_INTENT_LOG_EXCHANGE, RabbitMqConstants.AI_AGENT_INTENT_LOG_ROUTING_KEY, jsonObject.toJSONString());
   }

   /**
     * 在意图识别中的，回复工单，发mq
     */
    private void replyTicket(RabbitTemplate rabbitTemplate, AiAgentExecRequest aiAgentExecRequest,RunContext runContext, int replyType, JSONObject audioContent){
        String input = runContext.getInput();
        String inputType = runContext.getInputType();
        // 处理表单数据
        if (replyType == 2 && runContext.getInputType().equals("4")) {
            inputType = "7";
            JSONObject jsonObject = JSON.parseObject(input);
            // 表单取消情况下，不记录工单
            if (jsonObject.getString("operate").equals("cancle")){
                return;
            }
//            JSONObject jsonObject = JSON.parseObject(input);
//            if (jsonObject.getString("operate").equals("submit")) {
//                JSONArray submitList = jsonObject.getJSONArray("formList");
//                StringBuilder sb = new StringBuilder();
//                for (int i = 0; i < submitList.size(); i++) {
//                    JSONObject from = submitList.getJSONObject(i);
//                    String key = from.getString("valueName");
//                    String value = from.getString("value");
//                    sb.append("**").append(key).append("**: ").append(value).append("  \n  ");
//                }
//                input = sb.toString();
//            } else {
//                input = "cancel";
//            }
        }


        JSONObject questionReplyJson = new JSONObject();

        // 如果 audioContent 不为空表示是音频输入内容。
        if (null != audioContent) {
            // content JSON格式为{"content": "你好", "fileName": "https://fileUrl.mp4"}
            questionReplyJson.put("content", JSONObject.toJSONString(audioContent));
            questionReplyJson.put("contentType", "6");
        } else {
            questionReplyJson.put("content", input);
            questionReplyJson.put("contentType", inputType);
        }
        // 选中卡片
        if(inputType.equals("6")){
            questionReplyJson.put("content", aiAgentExecRequest.getInput());
            questionReplyJson.put("contentType", "9");
        }

        questionReplyJson.put("desAddress", aiAgentExecRequest.getDefinedAttr().getUserContact());
        questionReplyJson.put("chatUserName", aiAgentExecRequest.getDefinedAttr().getUserName());
        questionReplyJson.put("channelId", aiAgentExecRequest.getSourceChannelId());
        questionReplyJson.put("workRecordId", aiAgentExecRequest.getSessionId());
        questionReplyJson.put("replyType", replyType);
        questionReplyJson.put("memberId", aiAgentExecRequest.getDefinedAttr().getUserId());
        questionReplyJson.put("chatMember", aiAgentExecRequest.getDefinedAttr().getUserId());
        questionReplyJson.put("replyTime", new Date());
        questionReplyJson.put("phoneNumber", aiAgentExecRequest.getDefinedAttr().getUserMobile());
        questionReplyJson.put("channelTypeId", aiAgentExecRequest.getChannelId());
        questionReplyJson.put("companyId", aiAgentExecRequest.getCompanyId());
        questionReplyJson.put("contentId", runContext.getMsgId());


        rabbitTemplate.convertAndSend(RabbitMqConstants.SEND_MESSAGE_EXCHANGE, RabbitMqConstants.KNOWLEDGE_AUTO_REPLY_TICKET_ROUTING_KEY, questionReplyJson.toJSONString());
    }

       /**
     * 意图log
     */
    private void intentLog(String intentId, RunContext runContext, String sessionId, String companyId){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("intentId", intentId);
        jsonObject.put("sessionId", sessionId);
        jsonObject.put("companyId", companyId);
     //    jsonObject.put("intentName", aiAgent.getIntentName());
     //    jsonObject.put("language", jsonObject.getString("language"));
     //    jsonObject.put("scriptType", jsonObject.getString("scriptType"));
        jsonObject.put("input", runContext.getInput());
        jsonObject.put("inputRole", runContext.getRole());
        jsonObject.put("inputType", runContext.getInputType());
        rabbitTemplate.convertAndSend(RabbitMqConstants.AI_AGENT_INTENT_LOG_EXCHANGE, RabbitMqConstants.AI_AGENT_INTENT_LOG_ROUTING_KEY, jsonObject.toJSONString());
    }

    /**
     * 发送识别出意图的mq
     */
    private void sendIntentRecognitionMq(String intentId, String intentName, String aiAgentId, String aiAgentName, SessionAiAgent sessionAiAgent){
        // 
        JSONObject jsonObject = new JSONObject();
        String msgId = "";
        if(sessionAiAgent.firstIntentUserInput() == null){
            msgId = sessionAiAgent.lastUserInput().getMsgId();
        } else {
            msgId = sessionAiAgent.firstIntentUserInput().getMsgId();
        }
        log.info("发送识别出意图的mq:{}", msgId);
        jsonObject.put("contentId", msgId);
        jsonObject.put("companyId", sessionAiAgent.getCompanyId());
        jsonObject.put("incomingIntentId", intentId);
        jsonObject.put("incomingIntentName", intentName);
        jsonObject.put("intelligentAgentId", aiAgentId);
        jsonObject.put("intelligentAgentName", aiAgentName);
        jsonObject.put("sessionId", sessionAiAgent.getSessionId());
        rabbitTemplate.convertAndSend(RabbitMqConstants.SEND_MESSAGE_EXCHANGE,RabbitMqConstants.KNOWLEDGE_REPLY_TICKET_UPDATE_ROUTING_KEY, jsonObject.toJSONString());
    }

    /**
     *  验证索引是否存在
     * @param index 索引名称
     * @return 存在状态
     * @throws IOException
     */
    private boolean headIndexExists(String index){
        try {
            GetIndexRequest req = new GetIndexRequest(index);
            boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
            log.info("当前索引{}, 是否存在: {}", index, exists);
            return exists;
        }catch (Exception e){
            log.error("验证索引失败:",e);
        }
        return false;
    }


    /**
     * 记录用户输入到工单和上下文中
     *  逻辑：
     *      1. 根据用户输入类型执行对应处理。
     *         5为音频输入，执行音频转录 -> 存储S3 -> 获取S3文件信息，然后发送工单消息MQ
     *         其他类型为原有逻辑。
     *
     * @param aiAgentExecRequest 请求参数
     * @return Run上下文对象
     */
    private RunContext recordUserInputToTicket(AiAgentExecRequest aiAgentExecRequest) {
        RunContext runContext = new RunContext();

        String input = aiAgentExecRequest.getInput();
        String inputType = aiAgentExecRequest.getInputType();
        String companyId = aiAgentExecRequest.getCompanyId();
//        List<AnswerContentFile> answerContentFileList = new ArrayList<>();
        JSONObject audioContent = null;
        // 如果是音频输入信息
        if ("5".equals(inputType)) {
            // 执行转录操作
            Map<String, Object> fileInfoMap = this.handleAudioInput(input, aiAgentExecRequest);
            log.info("记录用户输入到工单和上下文中 - AIAgent处理音频输入结果：{}", fileInfoMap);

            if (null == fileInfoMap) {
                return null;
            }
            // 音频转录文本内容
            String transcriptionText = (String) fileInfoMap.get("transcriptionText");
            // 记录到上下文中。
            runContext.setInput(transcriptionText);
            runContext.setInputType(inputType);

            // 构建工单所需的音频类型字段。
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("url", fileInfoMap.get("fileUrl"));
            jsonObject.put("content", transcriptionText);
            audioContent = jsonObject;

//            // 工单附件信息
//            AnswerContentFile answerContentFile = new AnswerContentFile();
//            answerContentFile.setFileId(cn.hutool.core.lang.UUID.randomUUID().toString());
//            answerContentFile.setBucketName((String)fileInfoMap.get("bucketName"));
//            answerContentFile.setFileName((String)fileInfoMap.get("fileName"));
//            answerContentFile.setUrl((String)fileInfoMap.get("fileUrl"));
//            answerContentFile.setFileUrl((String)fileInfoMap.get("fileUrl"));
//            answerContentFile.setFileType(1);
//            answerContentFileList.add(answerContentFile);

        } else {
            // 非音频输入信息
            runContext.setInput(input);
            runContext.setInputType(inputType);
        }

        runContext.setRole(RunContext.ROLE_USER);
        runContext.setTimestamp(System.currentTimeMillis());
        runContext.setMsgId(cn.hutool.core.lang.UUID.randomUUID().toString());

        log.info("记录用户输入到工单和上下文中 - 发工单MQ：{}, 附件信息：{}", JSONObject.toJSONString(runContext) , JSONObject.toJSONString(audioContent));
        replyTicket(rabbitTemplate, aiAgentExecRequest, runContext, 2, audioContent);

        // 如果是音频输入信息
        if ("5".equals(inputType)) {
            // 转录后将音频输入类型 转换为 1文本类型
            runContext.setInputType("1");
        }
        return runContext;
    }

    /**
     * 处理音频处理
     * @param audioUrl  音频URL
     * @param companyId 公司ID
     * @return 转录后文本
     */
    private Map<String, Object> handleAudioInput(String audioUrl, AiAgentExecRequest aiAgentExecRequest) {
        if (audioUrl == null || !audioUrl.startsWith("http")) {
            log.info("AIAgent处理音频输入 - 无效的音频链接: {}", audioUrl);
            return null;
        }

        try {
            byte[] audioBytes = UtilsFunc.downloadFileFromUrl(audioUrl);
            if (audioBytes == null) {
                log.info("AIAgent处理音频输入 - 音频下载失败: {}", audioUrl);
                return null;
            }

            // 获取文件名 & contentType
            String fileName = UtilsFunc.extractFileNameFromUrl(audioUrl);
            String contentType = UtilsFunc.getMimeTypeFromExtension(fileName);
            log.info("AIAgent处理音频输入 - 文件名称：{}，文件类型：{}", fileName, contentType);
            // 构建 MultipartFile
            MultipartFile multipartFile = new MockMultipartFile("file", fileName, contentType, audioBytes);

            // 调用转录服务
            R transcribeResult = rescmgntClient.transcribeFile(multipartFile, aiAgentExecRequest.getCompanyId());
            log.info("AIAgent处理音频输入 - 音频转录结果：{}", JSONObject.toJSONString(transcribeResult));
            if (transcribeResult.getCode() != R.SUCCESS || transcribeResult.getData() == null) {
                log.info("AIAgent处理音频输入 - 音频转录失败");
                return null;
            }

            // 转录结果文本
            String transcriptionText = (String) transcribeResult.getData();

            // 将音频上传至S3桶内
            S3Utils instance = S3Utils.getInstance();
            Map<String, String> s3FileInfo = instance.uploadFileToS3(multipartFile);
            log.info("AIAgent处理音频输入 - 存储文件结果：{}", s3FileInfo);

            if (null == s3FileInfo) {
                log.info("AIAgent处理音频输入 - 存储文件失败");
                return null;
            }

            // 转录结果
            Map<String, Object> result = new HashMap<>();
            result.put("transcriptionText", transcriptionText);
            result.put("fileName", s3FileInfo.get("fileName"));
            result.put("fileUrl", s3FileInfo.get("url"));
            result.put("bucketName", s3FileInfo.get("bucketName"));
            //AIGC计费相关-- 在最后进行计费，防止代码异常导致计费成功
            InvokeServiceUtil instances = InvokeServiceUtil.getInstance();
            log.info("AIAgent音频转录 AIGC计费开始====");
            instances.sendBillingAlarm(aiAgentExecRequest.getCompanyId(),
                    aiAgentExecRequest.getChannelTypeId(),
                    aiAgentExecRequest.getChannelId(),
                    ChargeItemServiceEnum.AI_AGENT_TRANSCRIPTION_AUDIO);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.info("AIAgent处理音频输入 - 出现异常", e);
            return null;
        }
    }


        @Override
        public AjaxResult shareCodeInfo(String aiAgentId) {
            if (StringUtils.isEmpty(aiAgentId)) {
                return AjaxResult.failure("AI Agent ID不能为空");
            }

            try {
                String redisKeyPrefix = Constants.AI_AGENT_SHARE;
                String redisKey = redisKeyPrefix + aiAgentId;

                // 查询Redis是否存在未过期的分享码
                String codeInfo = RedisCacheUtil.getCacheObject(redisKey);

                AiAgentShareCodeVo shareCodeVo = null;
                JSONObject deployJson = new JSONObject();

                if (StringUtils.isNotEmpty(codeInfo)) {
                    // 存在未过期的分享码，解析数据
                    shareCodeVo = JSON.parseObject(codeInfo, AiAgentShareCodeVo.class);
                    log.info("找到AI Agent[{}]的现有分享码: {}", aiAgentId, shareCodeVo.getShareCode());
                } else {
                    // 不存在分享码，生成新的
                    String shareCode = generateRandomCode();

                    LocalDateTime now = LocalDateTime.now();
                    LocalDateTime expiryTime = now.plusHours(24);
                    String formattedExpiryTime = expiryTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                    shareCodeVo = new AiAgentShareCodeVo();
                    shareCodeVo.setShareCode(shareCode);
                    shareCodeVo.setExpiryTime(formattedExpiryTime);
                    shareCodeVo.setAiAgentId(aiAgentId);
                    shareCodeVo.setUserName(SecurityUtil.getUsername()==null?"":SecurityUtil.getUsername());
                    // 将分享码信息存入Redis，设置24小时过期
                    RedisCacheUtil.setCacheObject(redisKey, JSON.toJSONString(shareCodeVo), 24, TimeUnit.HOURS);

                    log.info("为AI Agent[{}]生成新的分享码: {}, 过期时间: {}", aiAgentId, shareCode, formattedExpiryTime);

                    deployJson = crmAiAgentDeployService.getDeployJson(aiAgentId);
                    if (deployJson == null) {
                        log.warn("AI Agent[{}]的部署JSON为空", aiAgentId);
                    } else {
                        log.info("成功获取AI Agent[{}]的部署JSON", aiAgentId);
                    }
                    upload(shareCodeVo, deployJson);
                }

                return AjaxResult.ok(shareCodeVo);
            } catch (Exception e) {
                log.error("获取AI Agent分享码出错", e);
                return AjaxResult.failure("获取分享码失败：" + e.getMessage());
            }
        }

        /**
         * 上传分享信息到S3
         *
         * @param shareCodeVo 分享码信息
         */
        private void upload(AiAgentShareCodeVo shareCodeVo, JSONObject deployJson) {
            try {
                // 构建要存储的分享内容
                Map<String, Object> shareContent = new HashMap<>();
                shareContent.put("shareCode", shareCodeVo.getShareCode());
                shareContent.put("aiAgentId", shareCodeVo.getAiAgentId());
                shareContent.put("expiryTime", shareCodeVo.getExpiryTime());
                // 将deployJson添加到shareContent中
                if (deployJson != null) {
                    shareContent.put("deployJson", deployJson);
                }
                // 转换为JSON字符串
                String jsonContent = JSON.toJSONString(shareContent);

                // 创建S3上传参数
                S3PutObjectParam objectParam = new S3PutObjectParam();
                objectParam.setBucketName(bucketName);
                objectParam.setObjectName(getObjectKey(shareCodeVo.getShareCode()));
                objectParam.setAccessKeyId(accessKey);
                objectParam.setSecretAccessKey(secretAccessKey);
                objectParam.setRegion(region);
                objectParam.setJson(jsonContent);
                log.info("请求S3参数:{}", objectParam);
                R result = s3Client.putJsonFile(objectParam, 5);

                if (result != null && result.getCode() == 200) {
                    log.info("成功上传分享码[{}]信息到S3", shareCodeVo.getShareCode());
                } else {
                    log.error("上传分享码[{}]信息到S3失败: {}", shareCodeVo.getShareCode(), result != null ? result.getMsg() : "未知错误");
                }
            } catch (Exception e) {
                log.error("上传分享码[{}]信息到S3出错", shareCodeVo.getShareCode(), e);
            }
        }

        /**
         * 生成分享对象的完整键名
         */
        private String getObjectKey(String shareCode) {
            return "shares/" + shareCode + ".json";
        }


    private static String generateRandomCode() {
        // 字符集：10个数字 + 26个大写字母
        String chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        StringBuilder sb = new StringBuilder(6);
        ThreadLocalRandom random = ThreadLocalRandom.current();

        for (int i = 0; i < 8; i++) {
            int index = random.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }

        return sb.toString();
    }

    /**
     * 根据分享码获取分享内容
     * @param shareCode 分享码
     * @return 分享内容
     */
    @Override
    public AjaxResult<JSONObject> getShareContent(String shareCode) {
        if (StringUtils.isEmpty(shareCode)) {
            return AjaxResult.failure("分享码不能为空");
        }

        try {
            // 从Redis缓存获取
            JSONObject deployJson = null;

            // 构建S3获取对象参数
            S3GetObjectParam s3GetObjectParam = new S3GetObjectParam();
            s3GetObjectParam.setBucketName(bucketName);
            s3GetObjectParam.setObjectName(getObjectKey(shareCode));
            s3GetObjectParam.setAccessKeyId(accessKey);
            s3GetObjectParam.setSecretAccessKey(secretAccessKey);
            s3GetObjectParam.setRegion(region);

            log.info("请求S3参数:{}", s3GetObjectParam);
            // 从S3获取分享内容
            R result = null;
            try {
                result = s3Client.getBucketObject(s3GetObjectParam);
                if (result == null) {
                    return AjaxResult.failure("无效分享码");
                }

            } catch (Exception e) {
                log.error("获取S3对象发生未知异常: 分享码: {}", shareCode, e);

                return AjaxResult.failure("无效分享码");
            }

            String jsonContent = (String) result.getData();
            if (StringUtils.isEmpty(jsonContent)) {
                return AjaxResult.failure("获取分享内容失败: S3返回的数据为空");
            }

            // 解析JSON内容
            Map<String, Object> shareContent = JSON.parseObject(jsonContent, Map.class);

            // 检查分享内容是否包含必要的字
            if (!shareContent.containsKey("expiryTime")) {
                return AjaxResult.failure("分享内容格式错误: 缺少过期时间");
            }

            if (!shareContent.containsKey("deployJson")) {
                return AjaxResult.failure("分享内容不包含部署信息");
            }

            // 检查是否过期
            String expiryTimeStr = (String) shareContent.get("expiryTime");
            LocalDateTime expiryTime = LocalDateTime.parse(expiryTimeStr,
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            if (LocalDateTime.now().isAfter(expiryTime)) {
                log.info("分享码[{}]已过期，过期时间: {}", shareCode, expiryTimeStr);
                return AjaxResult.failure("分享码已过期");
            }


            Object deployJsonObj = shareContent.get("deployJson");

            if(deployJsonObj!=null) {
                deployJson = JSON.parseObject(JSON.toJSONString(deployJsonObj), JSONObject.class);
            }

            return AjaxResult.ok(deployJson);
        } catch (Exception e) {
            log.error("获取分享码[{}]内容出错", shareCode, e);
            return AjaxResult.failure("获取分享内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取智能体信息，根据绑定的系统手机号查询。
     *
     * @param companyId 公司ID。
     * @param systemPhone 系统手机号。
     * @return 智能体信息。
     */
    @Override
    public AjaxResult<AiAgentInfoVo> getAgentInfoBySystemPhone(String companyId,String systemPhone) {
        return AjaxResult.ok(baseMapper.selectAgentInfoBySystemPhone(companyId, systemPhone));
    }

    /**
     * 获取智能体信息
     *
     * @param aiAgentId
     * @return
     */
    @Override
    public CrmAiAgentInfo getAiAgentApiInfo(String aiAgentId) {
        return this.getById(aiAgentId);
    }


}
