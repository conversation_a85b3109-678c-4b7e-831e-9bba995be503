<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket实时音频测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .content {
            padding: 25px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .section h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 1.1em;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
        }

        .btn-danger {
            background: #e53e3e;
        }

        .btn-danger:hover {
            background: #c53030;
        }

        .btn-success {
            background: #38a169;
        }

        .btn-success:hover {
            background: #2f855a;
        }

        .status {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }

        .status-connected {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-disconnected {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-connecting {
            background: #fefcbf;
            color: #744210;
        }

        .timeout-notification {
            background: #fed7d7;
            border: 2px solid #e53e3e;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            color: #742a2a;
            font-weight: bold;
            animation: pulse-warning 2s infinite;
        }

        @keyframes pulse-warning {
            0% { background-color: #fed7d7; }
            50% { background-color: #fbb6ce; }
            100% { background-color: #fed7d7; }
        }

        .controls {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .recording-indicator {
            width: 12px;
            height: 12px;
            background: #e53e3e;
            border-radius: 50%;
            animation: pulse 1s infinite;
            margin-right: 8px;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .log-container {
            background: #1a202c;
            border-radius: 6px;
            padding: 15px;
            height: 250px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            color: #e2e8f0;
        }

        .log-entry {
            margin-bottom: 3px;
            padding: 2px 0;
        }

        .log-timestamp {
            color: #718096;
            margin-right: 8px;
        }

        .log-info { color: #63b3ed; }
        .log-success { color: #68d391; }
        .log-warning { color: #fbb040; }
        .log-error { color: #fc8181; }

        .audio-player {
            width: 100%;
            margin-top: 15px;
        }

        .token-input {
            font-family: monospace;
            font-size: 13px;
        }

        .audio-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 10px;
        }

        .volume-control {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .volume-slider {
            width: 100px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎙️ WebSocket实时音频测试</h1>
            <p>实时录音、WebSocket传输、TTS音频播放</p>
        </div>
        
        <div class="content">
            <!-- Token认证 -->
            <div class="section">
                <h3>🔐 认证配置</h3>
                <div class="form-group">
                    <label for="bearerToken">Bearer Token *</label>
                    <input type="password" id="bearerToken" class="token-input" placeholder="请输入Bearer认证令牌">
                </div>
                <div class="controls">
                    <button class="btn" onclick="testAuth()">🔍 测试认证</button>
                    <span id="authStatus"></span>
                </div>
            </div>

            <!-- WebSocket连接配置 -->
            <div class="section">
                <h3>🔗 WebSocket连接</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="wsUrl">WebSocket地址</label>
                        <input type="text" id="wsUrl" value="ws://10.200.3.163:30765/voice-duplex/">
                    </div>
                    <div class="form-group">
                        <label for="workOrderId">工单ID</label>
                        <input type="text" id="workOrderId" value="workorder_test_001">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="aiAgentId">智能体ID</label>
                        <input type="text" id="aiAgentId" value="ac19d983e9e747d6bb70010175493fe7">
                    </div>
                    <div class="form-group">
                        <label for="companyId">公司ID</label>
                        <input type="text" id="companyId" value="d6ff59a027861d7d4133e1d6b6872464">
                    </div>
                </div>
                <div class="controls">
                    <button class="btn" onclick="connectWebSocket()" id="connectBtn">🚀 连接WebSocket</button>
                    <button class="btn btn-danger" onclick="disconnectWebSocket()" id="disconnectBtn" disabled>🛑 断开连接</button>
                    <span id="wsStatus" class="status status-disconnected">未连接</span>
                </div>
            </div>

            <!-- 音频录制控制 -->
            <div class="section">
                <h3>🎤 实时音频录制</h3>
                <div class="controls">
                    <button class="btn btn-success" onclick="toggleRecording()" id="recordBtn" disabled>🎤 开始录音</button>
                    <span id="recordingIndicator" class="recording-indicator" style="display: none;"></span>
                    <span id="recordingStatus">未录音</span>
                </div>
                <div class="audio-controls">
                    <div class="volume-control">
                        <label>录音音量:</label>
                        <input type="range" id="volumeSlider" class="volume-slider" min="0" max="100" value="50">
                        <span id="volumeValue">50%</span>
                    </div>
                </div>
            </div>

            <!-- 音频播放 -->
            <div class="section">
                <h3>🔊 TTS音频播放</h3>
                <audio id="audioPlayer" class="audio-player" controls style="display: none;"></audio>
                <div id="audioInfo" style="margin-top: 10px; font-size: 13px; color: #666;">
                    等待接收TTS音频数据...
                </div>
                <div style="margin-top: 10px;">
                    <button class="btn" onclick="testAudioPlayback()" id="testAudioBtn">🧪 测试音频播放</button>
                    <button class="btn" onclick="testSSEConnection()" id="testSSEBtn">📡 测试SSE连接</button>
                    <button class="btn btn-danger" onclick="clearAudioQueue()" id="clearQueueBtn">🗑️ 清空队列</button>
                    <span style="font-size: 12px; color: #888; margin-left: 10px;">测试和控制音频播放功能</span>
                </div>
            </div>

            <!-- 实时日志 -->
            <div class="section">
                <h3>📋 实时日志</h3>
                <div class="log-container" id="logContainer"></div>
                <div style="margin-top: 10px;">
                    <button class="btn" onclick="clearLog()">🗑️ 清空日志</button>
                    <button class="btn" onclick="exportLog()">📄 导出日志</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const API_BASE = '/wsbridge/test';
        let wsClientId = null;
        let audioStreamEventSource = null;
        let mediaRecorder = null;
        let audioContext = null;
        let isRecording = false;
        let recordingStream = null;
        let audioProcessor = null;
        let currentAudio = null;

        // 音频片段播放相关变量
        let audioQueue = [];
        let isPlayingQueue = false;
        let currentPlayingIndex = 0;
        let audioPlaybackContext = null;

        // 日志函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span>${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            addLog('📋 日志已清空', 'info');
        }

        function exportLog() {
            const logContainer = document.getElementById('logContainer');
            const logText = logContainer.innerText;
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `websocket-audio-log-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            addLog('📄 日志已导出', 'success');
        }

        // 认证相关函数
        function getAuthHeaders() {
            const token = document.getElementById('bearerToken').value.trim();
            if (!token) {
                throw new Error('请先输入Bearer Token');
            }
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
        }

        async function testAuth() {
            const authStatus = document.getElementById('authStatus');
            try {
                const headers = getAuthHeaders();
                authStatus.textContent = '🔄 测试中...';
                authStatus.style.color = '#f39c12';

                // 使用连接接口测试认证
                const response = await fetch(`${API_BASE}/connect`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({ test: true })
                });

                if (response.status === 401) {
                    authStatus.textContent = '❌ 认证失败';
                    authStatus.style.color = '#e74c3c';
                    addLog('🔐 Bearer Token认证失败 - 401未授权', 'error');
                    return false;
                } else if (response.ok) {
                    authStatus.textContent = '✅ 认证成功';
                    authStatus.style.color = '#27ae60';
                    addLog('🔐 Bearer Token认证测试成功', 'success');
                    return true;
                } else {
                    authStatus.textContent = '⚠️ 认证异常';
                    authStatus.style.color = '#f39c12';
                    addLog(`🔐 认证测试异常 - HTTP ${response.status}`, 'warning');
                    return false;
                }
            } catch (error) {
                authStatus.textContent = '💥 测试失败';
                authStatus.style.color = '#e74c3c';
                if (error.message.includes('Bearer Token')) {
                    addLog('❌ 请先输入Bearer Token', 'error');
                } else {
                    addLog(`💥 认证测试异常: ${error.message}`, 'error');
                }
                return false;
            }
        }

        // WebSocket连接函数
        async function connectWebSocket() {
            try {
                const headers = getAuthHeaders();
                const wsUrl = document.getElementById('wsUrl').value.trim();
                const workOrderId = document.getElementById('workOrderId').value.trim();
                const aiAgentId = document.getElementById('aiAgentId').value.trim();
                const companyId = document.getElementById('companyId').value.trim();

                if (!wsUrl || !workOrderId || !aiAgentId || !companyId) {
                    addLog('❌ 请填写完整的WebSocket配置信息', 'error');
                    return;
                }

                updateWsStatus('connecting', '连接中...');
                addLog('🔄 正在建立WebSocket连接...', 'info');

                const response = await fetch(`${API_BASE}/connect`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        webSocketUrl: wsUrl,
                        workOrderId: workOrderId,
                        aiAgentId: aiAgentId,
                        companyId: companyId
                    })
                });

                if (response.status === 401) {
                    addLog('❌ 认证失败，请检查Bearer Token', 'error');
                    updateWsStatus('disconnected', '未连接');
                    return;
                }

                const result = await response.json();

                if (result.code === 200) {
                    wsClientId = result.data.clientId;
                    updateWsStatus('connected', '已连接');
                    addLog(`✅ WebSocket连接成功，客户端ID: ${wsClientId}`, 'success');

                    // 启用录音按钮
                    document.getElementById('recordBtn').disabled = false;
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;

                    // 自动建立SSE音频流连接
                    connectAudioStream();
                } else {
                    addLog(`❌ WebSocket连接失败: ${result.msg}`, 'error');
                    updateWsStatus('disconnected', '未连接');
                }
            } catch (error) {
                if (error.message.includes('Bearer Token')) {
                    addLog('❌ 请先输入Bearer Token', 'error');
                } else {
                    addLog(`💥 WebSocket连接异常: ${error.message}`, 'error');
                }
                updateWsStatus('disconnected', '未连接');
            }
        }

        async function disconnectWebSocket() {
            try {
                // 停止录音
                if (isRecording) {
                    stopRecording();
                }

                // 清空音频队列
                clearAudioQueue();

                // 断开SSE连接
                if (audioStreamEventSource) {
                    audioStreamEventSource.close();
                    audioStreamEventSource = null;
                }

                if (wsClientId) {
                    const headers = getAuthHeaders();
                    const response = await fetch(`${API_BASE}/disconnect/${wsClientId}`, {
                        method: 'POST',
                        headers: headers
                    });

                    if (response.status === 401) {
                        addLog('❌ 认证失败，请检查Bearer Token', 'error');
                        return;
                    }

                    const result = await response.json();

                    if (result.code === 200) {
                        addLog(`✅ WebSocket连接断开成功`, 'success');
                    } else {
                        addLog(`⚠️ WebSocket断开响应: ${result.msg}`, 'warning');
                    }
                }

                wsClientId = null;
                updateWsStatus('disconnected', '未连接');

                // 重置按钮状态
                document.getElementById('recordBtn').disabled = true;
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;

                addLog('🔌 连接已断开', 'info');
            } catch (error) {
                if (error.message.includes('Bearer Token')) {
                    addLog('❌ 请先输入Bearer Token', 'error');
                } else {
                    addLog(`💥 断开连接异常: ${error.message}`, 'error');
                }
            }
        }

        function updateWsStatus(status, text) {
            const statusElement = document.getElementById('wsStatus');
            statusElement.className = `status status-${status}`;
            statusElement.textContent = text;
        }

        // SSE音频流连接
        function connectAudioStream() {
            if (!wsClientId) {
                addLog('❌ 请先建立WebSocket连接', 'error');
                return;
            }

            try {
                // 使用workOrderId作为SSE连接的clientId，与后端保持一致
                const workOrderId = document.getElementById('workOrderId').value.trim();
                const sseClientId = workOrderId || wsClientId;

                addLog(`🎵 建立音频流连接，SSE客户端ID: ${sseClientId}`, 'info');

                // 建立SSE连接（后端使用@NoLogin注解跳过认证）
                const eventSourceUrl = `${API_BASE}/audio-stream/${sseClientId}`;
                audioStreamEventSource = new EventSource(eventSourceUrl);

                addLog(`📡 SSE连接URL: ${eventSourceUrl}`, 'info');

                audioStreamEventSource.onopen = function(event) {
                    addLog('✅ 音频流连接已建立', 'success');
                };

                // 监听默认消息事件
                audioStreamEventSource.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        addLog(`📡 收到SSE消息: ${JSON.stringify(data)}`, 'info');
                        if (data.type === 'tts-audio' && data.audio) {
                            addLog(`🔊 收到TTS音频数据，格式: ${data.format}, 采样率: ${data.sampleRate}Hz, 时长: ${data.duration}s`, 'info');
                            playTTSAudio(data);
                        }
                    } catch (error) {
                        addLog(`❌ 解析音频数据失败: ${error.message}`, 'error');
                    }
                };

                // 监听特定的TTS音频事件
                audioStreamEventSource.addEventListener('tts-audio-data', function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        addLog(`🔊 收到TTS音频数据，格式: ${data.format}, 采样率: ${data.sampleRate}Hz, 时长: ${data.duration}s`, 'info');
                        playTTSAudio(data);
                    } catch (error) {
                        addLog(`❌ 解析TTS音频数据失败: ${error.message}`, 'error');
                    }
                });

                // 监听TTS打断事件
                audioStreamEventSource.addEventListener('tts-interrupt', function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        addLog(`⏹️ 收到TTS打断消息: ${data.reason}`, 'warning');
                        // 清空音频队列并停止播放
                        clearAudioQueue();
                    } catch (error) {
                        addLog(`❌ 解析TTS打断消息失败: ${error.message}`, 'error');
                    }
                });

                // 监听静默超时事件
                audioStreamEventSource.addEventListener('silence-timeout', function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        addLog(`🔇 收到静默超时消息: ${data.message}，超时时长: ${data.timeoutDuration}秒`, 'warning');

                        // 清空音频队列并停止播放
                        clearAudioQueue();

                        // 显示超时提示
                        showTimeoutNotification(data.message, data.timeoutDuration);

                        // 延迟更新连接状态（等待后端断开连接）
                        setTimeout(() => {
                            handleSilenceTimeoutDisconnection();
                        }, 1500);

                    } catch (error) {
                        addLog(`❌ 解析静默超时消息失败: ${error.message}`, 'error');
                    }
                });

                audioStreamEventSource.onerror = function(event) {
                    addLog('❌ 音频流连接错误', 'error');
                    audioStreamEventSource = null;
                };

            } catch (error) {
                addLog(`💥 音频流连接异常: ${error.message}`, 'error');
            }
        }

        // 播放TTS音频片段（支持连续播放）
        function playTTSAudio(audioData) {
            try {
                addLog(`🎵 收到音频片段，添加到播放队列...`, 'info');
                addLog(`📊 音频数据详情: 格式=${audioData.format}, 采样率=${audioData.sampleRate}, 时长=${audioData.duration}s, Base64长度=${audioData.audio ? audioData.audio.length : 0}`, 'info');

                if (!audioData.audio || audioData.audio.length === 0) {
                    addLog('❌ 音频数据为空', 'error');
                    return;
                }

                // 解码Base64音频数据
                const binaryString = atob(audioData.audio);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }

                addLog(`🔧 Base64解码完成，二进制数据长度: ${bytes.length} bytes`, 'info');

                // 根据音频格式创建正确的音频Blob
                let audioBlob;
                if (audioData.format === 'pcm') {
                    // PCM格式需要转换为WAV
                    addLog('🔄 转换PCM格式为WAV...', 'info');
                    audioBlob = createWavBlob(bytes, audioData.sampleRate || 16000);
                } else if (audioData.format === 'mulaw') {
                    // μ-law格式需要解码后转换为WAV
                    addLog('🔄 转换μ-law格式为WAV...', 'info');
                    audioBlob = createMulawWavBlob(bytes, audioData.sampleRate || 8000);
                } else {
                    // 其他格式直接创建Blob
                    addLog(`🔄 使用原始格式: ${audioData.format}`, 'info');
                    audioBlob = new Blob([bytes], { type: 'audio/wav' });
                }

                // 创建音频片段对象
                const audioSegment = {
                    blob: audioBlob,
                    url: URL.createObjectURL(audioBlob),
                    data: audioData,
                    timestamp: Date.now()
                };

                // 添加到播放队列
                audioQueue.push(audioSegment);
                addLog(`📋 音频片段已添加到队列，当前队列长度: ${audioQueue.length}`, 'info');

                // 更新音频信息显示
                updateAudioInfo();

                // 如果当前没有在播放，开始播放队列
                if (!isPlayingQueue) {
                    startPlayingQueue();
                }

            } catch (error) {
                addLog(`❌ TTS音频片段处理失败: ${error.message}`, 'error');
                console.error('TTS音频片段处理详细错误:', error);
            }
        }

        // 开始播放音频队列
        function startPlayingQueue() {
            if (isPlayingQueue || audioQueue.length === 0) {
                return;
            }

            isPlayingQueue = true;
            currentPlayingIndex = 0;
            addLog(`🎵 开始播放音频队列，共 ${audioQueue.length} 个片段`, 'success');

            playNextSegment();
        }

        // 播放下一个音频片段
        function playNextSegment() {
            if (currentPlayingIndex >= audioQueue.length) {
                // 队列播放完成
                isPlayingQueue = false;
                addLog('✅ 音频队列播放完成', 'success');
                updateAudioInfo();
                return;
            }

            const segment = audioQueue[currentPlayingIndex];
            const audioPlayer = document.getElementById('audioPlayer');

            // 停止当前播放的音频
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
            }

            // 设置新的音频源
            audioPlayer.src = segment.url;
            audioPlayer.style.display = 'block';
            currentAudio = audioPlayer;

            addLog(`▶️ 播放音频片段 ${currentPlayingIndex + 1}/${audioQueue.length}`, 'info');

            // 设置播放结束事件
            audioPlayer.onended = function() {
                addLog(`✅ 音频片段 ${currentPlayingIndex + 1} 播放完成`, 'success');

                // 清理当前片段的URL
                URL.revokeObjectURL(segment.url);

                // 移除已播放的片段
                audioQueue.shift();

                // 播放下一个片段（不增加索引，因为数组已经shift）
                setTimeout(() => {
                    playNextSegment();
                }, 50); // 短暂延迟避免音频切换过快
            };

            audioPlayer.onerror = function(e) {
                addLog(`❌ 音频片段 ${currentPlayingIndex + 1} 播放错误: ${e.target.error ? e.target.error.message : '未知错误'}`, 'error');

                // 清理当前片段的URL
                URL.revokeObjectURL(segment.url);

                // 移除错误的片段并继续播放下一个
                audioQueue.shift();
                setTimeout(() => {
                    playNextSegment();
                }, 50);
            };

            // 开始播放
            audioPlayer.play().then(() => {
                addLog(`🎵 音频片段 ${currentPlayingIndex + 1} 开始播放`, 'success');
            }).catch(error => {
                addLog(`❌ 音频片段 ${currentPlayingIndex + 1} 播放启动失败: ${error.message}`, 'error');

                // 播放失败，跳过这个片段
                URL.revokeObjectURL(segment.url);
                audioQueue.shift();
                setTimeout(() => {
                    playNextSegment();
                }, 50);
            });
        }

        // 更新音频信息显示
        function updateAudioInfo() {
            const audioInfo = document.getElementById('audioInfo');

            if (audioQueue.length === 0 && !isPlayingQueue) {
                audioInfo.innerHTML = '等待接收TTS音频数据...';
            } else {
                const currentSegment = audioQueue[0];
                audioInfo.innerHTML = `
                    <strong>音频队列播放中:</strong><br>
                    队列长度: ${audioQueue.length} 个片段<br>
                    当前片段: 格式=${currentSegment ? currentSegment.data.format : 'N/A'} |
                    采样率=${currentSegment ? currentSegment.data.sampleRate : 'N/A'}Hz<br>
                    播放状态: ${isPlayingQueue ? '播放中' : '等待播放'}
                `;
            }
        }

        // 清空音频队列
        function clearAudioQueue() {
            // 停止当前播放
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
            }

            // 清理所有URL对象
            audioQueue.forEach(segment => {
                URL.revokeObjectURL(segment.url);
            });

            // 清空队列
            audioQueue = [];
            isPlayingQueue = false;
            currentPlayingIndex = 0;

            // 隐藏播放器
            const audioPlayer = document.getElementById('audioPlayer');
            audioPlayer.style.display = 'none';
            audioPlayer.src = '';

            updateAudioInfo();
            addLog('🗑️ 音频队列已清空', 'info');
        }

        // 处理静默超时断开连接
        function handleSilenceTimeoutDisconnection() {
            addLog('🔌 因静默超时，WebSocket连接即将断开...', 'warning');

            // 停止录音
            if (isRecording) {
                stopRecording();
                addLog('⏹️ 因静默超时停止录音', 'warning');
            }

            // 断开SSE连接
            if (audioStreamEventSource) {
                audioStreamEventSource.close();
                audioStreamEventSource = null;
                addLog('📡 SSE连接已断开', 'info');
            }

            // 更新连接状态
            wsClientId = null;
            updateWsStatus('disconnected', '超时断开');

            // 重置按钮状态
            document.getElementById('recordBtn').disabled = true;
            document.getElementById('connectBtn').disabled = false;
            document.getElementById('disconnectBtn').disabled = true;

            addLog('❌ 因用户静默超时，连接已断开', 'error');
        }

        // 显示超时通知
        function showTimeoutNotification(message, timeoutDuration) {
            // 更新音频信息显示超时消息
            const audioInfo = document.getElementById('audioInfo');
            audioInfo.innerHTML = `
                <div class="timeout-notification">
                    ⏰ 静默超时通知<br>
                    消息: ${message}<br>
                    超时时长: ${timeoutDuration}秒<br>
                    连接将自动断开...
                </div>
            `;

            // 可选：显示浏览器通知（需要用户授权）
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('WebSocket连接超时', {
                    body: `${message}，超时时长: ${timeoutDuration}秒`,
                    icon: '/favicon.ico'
                });
            }
        }

        // 请求通知权限（可选）
        function requestNotificationPermission() {
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        addLog('✅ 浏览器通知权限已授权', 'success');
                    }
                });
            }
        }

        // 创建WAV格式的Blob（用于PCM数据）
        function createWavBlob(pcmData, sampleRate) {
            const length = pcmData.length;
            const buffer = new ArrayBuffer(44 + length);
            const view = new DataView(buffer);

            // WAV文件头
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };

            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length, true);

            // 复制PCM数据
            const pcmView = new Uint8Array(buffer, 44);
            pcmView.set(pcmData);

            return new Blob([buffer], { type: 'audio/wav' });
        }

        // 创建μ-law WAV格式的Blob
        function createMulawWavBlob(mulawData, sampleRate) {
            // μ-law解码表
            const mulawToLinear = (mulaw) => {
                mulaw = ~mulaw;
                const sign = (mulaw & 0x80);
                const exponent = (mulaw >> 4) & 0x07;
                const mantissa = mulaw & 0x0F;
                let sample = mantissa << (exponent + 3);
                if (exponent > 0) sample += (1 << (exponent + 2));
                return sign ? -sample : sample;
            };

            // 将μ-law转换为16位PCM
            const pcmData = new Int16Array(mulawData.length);
            for (let i = 0; i < mulawData.length; i++) {
                pcmData[i] = mulawToLinear(mulawData[i]);
            }

            // 创建WAV文件
            const buffer = new ArrayBuffer(44 + pcmData.length * 2);
            const view = new DataView(buffer);

            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };

            writeString(0, 'RIFF');
            view.setUint32(4, 36 + pcmData.length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, pcmData.length * 2, true);

            // 写入PCM数据
            let offset = 44;
            for (let i = 0; i < pcmData.length; i++) {
                view.setInt16(offset, pcmData[i], true);
                offset += 2;
            }

            return new Blob([buffer], { type: 'audio/wav' });
        }

        // 测试音频播放功能
        function testAudioPlayback() {
            addLog('🧪 开始测试音频播放功能...', 'info');

            // 生成一个简单的测试音频（440Hz正弦波，1秒）
            const sampleRate = 16000;
            const duration = 1; // 1秒
            const frequency = 440; // A4音符
            const samples = sampleRate * duration;

            // 生成PCM数据
            const pcmData = new Int16Array(samples);
            for (let i = 0; i < samples; i++) {
                const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate);
                pcmData[i] = sample * 0x7FFF; // 转换为16位整数
            }

            // 转换为Uint8Array
            const uint8Data = new Uint8Array(pcmData.buffer);

            // 创建WAV文件
            const wavBlob = createWavBlob(uint8Data, sampleRate);

            // 转换为Base64
            const reader = new FileReader();
            reader.onload = function() {
                const base64Data = reader.result.split(',')[1]; // 移除data:audio/wav;base64,前缀

                // 模拟TTS音频数据
                const testAudioData = {
                    type: 'tts-audio',
                    clientId: 'test-client',
                    msgId: 'test-msg-' + Date.now(),
                    audio: base64Data,
                    format: 'pcm',
                    sampleRate: sampleRate,
                    duration: duration,
                    timestamp: Date.now()
                };

                addLog('🎵 播放测试音频（440Hz正弦波，1秒）', 'info');
                playTTSAudio(testAudioData);
            };

            reader.readAsDataURL(wavBlob);
        }

        // 测试SSE连接
        function testSSEConnection() {
            addLog('📡 开始测试SSE连接...', 'info');

            if (!audioStreamEventSource) {
                addLog('❌ SSE连接未建立，请先连接WebSocket', 'error');
                return;
            }

            addLog(`📊 SSE连接状态: ${audioStreamEventSource.readyState}`, 'info');
            addLog('0=CONNECTING, 1=OPEN, 2=CLOSED', 'info');

            if (audioStreamEventSource.readyState === EventSource.OPEN) {
                addLog('✅ SSE连接正常，等待接收数据...', 'success');

                // 发送一个测试音频数据来触发后端推送
                if (wsClientId) {
                    addLog('🎵 发送测试音频数据以触发TTS回复...', 'info');
                    sendTestAudioForTTS();
                }
            } else if (audioStreamEventSource.readyState === EventSource.CONNECTING) {
                addLog('🔄 SSE连接正在建立中...', 'warning');
            } else {
                addLog('❌ SSE连接已关闭', 'error');
            }
        }

        // 发送测试音频数据以触发TTS回复
        async function sendTestAudioForTTS() {
            try {
                const headers = getAuthHeaders();

                // 发送一个简单的测试音频数据
                const response = await fetch(`${API_BASE}/send-audio?clientId=${wsClientId}`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        audioData: "dGVzdCBhdWRpbyBkYXRh" // "test audio data" 的Base64编码
                    })
                });

                if (response.status === 401) {
                    addLog('❌ 认证失败，请检查Bearer Token', 'error');
                    return;
                }

                const result = await response.json();
                if (result.code === 200) {
                    addLog('✅ 测试音频数据发送成功，等待TTS回复...', 'success');
                } else {
                    addLog(`❌ 测试音频数据发送失败: ${result.msg}`, 'error');
                }

            } catch (error) {
                addLog(`❌ 发送测试音频数据失败: ${error.message}`, 'error');
            }
        }

        // 音频录制功能
        async function toggleRecording() {
            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        }

        async function startRecording() {
            if (!wsClientId) {
                addLog('❌ 请先建立WebSocket连接', 'error');
                return;
            }

            try {
                addLog('🎤 正在启动录音...', 'info');

                // 获取麦克风权限
                recordingStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                // 创建AudioContext用于PCM转换
                audioContext = new (window.AudioContext || window.webkitAudioContext)({
                    sampleRate: 16000
                });

                const source = audioContext.createMediaStreamSource(recordingStream);
                audioProcessor = audioContext.createScriptProcessor(4096, 1, 1);

                // 实时处理音频数据
                audioProcessor.onaudioprocess = function(event) {
                    if (isRecording) {
                        const inputBuffer = event.inputBuffer;
                        const inputData = inputBuffer.getChannelData(0);

                        // 应用音量控制
                        const volume = document.getElementById('volumeSlider').value / 100;
                        const adjustedData = new Float32Array(inputData.length);
                        for (let i = 0; i < inputData.length; i++) {
                            adjustedData[i] = inputData[i] * volume;
                        }

                        // 转换为PCM 16位数据
                        const pcmData = convertToPCM16(adjustedData);

                        // 实时发送音频数据
                        sendAudioDataRealtime(pcmData);
                    }
                };

                source.connect(audioProcessor);
                audioProcessor.connect(audioContext.destination);

                isRecording = true;
                updateRecordingUI(true);
                addLog('🎤 录音已开始，正在实时发送PCM音频数据...', 'success');

            } catch (error) {
                addLog(`❌ 录音启动失败: ${error.message}`, 'error');
                if (error.name === 'NotAllowedError') {
                    addLog('💡 请允许浏览器访问麦克风权限', 'warning');
                }
            }
        }

        function stopRecording() {
            if (recordingStream) {
                recordingStream.getTracks().forEach(track => track.stop());
                recordingStream = null;
            }

            if (audioProcessor) {
                audioProcessor.disconnect();
                audioProcessor = null;
            }

            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }

            isRecording = false;
            updateRecordingUI(false);
            addLog('⏹️ 录音已停止', 'info');
        }

        function updateRecordingUI(recording) {
            const recordBtn = document.getElementById('recordBtn');
            const indicator = document.getElementById('recordingIndicator');
            const status = document.getElementById('recordingStatus');

            if (recording) {
                recordBtn.textContent = '⏹️ 停止录音';
                recordBtn.className = 'btn btn-danger';
                indicator.style.display = 'inline-block';
                status.textContent = '录音中...';
            } else {
                recordBtn.textContent = '🎤 开始录音';
                recordBtn.className = 'btn btn-success';
                indicator.style.display = 'none';
                status.textContent = '未录音';
            }
        }

        // 转换为PCM 16位数据
        function convertToPCM16(float32Array) {
            const buffer = new ArrayBuffer(float32Array.length * 2);
            const view = new DataView(buffer);
            let offset = 0;

            for (let i = 0; i < float32Array.length; i++, offset += 2) {
                const s = Math.max(-1, Math.min(1, float32Array[i]));
                view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
            }

            return buffer;
        }

        // 实时发送音频数据
        async function sendAudioDataRealtime(pcmData) {
            try {
                const headers = getAuthHeaders();

                // 转换为Base64
                const uint8Array = new Uint8Array(pcmData);
                const base64Audio = btoa(String.fromCharCode.apply(null, uint8Array));

                const response = await fetch(`${API_BASE}/send-audio?clientId=${wsClientId}`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        audioData: base64Audio
                    })
                });

                if (response.status === 401) {
                    addLog('❌ 认证失败，停止录音', 'error');
                    stopRecording();
                    return;
                }

                // 不需要处理响应，音频数据会通过SSE返回
                // 可以在这里添加发送成功的统计信息

            } catch (error) {
                if (error.message.includes('Bearer Token')) {
                    addLog('❌ 请先输入Bearer Token', 'error');
                    stopRecording();
                } else {
                    // 静默处理发送错误，避免日志过多
                    console.error('发送音频数据失败:', error);
                }
            }
        }

        // 音量控制
        document.addEventListener('DOMContentLoaded', function() {
            const volumeSlider = document.getElementById('volumeSlider');
            const volumeValue = document.getElementById('volumeValue');

            volumeSlider.addEventListener('input', function() {
                volumeValue.textContent = this.value + '%';
            });

            // 请求浏览器通知权限
            requestNotificationPermission();

            addLog('🚀 WebSocket实时音频测试页面已加载', 'info');
            addLog('📋 使用说明：', 'info');
            addLog('1. 输入Bearer Token并测试认证', 'info');
            addLog('2. 配置WebSocket参数并连接', 'info');
            addLog('3. 开始录音进行实时音频传输', 'info');
            addLog('4. 接收并播放TTS音频回复', 'info');
            addLog('5. 系统会自动处理静默超时并断开连接', 'info');
        });
    </script>
</body>
</html>
