# WebSocket Bridge 2.0 配置文件
# 用于配置WebSocket桥接服务的各种参数

websocket:
  bridge:
    # WebSocket服务器配置
    server-url: ws://192.168.110.21:8765/voice-duplex/
    
    # 连接配置
    connect-timeout: 30000          # 连接超时时间（毫秒）
    heartbeat-interval: 30000       # 心跳间隔（毫秒）
    heartbeat-timeout: 10000        # 心跳超时时间（毫秒）
    
    # 重连配置
    reconnect-interval: 5000        # 重连间隔（毫秒）
    max-reconnect-attempts: 3       # 最大重连次数
    auto-reconnect: true            # 是否启用自动重连
    
    # 消息配置
    send-timeout: 5000              # 消息发送超时时间（毫秒）
    receive-buffer-size: 8192       # 接收缓冲区大小
    send-buffer-size: 8192          # 发送缓冲区大小
    compression-enabled: false      # 是否启用压缩
    
    # 业务配置
    silence-timeout: 60             # 静默超时时间（秒）
    audio-sample-rate: 16000        # 音频采样率
    audio-format: pcm               # 音频格式
    audio-compression-enabled: false # 是否启用音频压缩
    
    # 日志配置
    log-level: INFO                 # 日志级别：DEBUG, INFO, WARN, ERROR
    verbose-logging: false          # 是否启用详细日志
    
    # 线程池配置
    connection-pool-size: 100       # 连接池大小
    thread-pool-core-size: 10       # 线程池核心大小
    thread-pool-max-size: 50        # 线程池最大大小
    thread-pool-queue-size: 1000    # 线程池队列大小

# 开发环境配置
---
spring:
  profiles: dev
  
websocket:
  bridge:
    server-url: ws://localhost:8765/voice-duplex/
    log-level: DEBUG
    verbose-logging: true
    max-reconnect-attempts: 5

# 测试环境配置  
---
spring:
  profiles: test
  
websocket:
  bridge:
    server-url: ws://test-server:8765/voice-duplex/
    log-level: INFO
    verbose-logging: false
    max-reconnect-attempts: 3

# 生产环境配置
---
spring:
  profiles: prod
  
websocket:
  bridge:
    server-url: ws://prod-server:8765/voice-duplex/
    log-level: WARN
    verbose-logging: false
    max-reconnect-attempts: 1
    connection-pool-size: 200
    thread-pool-core-size: 20
    thread-pool-max-size: 100
