# WebSocket Bridge 2.0 测试页面使用说明

## 📋 概述

WebSocket Bridge 2.0 测试页面是一个完整的WebSocket连接测试工具，基于独立的WebSocket桥接服务实现，提供了直观的用户界面来测试WebSocket连接、音频传输和实时消息处理功能。

## 🌐 访问地址

### 1. 直接访问
```
http://localhost:8080/websocket-bridge2-test.html
```

### 2. 通过控制器访问
```
http://localhost:8080/wsbridge2/test/page
```

## 🎯 主要功能

### 1. WebSocket连接管理
- ✅ 建立WebSocket连接
- ✅ 断开WebSocket连接
- ✅ 实时连接状态监控
- ✅ 连接信息统计

### 2. 音频功能测试
- ✅ 实时录音和音频发送
- ✅ 测试音频数据发送
- ✅ TTS音频接收和播放
- ✅ 音频格式和采样率配置

### 3. 实时消息处理
- ✅ init消息自动处理
- ✅ TTS音频数据接收
- ✅ TTS打断消息处理
- ✅ 静默超时消息处理
- ✅ ASR识别结果显示
- ✅ 错误消息处理

### 4. 用户界面功能
- ✅ 实时日志显示
- ✅ 连接状态可视化
- ✅ 音频信息展示
- ✅ 日志导出功能

## 🚀 使用步骤

### 第一步：认证配置

1. **Bearer Token认证**
   - 在"认证配置"区域输入有效的Bearer Token
   - 点击"🔍 测试认证"按钮验证Token有效性
   - 确保认证状态显示为"✅ 认证成功"

### 第二步：配置连接参数

1. **必填参数**
   - **工单ID**: 唯一标识符，必须填写（如：test_order_001）

2. **可选参数**
   - **智能体ID**: AI智能体标识（如：test_agent_001）
   - **公司ID**: 公司标识（如：test_company_001）
   - **渠道ID**: 接入渠道标识（如：web_channel）
   - **用户ID**: 用户标识（如：test_user_001）
   - **会话ID**: 会话标识（如：test_session_001）

### 第三步：建立连接

1. 点击 **"🔗 建立连接"** 按钮
2. 观察连接状态变化：`未连接` → `连接中...` → `已连接`
3. 查看实时日志中的连接信息
4. 确认SSE音频流连接建立成功

### 第三步：音频测试

#### 方式1：实时录音测试
1. 点击 **"🎤 开始录音"** 按钮
2. 对着麦克风说话
3. 点击 **"⏹️ 停止录音"** 按钮
4. 系统自动发送录音数据到WebSocket服务器

#### 方式2：发送测试音频
1. 点击 **"📤 发送测试音频"** 按钮
2. 系统发送预设的测试音频数据
3. 观察发送结果和服务器响应

### 第四步：观察响应

1. **TTS音频接收和播放**
   - 当服务器返回TTS音频时，页面会自动添加到播放队列
   - 支持连续播放多个音频片段
   - 音频播放区域会显示队列信息和播放状态
   - 可以通过音量滑块调节播放音量
   - 支持暂停/恢复播放和清空队列

2. **实时消息监控**
   - 所有WebSocket消息都会在日志区域实时显示
   - 包括连接状态、音频数据、错误信息等

3. **连接信息统计**
   - 实时显示连接时长、发送/接收消息数量
   - 连接状态和错误信息

### 第五步：断开连接

1. 点击 **"🔌 断开连接"** 按钮
2. 系统会清理所有资源
3. 界面状态重置为初始状态

## 📊 界面说明

### 1. 认证配置区域
- Bearer Token输入框
- 认证测试按钮和状态指示器
- 认证状态实时显示

### 2. 连接配置区域
- 输入连接所需的业务参数
- 连接/断开按钮和状态指示器
- 连接状态实时显示

### 3. 音频测试区域
- 音频格式和采样率配置
- 录音和发送测试音频按钮
- 音量控制滑块
- 音频信息显示区域
- TTS音频播放器和队列管理

### 4. 连接信息区域
- 连接详细信息统计
- 实时更新连接时长
- 消息发送/接收计数

### 5. 实时日志区域
- 所有操作和消息的详细日志
- 支持日志清空和导出
- 不同类型消息用不同颜色标识

## 🎨 状态指示说明

### 连接状态
- 🔴 **未连接**: 初始状态或连接断开
- 🟡 **连接中...**: 正在建立连接
- 🟢 **已连接**: 连接建立成功
- 🔴 **连接失败**: 连接建立失败
- 🔴 **连接异常**: 连接过程中发生异常

### 日志类型
- 🟢 **INFO**: 一般信息（绿色）
- 🟡 **WARNING**: 警告信息（黄色）
- 🔴 **ERROR**: 错误信息（红色）
- 🔵 **SUCCESS**: 成功信息（蓝色）

## 🔧 API接口说明

### 1. 建立连接
```http
POST /wsbridge2/test/connect
Content-Type: application/json

{
    "workOrderId": "test_order_001",
    "aiAgentId": "test_agent_001",
    "companyId": "test_company_001",
    "channelId": "web_channel",
    "userId": "test_user_001",
    "sessionId": "test_session_001"
}
```

### 2. 发送音频
```http
POST /wsbridge2/test/send-audio/{clientId}
Content-Type: application/json

{
    "audioData": "base64编码的音频数据",
    "format": "pcm",
    "sampleRate": 16000
}
```

### 3. 断开连接
```http
POST /wsbridge2/test/disconnect/{clientId}
```

### 4. 查看状态
```http
GET /wsbridge2/test/status/{clientId}
```

### 5. SSE音频流
```http
GET /wsbridge2/test/audio-stream/{clientId}
```

## 🧪 测试场景

### 1. 基础连接测试
- 测试WebSocket连接建立和断开
- 验证连接状态变化
- 检查错误处理机制

### 2. 音频传输测试
- 测试实时录音和音频发送
- 验证TTS音频接收和播放
- 检查音频格式兼容性

### 3. 消息处理测试
- 测试各种WebSocket消息类型
- 验证消息解析和回调触发
- 检查异常消息处理

### 4. 长连接稳定性测试
- 测试长时间连接稳定性
- 验证心跳和重连机制
- 检查资源清理

## ⚠️ 注意事项

### 1. 浏览器兼容性
- 需要支持WebSocket和SSE的现代浏览器
- 需要支持MediaRecorder API（录音功能）
- 建议使用Chrome、Firefox、Safari等主流浏览器

### 2. 权限要求
- 录音功能需要麦克风权限
- 音频播放需要用户交互激活

### 3. 网络要求
- 确保能够访问WebSocket服务器
- 防火墙需要允许WebSocket连接

### 4. 服务器配置
- 确保WebSocket Bridge 2.0服务正常运行
- 确保相关依赖已正确安装

## 🔍 故障排除

### 1. 连接失败
- 检查工单ID是否填写
- 确认WebSocket服务器地址配置
- 查看浏览器控制台错误信息

### 2. 音频问题
- 检查麦克风权限
- 确认音频格式支持
- 查看音频编码是否正确

### 3. 消息接收问题
- 检查SSE连接状态
- 确认服务器消息格式
- 查看网络连接稳定性

---

**提示**: 这个测试页面是为开发和调试设计的，提供了完整的WebSocket Bridge 2.0功能测试能力。在生产环境中，建议根据实际业务需求进行相应的安全配置和功能定制。
