# WebSocket Bridge 2.0 测试页面功能说明

## 📋 功能对比

### 与原页面功能对比
| 功能模块 | 原页面 (websocket-realtime-audio.html) | 新页面 (websocket-bridge2-test.html) | 状态 |
|---------|---------------------------------------|-------------------------------------|------|
| 认证配置 | ✅ Bearer Token认证 | ✅ Bearer Token认证 | ✅ 完全一致 |
| WebSocket连接 | ✅ 连接/断开控制 | ✅ 连接/断开控制 | ✅ 完全一致 |
| 连接配置 | ✅ URL、参数配置 | ✅ URL、参数配置 | ✅ 完全一致 |
| 实时录音 | ✅ 开始/停止录音 | ✅ 开始/停止录音 | ✅ 完全一致 |
| 录音模式 | ✅ 连续/VAD/手动 | ✅ 连续/VAD/手动 | ✅ 完全一致 |
| 音频发送 | ✅ 实时发送音频 | ✅ 实时发送音频 | ✅ 完全一致 |
| TTS音频播放 | ✅ 队列播放 | ✅ 队列播放 | ✅ 完全一致 |
| 音频控制 | ✅ 暂停/恢复/清空 | ✅ 暂停/恢复/清空 | ✅ 完全一致 |
| 连接状态 | ✅ 实时状态显示 | ✅ 实时状态显示 | ✅ 完全一致 |
| 统计信息 | ✅ 消息统计 | ✅ 消息统计 | ✅ 完全一致 |
| 日志记录 | ✅ 实时日志 | ✅ 实时日志 | ✅ 完全一致 |

## 🎯 核心功能

### 1. 认证配置
- **Bearer Token认证**: 支持Token输入和验证
- **认证状态显示**: 实时显示认证状态
- **自动认证检查**: 连接前自动检查认证状态

### 2. WebSocket连接管理
- **连接配置**: WebSocket URL、工单ID、智能体ID、公司ID等
- **连接控制**: 一键连接/断开
- **连接状态**: 实时显示连接状态（未连接/连接中/已连接）
- **自动重连**: 支持连接失败后的重试机制

### 3. 实时录音功能
- **录音模式**: 
  - 连续录音：持续录音并发送
  - 语音检测(VAD)：检测到语音时自动录音
  - 手动控制：手动开始/停止录音
- **录音参数**: 可配置采样率、音频格式
- **录音状态**: 实时显示录音时长、音频大小
- **音频发送**: 自动将录音数据发送到WebSocket服务器

### 4. TTS音频播放
- **队列播放**: 支持多个音频片段的队列播放
- **播放控制**: 暂停、恢复、清空队列
- **音频信息**: 显示当前播放的音频详情
- **音量控制**: 可调节播放音量
- **格式支持**: 支持PCM、WAV、μ-law等多种音频格式

### 5. 连接状态与统计
- **连接信息**: 客户端ID、连接时间、持续时间
- **消息统计**: 发送音频数、接收TTS数、ASR识别数、错误次数
- **实时更新**: 所有统计信息实时更新

### 6. 日志系统
- **实时日志**: 所有操作和消息的详细记录
- **日志分类**: 信息、警告、错误、成功等不同类型
- **日志导出**: 支持导出日志文件
- **日志清理**: 一键清空日志

## 🔧 技术实现

### 前端技术
- **原生JavaScript**: 无框架依赖，纯JavaScript实现
- **WebSocket客户端**: 使用原生WebSocket API
- **SSE连接**: Server-Sent Events实现实时消息推送
- **MediaRecorder API**: 实现浏览器录音功能
- **Audio API**: 实现音频播放和控制

### 后端接口
- **REST API**: 提供HTTP接口进行WebSocket连接管理
- **SSE推送**: 实时推送TTS音频和消息
- **认证机制**: Bearer Token认证
- **音频处理**: 支持多种音频格式的转换和处理

## 🎨 用户界面

### 界面设计
- **现代化设计**: 渐变色背景、卡片式布局
- **响应式布局**: 支持不同屏幕尺寸
- **状态指示**: 清晰的状态指示器和进度显示
- **操作反馈**: 实时的操作反馈和状态更新

### 交互体验
- **一键操作**: 简单的按钮操作
- **实时反馈**: 即时的状态更新和消息提示
- **错误处理**: 友好的错误提示和处理
- **数据可视化**: 直观的统计信息展示

## 🚀 使用流程

### 基本使用流程
1. **认证配置**: 输入Bearer Token并测试认证
2. **连接配置**: 配置WebSocket URL和业务参数
3. **建立连接**: 点击"连接WebSocket"建立连接
4. **开始录音**: 选择录音模式并开始录音
5. **音频交互**: 发送音频数据，接收TTS音频回复
6. **监控状态**: 观察连接状态和统计信息
7. **断开连接**: 完成测试后断开连接

### 高级功能
- **音频队列管理**: 管理TTS音频播放队列
- **录音参数调节**: 调整采样率、格式等参数
- **日志分析**: 导出和分析操作日志
- **错误诊断**: 通过日志和统计信息诊断问题

## 📊 API接口

### 主要接口
```
POST /wsbridge2/test/connect          # 建立WebSocket连接
POST /wsbridge2/test/disconnect/{id}  # 断开WebSocket连接
POST /wsbridge2/test/send-audio/{id}  # 发送音频数据
GET  /wsbridge2/test/status/{id}      # 获取连接状态
GET  /wsbridge2/test/audio-stream/{id} # SSE音频流
```

### 接口特点
- **RESTful设计**: 符合REST规范的API设计
- **统一认证**: 所有接口使用Bearer Token认证
- **错误处理**: 完善的错误码和错误信息
- **实时推送**: 通过SSE实现实时消息推送

## 🧪 测试功能

### 测试场景
- **连接测试**: 测试WebSocket连接的建立和断开
- **音频测试**: 测试实时录音和音频发送
- **播放测试**: 测试TTS音频的接收和播放
- **错误测试**: 测试各种错误情况的处理
- **性能测试**: 测试长时间连接的稳定性

### 测试工具
- **内置测试音频**: 提供测试音频数据发送
- **录音测试**: 支持实时录音测试
- **状态监控**: 实时监控连接状态和性能
- **日志记录**: 详细记录所有测试过程

## 🔍 故障排除

### 常见问题
1. **连接失败**: 检查WebSocket URL和网络连接
2. **认证失败**: 确认Bearer Token有效性
3. **录音失败**: 检查麦克风权限和浏览器支持
4. **音频播放失败**: 检查音频格式和浏览器兼容性

### 调试工具
- **实时日志**: 查看详细的操作日志
- **连接状态**: 监控连接状态变化
- **统计信息**: 分析消息发送和接收情况
- **浏览器控制台**: 查看JavaScript错误信息

---

**总结**: WebSocket Bridge 2.0 测试页面完全对标原有页面功能，提供了完整的WebSocket连接测试、实时录音、TTS音频播放等功能，同时基于新的独立WebSocket实现，具有更好的稳定性和可维护性。
