<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.goclouds.crm</groupId>
        <artifactId>crm_service</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>crm_service_ai_agent</artifactId>
    <description>AI Agent</description>
    <packaging>jar</packaging>

    <dependencyManagement>
        <!-- https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk-bom -->

    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.goclouds.crm</groupId>
            <artifactId>crm_service_common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.goclouds.crm</groupId>
            <artifactId>crm_service_common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
            <scope>test</scope>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>commons-io</groupId>-->
<!--                    <artifactId>commons-io</artifactId>-->
<!--                    <version>2.11.0</version>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>

        <!-- Mockito -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.12.4</version>
            <scope>test</scope>
        </dependency>

        <!-- ByteBuddy 依赖 -->
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.11.22</version>
            <scope>test</scope>
        </dependency>

        <!-- ByteBuddy Agent 依赖 -->
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.11.22</version>
            <scope>test</scope>
        </dependency>

        <!-- Java WebSocket 客户端（可选替代 Tyrus） -->
        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
            <version>1.5.3</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>net.roseboy</groupId>
                <artifactId>classfinal-maven-plugin</artifactId>
            </plugin>
        </plugins>

    </build>



</project>
