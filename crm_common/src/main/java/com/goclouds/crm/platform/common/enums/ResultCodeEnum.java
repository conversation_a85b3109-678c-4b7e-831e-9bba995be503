package com.goclouds.crm.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 针对不同的模块返回不同的code-message <br/>
 * 
 * 通用的操作成功（200）和操作失败（500）定义在 @see AjaxResult 中
 * 
 */
@Getter
@AllArgsConstructor
public enum ResultCodeEnum {
    // 分模块声明不同的错误信息
    
    // 1xxxx 认证、鉴权 auth，用户模块 system
    TOKEN_EXPIRATION(10000, "token.expiration"),
    AUTH_NOT(10001, "auth.not"),
    COST_OVERDUE(10002, "cost.overdue"),
    ACCOUNT_LOGGED_BY_OTHER(10003, "account_logged_by_other"),
    
    // 2xxxx  customer
    CUSTOMER_PHOTO_COLOR_SETTING_INFO_CAN_NOT_BE_EMPTY(2000001, "customer.photo.color.setting.info.can.not.be.empty"),
    CUSTOMER_PHOTO_COLOR_SETTING_RULE_COUNT_LIMIT(2000002, "customer.photo.color.setting.rule.count.limit"),
    CUSTOMER_PHOTO_COLOR_IN_USE(2000003, "customer.photo.color.in.use"),
    CUSTOMER_PHOTO_COLOR_SAME_TAG_IN_USE(2000004, "customer.photo.color.same.tag.in.use"),

    // 3xxxx  call
    CALL_CENTER_TICKET_NOT_CHAT_HISTORY(300001, "call.center.ticket.not.chat.history"),

    // 4xxxx channel
    TIKTOK_NOT_EXIST_COUNTRY_CODE_APP_AUTH_INFO(400001, "tiktok.not.exist.country.code.app.auth.info"),
    TIKTOK_USE_AUTH_INFO_GET_TOKEN_ERROR(400002, "tiktok.use.auth.info.get.token.error"),
    TIKTOK_USE_AUTH_INFO_GET_SHOP_INFO_ERROR(400003, "tiktok.use.auth.info.get.shop.info.error"),
    TIKTOK_CHANNEL_CREATE_SHOP_INFO_MUST_SELECT(400004, "tiktok.channel.create.shop.info.must.select"),
    TIKTOK_CHANNEL_CREATE_SHOP_INFO_MUST_ONLY_ONE(400005, "tiktok.channel.create.shop.info.must.only.one"),
    TIKTOK_CHANNEL_CREATE_SHOP_INFO_AUTH_CODE_CAN_NOT_BE_EMPTY(400006, "tiktok.channel.create.shop.info.auth.code.can.not.be.empty"),
    DISCORD_CHANNEL_TOKEN_INVALID(400007, "discord.channel.token.invalid"),
    DISCORD_CHANNEL_APPLICATION_ID_EXIST(400008, "discord.channel.application.id.exist"),
    DISCORD_CHANNEL_BOT_TOKEN_EXIST(400009, "discord.channel.bot.token.exist"),

    // 5xxxx file，knowledge，ai_agent，aigc

    // 6xxxx  计费成本类型
    AI_Agent_INNER_COUNT_ZERO(60000, "ai.agent.inner.count.zero"),
    Agent_Copilot_COUNT_ZERO(60001, "agent.copilot.count.zero"),
    AI_Agent_OUTER_COUNT_ZERO(60002, "ai.agent.outer.count.zero"),
    AI_AGENT_USAGE_LIMIT(60003,"ai.agent.usage.limit"),
    // 7xxxx market

    // 8xxxx openapi

    // 9xxxx 其他


    // 通用的返回信息
    OPERATE_SUCCESS(200, "operate.success"),
    OPERATE_FAILURE(500, "operate.failure"),
    SYSTEM_ERROR(99999, "system.error")
    ;

    private final int code;
    private final String message;

}
