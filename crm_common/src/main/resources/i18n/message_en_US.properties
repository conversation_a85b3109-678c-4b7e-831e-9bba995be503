# \u793A\u4F8B\uFF1A\u64CD\u4F5C\u63D0\u793A
# save.success=Saved successfully

# \u901A\u7528\u4FE1\u606F
common.people.male=Male
common.people.female=Female
common.people.other=Others
operate.success=Operation Successful
operate.failure=Operation Failed
save.success=Saved Successfully
save.failure=Saved Failed
temporary.storage.success=Temporary Storage Successfully
temporary.storage.failure=Temporary Storage Failed
update.success=Update Successfully
update.failure=Update Failed
delete.success=Delete Successfully
delete.failure=Delete Failed
upload.success=Upload Successfully
upload.failure=Upload Failed
file.upload.max.size=The maximum of the uploaded file is {0}
s3.create.failure=Failed to create an S3 bucket
system.error=System Error
system.runtime.exception=Network error, please try again later.

# \u8BA4\u8BC1\u3001\u6743\u9650\u7C7B
token.expiration=Token expire
auth.not=Permission denied
auth.input.not=Please enter the corresponding permission parameters
cost.overdue=Your account is overdue, temporarily unable to use this function, please recharge in time
account_logged_by_other=Your account has been logged in elsewhere


# \u7528\u6237\u7C7B
register.error=Registration failure
login.email.password.notice=The user email address or login password is incorrect. Please confirm and log in again
login.user.not-exist.notice=The current user does not exist. Please confirm and log in again
email.not.null=The user mailbox cannot be empty
email.length.80=The user mailbox cannot exceed 80 characters
email.format=The user mailbox must conform to the mailbox entry format
password.not.null=The password cannot be empty
password.length.80=The password contains a maximum of 80 characters
company.name.not.null=The company name cannot be empty
company.name.length.80=The company name cannot exceed 80 characters
company.code.not.null=The company code cannot be empty
company.code.length.80=The company code cannot exceed 80 characters
company.address.length.2000=The company address cannot exceed 2000 characters
post.name.length.80=The post contains a maximum of 80 characters
user.name.not.null=The name cannot be empty
user.name.length.80=The name cannot exceed 80 characters
user.id.not.null=The user id cannot be empty
user.id.length.40=The user id contains a maximum of 40 characters
user.status.not.null=The user status cannot be empty
email.captcha.not.null=The email verification code cannot be empty
email.captcha.length.10=The email verification code contains a maximum of 10 characters
email.captcha.wrong=The email verification code is wrong
email.captcha.expire=The email verification code is invalid
phone.number.not.null=The mobile phone number cannot be empty
phone.number.length.80=The length of the phone number cannot exceed 80 characters
phone.format=The phone number must conform to the format of the phone number
phone.number.register.already=The phone number has been registered
email.register.already=The email address has been registered
email.captcha.send=The email verification code has been sent
email.captcha.send.error=Failed to send the email verification code
user.role.not.null=The user role must be selected
user.role.error=The user role selection is incorrect
login.user.approve.wait=This account needs to be approved. Please <NAME_EMAIL>
login.user.approve.reject=This account has been rejected. Please <NAME_EMAIL>
login.user.probation-period.notice = Your account has expired, please contact your administrator
login.user.account.deadline=The trial period of this account has expired. Please <NAME_EMAIL>
user.create.count.limit=The number of created users has reached the upper limit. Please <NAME_EMAIL>
login.user.status.invite.forbidden=The account is disabled. Please contact your administrator
login.user.status.invite.wait=This account needs to be approved. Please contact your administrator
login.user.status.forbidden=The account is disabled. Please <NAME_EMAIL>

# \u6279\u91CF\u53D1\u9001\u6D88\u606F
message.id.not.null=The message's id cannot be empty
content.id.not.null=The message's content id cannot be empty
send.channel.not.null=The sending channel cannot be empty
send.channel.length.40=The length of the sending channel cannot exceed 40 characters
send.type.not.null=The send type cannot be null
send.mode.not.null=The customer selection mode cannot be empty
subject.not.null=The topic cannot be empty
subject.length.100=The topic length cannot exceed 100 characters
message.content.not.null=The message content cannot be empty
message.content.length.2000=The message contains a maximum of 2000 characters
send.time.not.null=The sending time cannot be empty
send.time.week.not.null=The week of the sending time cannot be empty
send.type.value.error=The sending type is incorrect
send.mode.value.error=The sending mode selected by the customer is incorrect
send.mode.group.not.null=The customer selection group cannot be empty
send.send.email.not.null=No email address is included in the selected customer profile and no email will be sent
send.customer.count.limit=The number of customers selected today has reached the daily limit, please <NAME_EMAIL>

# \u7528\u6237\u5E73\u53F0\u7BA1\u7406
dept.id.length.40=The length of the department's id cannot exceed 40 characters
dept.id.not.null=The department cannot be empty
dept.name.length.80=The length of the department's name cannot exceed 80 characters
dept.name.not.null=The department's name cannot be empty
dept.code.length.80=The length of the department's code cannot exceed 80 characters
dept.code.not.null=The department's code cannot be empty
dept.parent.id.length.40=The length of parent department's id cannot exceed 40 characters
dept.parent.id.not.null=The parent department's id cannot be empty
dept.level.not.null=The department level cannot be empty
dept.level.not.operate.not.3=Only Level 3 tissue for now
dept.not.delete=This department has personal information and cannot be deleted for the time being
dept.remove.user.leader.not=Attendant leaders cannot be removed from the organization
user.self.forbidden.error=The current user cannot disable himself
user.self.remove.error=The current user cannot be deleted by itself
user.role.leader.admin.not=Administrators cannot be set as principals
user.role.leader.not=The current staff is already in charge
user.role.dept.not=The selected organizations and roles must match
user.connect.id.not.null=connectId can not be empty
user.dept.leader.only.error=A leader already exists under the organization
batch.update.ids.not.null=Select at least one user
batch.add.dept.not=Select users that are not assigned to an organization
batch.confirm.to.be.checked=Cannot include persons who are not in the pending audit status
batch.update.dept.role.not=Administrators and principals cannot modify organizations
batch.update.dept.not.role.not=Organizations cannot be modified across centers
send.invitation.email.list.not.null=The mailbox for sending invitations cannot be empty
send.invitation.number.not.null=The invitation code cannot be empty
send.invitation.number.expire=Invitation code expired

# aws\u8D26\u53F7\u548Cconnect\u6848\u4F8B
error.same.account=Same account ID exists
error.null.account=The account does not exist
error.null.case=The case does not exist
error.null.extInst=Customer extension information cannot be empty

#\u5BA2\u6237\u8D44\u6599\u5206\u7EC4\u63D0\u793A\u4FE1\u606F
customer.group.create.exit=Group name already exists
customer.group.update.notexit=group does not exist
customer.group.delete.success=Successfully deleted the group
customer.group.delete.exit=There are customers under the group and cannot be deleted
customer.remove.group.success=Successfully removed customer from group
customer.group.name.pattern = Group names can only consist of uppercase and lowercase English and Chinese characters

# \u5BA2\u6237\u8D44\u6599
customer.key.not.null=Customer ID cannot be empty
customer.email.exit=There is a customer profile with email {0}
customer.telephone.exit=There is a customer profile with mobile phone {0}
customer.media.exit={0} has customer profile with {1} as {2}
customer.no.exit=Customer information does not exist
customer.save.success=Successfully save customer information
customer.update.error=Failed to update customer information
customer.filter.id.not.null=The filter id cannot be empty
customer.filter.name.not.null=The filter name cannot be empty
customer.filter.list.not.null=The filter condition corresponding to the filter cannot be empty
customer.filter.ext.code.not.null=The filter code corresponding to the filter cannot be empty
customer.filter.ext.value.not.null=The filter value corresponding to the filter cannot be empty
customer.channel.not.null=The channel cannot be empty
customer.group.not.null=The customer group cannot be empty

# \u5DE5\u4F5C\u8BB0\u5F55\u63D0\u793A\u4FE1\u606F
error.null.record=The work record does not exist
error.null.record.transfer=The ticket is being processed and cannot be transferred
error.null.agent.online=The team has no online representatives available to transfer the order
error.null.status=The record status has been resolved and cannot be changed
work.record.contact.exists=The customer contact record already exists
work.record.exists=The work record already exists
send.email.limit.count=The number of emails sent today has reached the daily limit. Please <NAME_EMAIL>
ticket.already.collect=The order ticket has already been favorited

# \u6E20\u9053\u914D\u7F6E
channel.not.config=No channel configuration found


work.record.id=Record ID
work.record.channel.name=Channel Type
work.record.customer.name=Customer Name
work.record.customer.link.way=Customer contact information
work.record.settle.name=Seat Name
work.record.status=Status
work.record.create.time=Create time
work.record.resolve.time=Resolution Time
work.record.file.name=Word Record

customer.channel.not.exist=The source channel you filled in [{0}] does not exist, please confirm
customer.group.not.exist=The group name you filled in [{0}] does not exist, please confirm
customer.part.save.fail=Failed to save customer information with email address [{0}]. Please confirm and try saving again

# \u5BA2\u6237\u8D44\u6599\u57FA\u672C\u4FE1\u606F
customer.name=Customer First Name
customer.last.name=Customer Last Name
customer.email=Email
customer.birthday=Birthday
customer.gender=Gender
customer.phone=Phone Number
customer.label=Customer Label
customer.company.name=Company Name
customer.post=Post
customer.group.name=Group Name
customer.channel.name=Channel Name
customer.mailing.address=Mailing Address
customer.order.address=Order Address
customer.delivery.address=Delivery Address
customer.other.address=Other Address
customer.remark=Remark
customer.info=Customer Info
customer.grade.vip=VIP Customer
customer.grade.ordinary=Ordinary Customer
customer.create.time=Creation time
customer.label.exceed = Number of labels exceeded
customer.batch.modify.success = Batch modification of group succeeded
customer.batch.modify.fail = Batch modification of group failed

work.number.length.40=The work number contains a maximum of 40 characters
work.number.already.exist=The work number is already occupied
phone.number.already.exist=The phone number is already occupied
old.password.input.wrong=Original password input error
new.password.can.not.equal.old.password=The new password can not match the original password
password.update.success=Password modification successful
property.name.already.exist=The attribute name already exists
property.code.already.exist=The attribute code already exists
response.time.can.not.be.zero=Response time cannot be 0
resolve.time.can.not.be.zero=Resolution time cannot be 0
work.order.type.name.or.value.can.not.be.empty=The ticket type name or value cannot be empty
work.order.type.name.repeat=Duplicate ticket type name
work.order.type.value.repeat=Duplicate ticket type values
save.one.work.order.type.data.at.least=Save at least one ticket type definition data
work.record.type.id.occupied.by.work.order=This ticket type is already occupied by the ticket and cannot be deleted
work.update.status.termination=The current ticket status does not support termination
work.update.status.resolution=The current ticket status does not support resolution
work.update.status.resolution.solved=Ticket status updated to Resolved
work.update.status.resolution.terminated=Ticket status updated to Terminated
work.update.status.resolution.transferred=Ticket status updated to Transferred
work.update.status.Association=The ticket is already associated, please do not duplicate the association,The ticket number is:
work.update.status.level=ticket level not adjusted
work.update.status.batch.termination=The following tickets do not support resolution, and the ticket number is as follows:
work.cannot.assign=The current ticket cannot be assigned
work.cannot.claim=The current ticket cannot be claimed
work.create.error=Administrators cannot create work orders
work.solve.error=Administrator cannot batch label work orders
work.cannot.binding=The current tickets cannot be assigned
user.last.name.not.null=The last name cannot be empty
user.last.name.length.80=The last name cannot exceed 80 characters
work.update.agent.fail=No other agents are online at the moment, transfer is not possible
work.update.not.effective.agent=There are no other available agents in the department, transfer is not possible


work.reminder.no = No reminder
work.reminder.yes = Reminded

work.status.undistributed = To be allocated
work.status.agent = Need customer service to handle
work.status.custom = Customer processing required
work.status.solve = resolved
work.status.termination = terminated
work.status.transfer = Transferred order

work.accept.type.artificial = Assigned by seat administrator
work.accept.type.claim = Proactively claim
work.accept.type.automatic = Automatic ticket allocation


work.create.automatic = Auto creation
work.create.artificial = Manual creation


work.beyond.unresolved = Beyond unresolved
work.status.unresolved = Not exceeding unresolved
work.timeout.resolution = Timeout resolution
work.time.solve = Resolve on time
work.automatic.solve = Automatically processed



title.ticket.id = ticket ID
title.channel.name = Channel Name
title.priority = priority
title.service.objectives = SLA Service Objectives
title.ticket.state = state
title.custom.phone = customer phone
title.agent.name = Customer service name
title.ticket.describe = ticket Description




# aigc
aigc.chat.content.id.not.null=contentId cannot be empty
aigc.chat.content.prompt.id.not.null=promptId cannot be empty
aigc.chat.content.prompt.id.length.40=promptId Specifies a maximum of 40 characters
aigc.chat.content.question.not.null=The request question cannot be empty
aigc.chat.content.answer.not.null=The answer to the question cannot be empty

option.data.can.not.be.empty=Option data cannot be empty
option.name.or.value.can.not.be.empty=Option name or option value cannot be empty
option.name.repeat=Option name is duplicated
option.value.repeat=Option value is duplicated

homepage.channel.ticket.status.timeout=Timeout
homepage.channel.ticket.status.processing=Processing
homepage.channel.ticket.status.pending.assignment=Pending
homepage.channel.ticket.status.resolved=Resolved

ai.agent.inner.count.zero=The current balance of the internal agent AIGC is 0, please recharge in time!
agent.copilot.count.zero=The current balance of AIGC for seat assistance is 0, please recharge in a timely manner!
ai.agent.outer.count.zero=The current balance of the external intelligent agent AIGC is 0, please recharge in time!

# knowledge \u77E5\u8BC6\u5E93
knowledge.document.index.max.count=The number of document knowledge bases has reached the upper limit, Please <NAME_EMAIL>
knowledge.document.index.create.not.success=The document knowledge base cannot be updated because it is not created successfully
knowledge.document.id.not.null=Please select a knowledge base document
knowledge.document.id.length.40=The length of the knowledge base document ID cannot exceed 40 characters
knowledge.document.name.not.null=The knowledge base name cannot be empty
knowledge.document.name.length.1000=The knowledge base name contains a maximum of 1000 characters
knowledge.document.name.pattern=The knowledge base name contains only letters, numbers, and hyphens
knowledge.document.description.length.1000=The knowledge base introduction contains a maximum of 1000 characters
knowledge.document.description.pattern=Knowledge base Introduction The input does not conform to the format
knowledge.document.type.not.null=The knowledge base type cannot be empty
knowledge.document.type.list.exists=The type of knowledge base must be of the specified type.
knowledge.document.sync.no.doc=No documents need to be synchronized
knowledge.document.syncing.job=Documents are being synchronized, please try again later
knowledge.document.sync.success=Documents are being synchronized, please check later
knowledge.document.sync.failure=Document synchronization failed
knowledge.document.file.id.not.null=Please select a document
knowledge.document.file.suffix.error=This document type is not supported
knowledge.document.role.list.exists=The role of the knowledge base must be the specified role.
knowledge.document.role.administrator.not.empty=There must be at least one administrator under the knowledge base.
knowledge.document.role.administrator.not.delete.self=The current operation is not allowed to remove itself from the administrator list.
knowledge.document.role.user.only.one=A user's knowledge base role can only be assigned one type.
knowledge.document.crawler.rule.id.not.null=Please select a web scraping rule
knowledge.document.crawler.rule.id.length.40=The web scraping rule ID cannot exceed 40 characters
knowledge.document.crawler.rule.name.not.null=The name of the web crawling rule cannot be empty
knowledge.document.crawler.rule.name.length.80=The length of the web crawl rule name cannot exceed 80 characters
knowledge.document.crawler.rule.url.not.null=The web crawl URL cannot be empty
knowledge.document.crawler.rule.frequency.not.null=The crawl frequency of a website cannot be empty
knowledge.document.crawler.rule.update.not.null=The rules for updating web crawled data cannot be empty
knowledge.document.crawler.rule.deep.not.null=Web page crawling depth cannot be empty
knowledge.document.crawler.rule.main.url.not.null=The crawling of the current main domain name webpage cannot be empty
knowledge.document.crawler.rule.main.url.list.exists=You must pass the specified type to crawl the current main domain web pages\uFF080\uFF0C1\uFF09
knowledge.document.crawler.rule.update.list.exists=The data update rules for web scraping must pass the specified type\uFF081\uFF0C2\uFF09
knowledge.document.crawler.rule.deep.list.exists=The web crawling depth must pass the specified value\uFF081-5\uFF09
knowledge.document.crawler.rule.frequency.list.exists=The web crawling frequency must pass a specified type\uFF081\uFF0C2\uFF09
knowledge.document.crawler.rule.prefix.list.exists = The crawling of the current webpage prefix must be restricted to a specified type (0, 1)
knowledge.document.crawler.rule.prefix.not.null = The crawling of the current webpage prefix must not be empty
knowledge.document.crawler.rule.webpage.list.exists = The maximum total number of crawled webpages must be between 1 and 100,000
knowledge.document.crawler.rule.webpage.not.null = The maximum total number of crawled webpages must not be empty
knowledge.document.crawler.rule.content.type.list.exists = The content type for crawling must specify a type (1-4)
knowledge.document.crawler.rule.content.type.not.null = The content type for crawling must not be empty
knowledge.document.crawler.rule.update.not=The current status is crawling, and cannot be modified temporarily.
knowledge.document.crawler.rule.delete.not=The current status is crawling, and cannot be deleted temporarily
knowledge.document.crawler.rule.recrawl.not=The current status is crawling, so it cannot be re-crawled for now.
knowledge.document.upload.exist=The document {0} already exists, please do not upload it again
knowledge.document.upload.filename.special.character=The file name cannot contain special characters

knowledge.synonym.info.not.null=Terminology rule input data cannot be empty
knowledge.synonym.info.not.regex=The terminology rule input data does not meet the format requirements
knowledge.synonym.info.num.more.than.limit=Can only add up to 10 words

knowledge.document.knowledge=Document Knowledge Base
knowledge.question.answer.knowledge=Q&A Knowledge Base

knowledge.synonym.info.no.success.kendra=There is currently no successfully created document knowledge base, so synchronization of synonyms is not supported

knowledge.synonym.info.count.limit=Terminology rules cannot exceed 10,000 entries.
knowledge.synonym.info.file.size.limit=Terminology rules file size cannot exceed 5MB.

no.support.this.language.query=The language query is not supported

format.not.match.requirement=Email information configuration is missing necessary parameters
channel.email.account.unique.message=The Email account already exists
channel.config.robot.exceeded=The number of robots has exceeded
instance.web.channel.limit=A WEB chat channel for this language already exists under the domain name of this website
instance.app.channel.limit=A APP chat channel for this language already exists under the domain name of this website
port.error.message=IMAP/SMTP port number can only be entered as a number, with a length not exceeding 10
imap.smtp.error.message=IMAP/SMTP addresses can only input uppercase and lowercase letters, numbers, and '.'
rab.error.message=Unknown robot answer cannot exceed 200 characters
channel.name.error.message=Channel names cannot exceed 80 characters
channel.name.unique.message=The channel name already exists
chat.welcome.content.error.message=Welcome message cannot exceed 200 characters
chat.title.name.error.message=Chat box name cannot exceed 40 characters
rab.number.error.message=The number of robots has exceeded

answer.questions.not.null =Please select a language

no.search.answer=I don't know

data.can.not.be.empty=Data cannot be empty
whatsapp.phone.number.already.exist=That phone number already exists
contactId.data.is.invalid=Your contactId data is invalid
channel.config.setting.error=Your system configuration has issues, please contact the administrator

marketing.email.template.name.can.not.be.empty=The template name cannot be empty
marketing.email.template.name.exist=The template name already exists
marketing.email.template.content.can.not.be.empty=The content inside the paragraph tags cannot be empty
marketing.email.template.type.can.not.be.empty=Template type cannot be empty
marketing.email.template.generate.local.template.file.error=Failed to generate local mail template file, please contact administrator
marketing.email.template.name.length.limit=The length of the template name cannot exceed 100 characters
marketing.email.template.description.content.length.limit=The length of the template description cannot exceed 255 characters
marketing.email.template.type.can.length.limit=The length of the template type cannot exceed 200 characters
marketing.email.template.can.not.be.delete=Only custom templates can be deleted
marketing.email.template.can.not.be.update=Only custom templates can be edited
marketing.email.template.data.not.exist=Data does not exist



activity.node.start = start
activity.node.end = end

marketing.event.eventId.not.null=Please select a marketing event
marketing.event.eventId.length.40=The length of the marketing event event_id cannot exceed 40 characters
marketing.event.activityId.not.null=Please select a marketing campaign
marketing.event.activityId.length.40=The length of the marketing activity activity_id cannot exceed 40 characters
marketing.event.eventName.not.null=The marketing event name cannot be empty
marketing.event.eventName.length.100=The length of the marketing event name cannot exceed 100 characters
marketing.event.eventType.not.null=The marketing event type cannot be empty
marketing.event.eventType.list.exists=The event type must be selected from the specified types
marketing.event.custom.autoEventNotificationEmail.length.255=The custom marketing event notification email length cannot exceed 255 characters
marketing.event.marketingType.list.exists=The marketing method must be selected from the specified type
marketing.event.planabComparing.list.exists=A/B testing must select from specified types in terms of comparison
marketing.event.channelId.not.null=The marketing channels cannot be empty
marketing.event.channelId.length.40=The channel information of the marketing event type, channel_id, contains 40 characters
marketing.event.detail.typeCustomerId.not.null=The typeCustomerId cannot be empty in a marketing event
marketing.event.detail.typeCustomerId.length.40=The typeCustomerId for selecting a customer in a marketing event cannot exceed 40 characters long
marketing.event.detail.eventDetailType.list.exists=A/B testing marketing types must select from specified types
marketing.event.detail.aiMarketingDescription.length.255=AI marketing ideas outline cannot exceed 255 characters in length
marketing.event.detail.aiUserPortraitGeneration.length.255=AI generated customer portrait length cannot exceed 255 characters
marketing.event.detail.customerLabels.length.255=The length of labels automatically set for customers cannot exceed 255 characters
marketing.event.detail.subdivisionMode.not.null=The customer selection method cannot be empty
marketing.event.detail.subdivisionMode.list.exists=Customers must be selected from the specified type
marketing.event.detail.subdivisionId.not.null=Please select a customer segmentation
marketing.event.detail.subdivisionId.length.40=The customer segment subdivision_id in a marketing event cannot exceed 40 characters long
marketing.event.detail.typeContentId.not.null=typeContentId cannot be empty in marketing events
marketing.event.detail.typeContentId.length.40=The typeContentId length for content sent in marketing events cannot exceed 40 characters
marketing.event.detail.templateId.length.40=The templateId for a template in a marketing event cannot exceed 40 characters long
marketing.event.detail.content.not.null=The content cannot be empty
marketing.event.detail.typeTimeId.not.null=typeTimeId cannot be empty in marketing events
marketing.event.detail.typeTimeId.length.40=The typeTimeId length for sending time in marketing events cannot exceed 40 characters
marketing.event.detail.sendType.not.null=The sending method type cannot be empty
marketing.event.detail.sendType.list.exists=The sending type must be selected from the specified types
marketing.event.detail.sendDate.length.40=The length of the sending date cannot exceed 40 characters
marketing.event.detail.sendTime.length.40=The message length cannot exceed 40 characters
marketing.event.detail.sendTime.format=The time format is hh:mm:ss
marketing.event.detail.customSendTime.format=The custom sending time format is yyyy-MM-dd hh:mm:ss
marketing.event.detail.useReceiverTimezone.list.exists=Whether to use the recipient's local timezone must be selected as yes or no
marketing.event.detail.defaultTimezone.length.40=Unable to identify the recipient time zone using the default time zone length cannot exceed 40 characters
marketing.event.detail.sendDate.format=The date format is yyyy-MM-dd
marketing.event.detail.sendDate.date.format=Sending dates must be between 1-31 when the sending type is monthly
marketing.event.detail.sendDate.week.format=The sending type must be between Monday to Sunday when set as weekly
marketing.event.start.not.status.draft=Draft marketing events cannot be clicked to start
marketing.event.start.not.status.sending=Marketing events with a status of in progress or sending cannot be clicked to start
marketing.event.start.not.status.finish=Marketing events that are ended cannot be clicked to start
marketing.event.pause.not.status.draft=Draft marketing events cannot be clicked to pause
marketing.event.pause.not.status.not.start=Marketing events that have not started cannot be clicked to pause
marketing.event.pause.not.status.pause=Suspended marketing events cannot be clicked to pause
marketing.event.pause.not.status.finish=A marketing event that has ended status cannot be clicked to pause
marketing.event.activity.status.not.start=The current campaign has not started
marketing.event.activity.status.pause=The current campaign has been suspended
marketing.event.activity.status.finish=The current campaign has ended

marketing.event.customer.list=Customer list

marketing.activity.name=Campaign Name
marketing.event.channel.type=Marketing Channel Type
marketing.event.send.channel=Sending Channel
marketing.event.name=Marketing Event
marketing.event.marketing.type=Marketing Type
marketing.event.event.detail.type=Types of A/B Tests
marketing.event.batch.num=Marketing Event Batch
marketing.event.customer.name=Customer Name
marketing.event.customer.link.way=Customer contact information
marketing.event.status=Status
marketing.event.error.reason=Failure Reason

marketing.event.channel.email=Email
marketing.event.channel.facebook=Facebook
marketing.event.channel.whatsapp=WhatsApp
marketing.event.channel.phone=Phone
marketing.event.channel.web.chat=Web Chat
marketing.event.channel.app.chat=App Chat
marketing.event.channel.web.video=Web Video
marketing.event.channel.app.video=App Video

marketing.event.marketing.type1=Standard test
marketing.event.marketing.type2=A/B testing

marketing.event.marketing.plan.a=Plan A
marketing.event.marketing.plan.b=Plan B

marketing.event.marketing.status.delivery=Delivery
marketing.event.marketing.status.open=Open
marketing.event.marketing.status.click=Click
marketing.event.marketing.status.complaint=Complaint
marketing.event.marketing.status.unsubscription=Unsubscription
marketing.event.marketing.status.bounce=Bounce
marketing.event.marketing.status.delivery.delay=DeliveryDelay
marketing.event.marketing.status.reject=Reject
marketing.event.marketing.status.rendering.failure=Rendering Failure


activity.not.delete = Underneath the campaign, there are marketing events, deletion is not allowed
activity.name.exist = The campaign name already exists, addition failed
activity.update.status = The campaign status modification must be selected within the specified types
activity.name.not.null = The name of the campaign cannot be empty
activity.target.not.null = The campaign goal cannot be empty
activity.type.not.null = The campaign type cannot be empty
activity.start.time.not.null =The campaign start time cannot be empty
activity.end.time.not.null = The campaign end time cannot be empty

marketing.event.test.send.email.content.empty=Email content not set

marketing.event.email.status.yes=Yes
marketing.event.email.status.no=No



company.already.register =Your subscription contract in the marketplace has been successfully registered. We are currently undergoing review. Please do not resubmit your registration information!


#company.already.register =Your subscription contract in the marketplace has been successfully registered. We are currently undergoing review. Please do not resubmit your registration information!


#\u7EDF\u8BA1\u8868
contact.id = Contact Id
#call.time = Call Time
#end.time = End Time
#acw.time = ACW Time
total.time = Total Time
interaction.time = Interaction Time
queue.wait.time =  Queue Wait Time
#call.channel = Call Channel
reception.agent = Reception Agent
agent.group = Agent Group
queue.name = Queue Name
acw.duration = ACW Duration
work.order.number = ticket Number
#on.hold.time = On Hold Time
#on.hold.number = On Hold Number
#is.switch = Is Switch
hanging.type =  Hanging Type
system.phone = System Phone
satisfaction.rating = Satisfaction Rating

call.time = Agent Answer Time
end.time = Hang Up Time
acw.time = ACW End Time
call.channel = Channel
on.hold.time = Agent OnHold Time
on.hold.number = Agent OnHold Times
is.switch = Transfer Or Not
start.time = Start Time
in.coming.out.coming = Incoming/Outgoing
customer.phone2 = Customer Phone Number
initial.contact.id = Initial Contact ID
previous.contact.id = Previous Contact ID
next.contact.id = Next Contact ID

agent.name = Agent Name
accumulated.online.duration = Accumulated Online Duration
accumulated.idle.duration = Accumulated Idle Duration
accumulated.reception.duration = Accumulated Reception Duration
task.time.utilization.rate = Task Time Utilization Rate
unresponsive.quantity = Unresponsive Quantity
reception.contacts.quantity = Reception Contacts Quantity
response.rate = Response Rate
avg.working.hours.after.contact = Average Working Hours After Contact
avg.customer.retention.time = Average Customer Retention Time

#queue.name = Queue Name
#avg.working.hours.after.contact = Average Working Hours After Contact
avg.agent.interaction.time = Average Agent Interaction Time
#avg.customer.retention.time = Average Customer Retention Time
avg.queue.abandonment.time = Average Queue Abandonment Time
avg.queue.waiting.time = Average Queue Waiting Time 
abandon.contact.quantity = Abandon Contact Quantity
queued.contacts.quantity = Queued Contacts Quantity

real.work.ticket=Agent tickets
robot.work.ticket=Robot tickets
resolve.ticket.count=Resolved ticket count
un.resolve.ticket.count=Unresolved ticket count
waiting.for.reply.ticket.count=Waiting for customer reply ticket count

zero.to.three.hour=0-1 hour
one.to.three.hour=1-3 hour
three.to.eight.hour=3-8 hour
over.eight.hour=Over 8 hours

statistic.work.record.sheet.name=Statistic report
statistic.export.agent.group.name=Agent group name
statistic.export.agent.name=Agent name
statistic.export.work.order.total.count=Total number of work orders
statistic.export.work.order.percent=Percent
statistic.export.work.order.feedback.rate=Feedback rate
statistic.export.work.order.classification=Classification
statistic.export.time.range=Time range

statistic.export.work.record.filename1=Total number of tickets handled by agents
statistic.export.work.record.filename2=Number of tickets classified by channel
statistic.export.work.record.filename3=ticket status distribution
statistic.export.work.record.filename4=ticket type distribution
statistic.export.work.record.filename5=ticket priority distribution
statistic.export.work.record.filename6=Average ticket resolution time
statistic.export.work.record.filename7=ticket processing time distribution
statistic.export.work.record.filename8=SLA report
statistic.export.work.record.filename9=Satisfaction report_Agent dimension
statistic.export.work.record.filename10=Satisfaction Report_ticket Type Dimension
statistic.export.work.record.filename11=Satisfaction Report_Channel Dimension
statistic.export.work.record.filename12=Robot ticket Report
statistic.export.work.record.filename13=Number of Robot tickets by Channel
statistic.export.work.record.filename14=Proportion of Robot Work Orders
statistic.export.work.record.filename15=Trend Changes in Number of tickets Processed by Each Agent
statistic.export.work.record.filename16=Trend Changes in Average Satisfaction Score of Each Agent
statistic.export.work.record.filename17=Trend Changes in Time of ticket Processing by Each Agent
statistic.export.work.record.filename18=Statistical Reply Rate of Each Agent
statistic.export.work.record.filename19=Query Top 10 Customer Work Orders

statIndex.export.work.record.filename1=Contact Details
statIndex.export.work.record.filename2=Agent Historical Work Indicators
statIndex.export.work.record.filename3=Historical Queue Indicators

#web.chat.count=Web Chat ticket Count
#phone.count=Phone ticket Count
#app.chat.count=APP Chat ticket Count
#email.count=Email ticket Count
#whatsapp.count=WhatsApp ticket Count
#web.online.video.count=Web Online Video ticket Count

agenty.name=Agent Name
work.order.count=ticket Count
work.order.avg.resolution.time=Average ticket Resolution Time
rank=Ranking
#customer.name=\u5BA2\u6237\u540D\u79F0
customer.work.order.count=Customer ticket Count
#satisfaction.rating=Satisfaction Rating

connect.alias=Contact Lines

knowledge.space.limit.warn=Your knowledge base space has reached its limit. Please contact the administrator
#\u81EA\u6211\u8BC4\u4F30\u8BE6\u60C5
evaluation.type=Evaluation Type
knowledge.name=Knowledge Base Name
channel.type=Channel Type
channel.name=Channel Name
questions=Questions
robot.answer=AI Chatbot's Answer
word.record.code=Ticket No
average.score=Average Score
loyalty=Closeness to the Facts Score
answer.relevance=Answer Relevance Score
context.accuracy=Context Precision Score
semantic.similarity.answers=Answer Semantic Similarity Score 
correct.answer=Answer Correctness Score
custom.name=Custom Name
custom.question.time=Custom Question Time

statIndex.export.self.assessment.filename1=Self-evaluation Report Details
self.assessment.scoreFile.time.row=Time
self.assessment.one.score.filename2=Self-evaluation Accuracy Score Trend Chart
self.assessment.multi.scores.filename3=Self-evaluation Accuracy Score Trend Chart For Each Dimension

manual.evaluation.type=Manual Evaluation Type
automatic.evaluation.type=Automatic Evaluation Type

channel.diff.id.unique.message=Account ID already exists

statIndex.export.like.dislike.filename=Likes and Dislikes Details List
interaction.type=Interaction Type
liked=Liked
disliked=Disliked
no.operation=No Operation
custom.voice.column.like=like
custom.voice.column.dislike=dislike
custom.voice.filename1=Likes and Dislikes Interaction Data Trend Chart
custom.voice.filename2=Number of likes and dislikes interactions on each channel

answer.type = Answer Type
answer.type1 = FAQ Normal Answer
answer.type2 = RAG,Lack of Knowledge
answer.type3 = RAG,I don't know
answer.type4 = RAG,Normal Answer

channel.facebook.id.unique.message=Facebook public page already exists
channel.instagram.id.unique.message=Instagram account already exists
channel.line.id.unique.message=Line account already exists
channel.wechat.biz.id.unique.message=WeChatBiz account already exists
channel.wechat.official.account.id.unique.message=WeChat official account already exists


channel.empower.fail = Authorization failed, please contact the administrator

agent.access.channel=Agent access channel cannot be empty
receive.ticket.type.not.null=Chat ticket receiving method cannot be empty

#\u5BA2\u6237\u6807\u7B7E
customer.tag.save.one.data.at.least=Please save at least one tag
customer.tag.type.content.or.color.can.not.be.empty=Tag content or tag color cannot be empty
customer.tag.content.repeat=The tag cannot be duplicated
customer.tag.content.length.limit=The length of the tag content cannot exceed 200 characters
customer.tag.content.already.exist =The tag content you filled in [{0}] is already exists, please confirm
customer.tag.operate.one.data.at.least=Please operate at least one tag
customer.tag.category.content.can.not.be.empty=Tag category cannot be empty
customer.tag.category.content.length.limit=The length of the tag category content cannot exceed 200 characters
customer.tag.category.content.repeat=The tag category already exists, please do not add it again
customer.tag.category.in.use.can.not.delete=This category is already in use and cannot be deleted
customer.tag.batch.add.result=You have tagged {0} customers this time. Successful tags: {1}, Failed tags: {2}.
customer.tag.scope.select.one.at.least=Select at least one tag application scope


#\u70ED\u7EBF\u6307\u6807
hotline.missed.call=Missed Calls
hotline.ivr.abandon=IVR Abandonment
hotline.queue.abandon=Queue Abandonment
hotline.non.service.time.inbound=After-Hours Calls
hotline.non.service.time.calling=Agents on Call
hotline.non.service.time.queue=Calls in Queue
hotline.non.service.time.inbound.num=Inbound Volume
hotline.non.service.time.outbound.num=Outbound Volume

#AiAgent\u76F8\u5173
intent.category.delete.failure=There are intents under the current intent category, and it cannot be deleted directly. Please delete the intents under this category first
intent.delete.failure=There are AI agents under the current intent, so the intent cannot be deleted directly
agent.binding.failure=An intent can only have one agent within the same channel type
aiagent.variable.repeat=The variable name already exists, please do not add it again
intent.name.duplicate=Intent name cannot be duplicated.
intent.script.max.limit=An intent can contain a maximum of 3 utterances
intent.attribute.max.limit=An intent can contain a maximum of 4 variables
agent.not.found=No corresponding AI Agent found
agent.name.duplicate=AI Agent name cannot be duplicated.
aiagent.variable.name.null=Variable name cannot be empty
agent.create.beyond=Intent agent creation exceeds quantity limit
intent.create.beyond=Intent creation exceeds quantity limit
aiagent.test.flow.intent.mapping.fail=Intent hit failure
intent.update.script.type=The intent has been bound to an AI agent and cannot modify the script type
agent.voice.binding.phone=The number {0} has already been bound by another agent. Please try again with a different number.

agent.create.user.chanel.fail = The available seats for channel {0} are full. Please contact the ConnectNow administrator to upgrade.
agent.agent.status.creat.fail = The current {0} name already exists, please rename it

agent.agent.sal.rul.creat.fail = The current {0} rule name already exists, please rename it
agent.agent.sal.rul.channel.record.creat.fail = The current {0} channel type and {1} work order type already exist, please filter again
agent.agent.sal.rul.channel.creat.fail = The current {0} channel type already exists, please filter again
agent.agent.sal.rul.record.creat.fail = The current {0} ticket type already exists, please filter again
agent.agent.dept.max.active.chat.num = The maximum number of active chats per seat cannot exceed 100
#\u8BA1\u91CF\u8BA1\u8D39
ai.agent.outer.usage.limit=Insufficient balance: Your remaining available calls for external AI agents are less than {0}, please recharge in time
ai.copilot.usage.limit=Insufficient balance: Your remaining available calls for Agent copilot are less than {0}, please recharge in time
ai.robot.phone.usage.limit=Insufficient balance: Your remaining available minutes for phone robot are less than {0} minutes, please recharge in time
ai.phone.expenses.usage.limit=Insufficient balance: Your current call balance is below {0}. Please top up promptly.
ai.agent.usage.limit=Insufficient balance: Currently, the remaining adjustable times for your AI/AGC is less than {0}. Please recharge promptly.

channel.company.pay.version.add.limit=Your company's current version only supports adding {0} types of channels.

work.message.reminding.add.fail=This message content or reminder time already exists. Please modify and try again.
work.message.reminding.update.fail=This message content or reminder time already exists. Please modify it.
#\u667A\u80FD\u586B\u5355
ticket.smart.fill.ticket.type.can.not.be.empty=Please select a ticket type
ticket.smart.fill.configure.properties.can.not.be.empty=Configuration field cannot be empty
ticket.smart.fill.attr.name.can.not.be.empty=Field name cannot be empty
ticket.smart.fill.attr.value.example.can.not.be.empty=Example value cannot be empty
ticket.smart.fill.attr.describe.can.not.be.empty=Description cannot be empty
ticket.smart.fill.attr.name.repeat=Field name already exists
ticket.smart.fill.language.and.type.union.exist=Combination of language code and ticket type already exists
ticket.smart.fill=Intelligent ticket filling rule not found for this ticket type. Contact admin to add one in System settings > Intelligent ticket filling.


# \u5E73\u53F0\u7528\u6237\u7BA1\u7406 \u521B\u5EFA\u7528\u6237\u548C\u66F4\u65B0\u7528\u6237 \u6839\u636E\u4E0D\u540C\u89D2\u8272\u9650\u5236\u56E2\u961F\u7EA7\u522B\u9009\u62E9
#\u5BA2\u670D\u4EBA\u5458\uFF1A
user.platform.call.center.limit=Only third-level teams under Contact Center can be selected for creation
#\u8FD0\u8425\u4EBA\u5458\uFF1A
user.platform.marketing.center.limit=Only third-level teams under Marketing Center can be selected for creation
#\u7BA1\u7406\u5458\uFF1A
user.platform.company.level.limit=Administrators can only be created under first-level organizations of the company


work.translate.ok=Translation task submitted. Please refresh the page later to see the results.

#TikTok\u6E20\u9053
tiktok.not.exist.country.code.app.auth.info=License information for the current region does not exist, please verify
tiktok.use.auth.info.get.token.error=Failed to get token during authorization
tiktok.use.auth.info.get.shop.info.error=Failed to get shop information after authorization
tiktok.channel.create.shop.info.must.select=A shop must be selected
tiktok.channel.create.shop.info.must.only.one=This shop is already in use, please select another one
tiktok.channel.create.shop.info.auth.code.can.not.be.empty=Please provide the authorization code
discord.channel.token.invalid=The provided token is invalid
discord.channel.application.id.exist=This App ID already exists
discord.channel.bot.token.exist=The bot token already exists

#\u5BA2\u6237\u5934\u50CF\u989C\u8272\u8BBE\u7F6E
customer.photo.color.setting.info.can.not.be.empty=Customer avatar color settings cannot be empty
customer.photo.color.setting.rule.count.limit=Only {0} customer avatar color rules can be added
customer.photo.color.in.use=The selected color is already used by another rule
customer.photo.color.same.tag.in.use=The tag '{1}' under category '{0}' is already used in another avatar color rule

batch.faq.ok=Upload is in progress, please refresh the page later to check.

batch.faq.fail=This file is already being uploaded, please refresh the page later to view

# AI\u667A\u80FD\u8D28\u68C0
call.center.ticket.not.chat.history=The current ticket conversation is too short and does not meet the minimum requirements for intelligent quality inspection