# \u793A\u4F8B\uFF1A\u64CD\u4F5C\u63D0\u793A
# save.success=\u4FDD\u5B58\u6210\u529F

# \u901A\u7528\u7C7B
common.people.male=M\u00E4nnlich
common.people.female=Weiblich
common.people.other=Andere
operate.success=Vorgang erfolgreich
operate.failure=Vorgang fehlgeschlagen
save.success=Speichern erfolgreich
save.failure=Speichern fehlgeschlagen
temporary.storage.success=Zwischenspeichern erfolgreich
temporary.storage.failure=Zwischenspeichern fehlgeschlagen
update.success=Aktualisierung erfolgreich
update.failure=Aktualisierung fehlgeschlagen
delete.success=L\u00F6schen erfolgreich
delete.failure=L\u00F6schen fehlgeschlagen
upload.success=Hochladen erfolgreich
upload.failure=Hochladen fehlgeschlagen
file.upload.max.size=Die maximale Dateigr\u00F6\u00DFe betr\u00E4gt {0}
s3.create.failure=Erstellung des S3-Buckets fehlgeschlagen
system.error=Systemfehler
system.runtime.exception=Netzwerkfehler, bitte versuchen Sie es sp\u00E4ter erneut

# Authentifizierung, Berechtigung
token.expiration=Token abgelaufen
auth.not=Keine Berechtigung
auth.input.not=Bitte geben Sie die entsprechenden Berechtigungsparameter ein
cost.overdue=Ihr Konto ist \u00FCberf\u00E4llig, diese Funktion ist vor\u00FCbergehend nicht verf\u00FCgbar, bitte laden Sie Ihr Konto rechtzeitig auf
account_logged_by_other=Ihr Konto ist an einem anderen Ort angemeldet

# Benutzer
register.error=Registrierung fehlgeschlagen
login.email.password.notice=Benutzer-E-Mail oder Passwort falsch, bitte \u00FCberpr\u00FCfen und erneut anmelden
login.user.not-exist.notice=Benutzer existiert nicht, bitte \u00FCberpr\u00FCfen und erneut anmelden
email.not.null=Benutzer-E-Mail darf nicht leer sein
email.length.80=Benutzer-E-Mail darf nicht l\u00E4nger als 80 Zeichen sein
email.format=Benutzer-E-Mail muss dem E-Mail-Format entsprechen
password.not.null=Passwort darf nicht leer sein
password.length.80=Passwort darf nicht l\u00E4nger als 80 Zeichen sein
company.name.not.null=Firmenname darf nicht leer sein
company.name.length.80=Firmenname darf nicht l\u00E4nger als 80 Zeichen sein
company.code.not.null=Firmencode darf nicht leer sein
company.code.length.80=Firmencode darf nicht l\u00E4nger als 80 Zeichen sein
company.address.length.2000=Firmenadresse darf nicht l\u00E4nger als 2000 Zeichen sein
post.name.length.80=Berufsbezeichnung darf nicht l\u00E4nger als 80 Zeichen sein
user.name.not.null=Name darf nicht leer sein
user.name.length.80=Name darf nicht l\u00E4nger als 80 Zeichen sein
user.id.not.null=Benutzer-ID darf nicht leer sein
user.id.length.40=Benutzer-ID darf nicht l\u00E4nger als 40 Zeichen sein
user.status.not.null=Benutzerstatus darf nicht leer sein
email.captcha.not.null=E-Mail-Captcha darf nicht leer sein
email.captcha.length.10=E-Mail-Captcha darf nicht l\u00E4nger als 10 Zeichen sein
email.captcha.wrong=E-Mail-Captcha falsch
email.captcha.expire=E-Mail-Captcha abgelaufen
phone.number.not.null=Telefonnummer darf nicht leer sein
phone.number.length.80=Telefonnummer darf nicht l\u00E4nger als 80 Zeichen sein
phone.format=Telefonnummer muss dem Telefonnummernformat entsprechen
phone.number.register.already=Diese Telefonnummer ist bereits registriert
email.register.already=Diese E-Mail ist bereits registriert
email.captcha.send=E-Mail-Captcha wurde gesendet
email.captcha.send.error=E-Mail-Captcha-Versand fehlgeschlagen
user.role.not.null=Benutzerrolle muss ausgew\u00E4hlt werden
user.role.error=Benutzerrolle falsch ausgew\u00E4hlt
login.user.approve.wait=Dieses Konto wartet auf Genehmigung, bitte <NAME_EMAIL>
login.user.approve.reject=Dieses Konto wurde abgelehnt, bitte <NAME_EMAIL>
login.user.probation-period.notice = Ihr konto ist abgelaufen, bitte kontaktieren sie ihren administrator
login.user.account.deadline=Die Testphase dieses Kontos ist abgelaufen, bitte <NAME_EMAIL>
user.create.count.limit=Die Anzahl der erstellten Benutzer hat das Limit erreicht, bitte <NAME_EMAIL>
login.user.status.invite.forbidden=Dieses Konto wurde deaktiviert, bitte kontaktieren Sie Ihren Administrator
login.user.status.invite.wait=Dieses Konto wartet auf Genehmigung, bitte kontaktieren Sie Ihren Administrator

# Massenversand von Nachrichten
message.id.not.null=Nachrichten-ID darf nicht leer sein
content.id.not.null=Inhalts-ID darf nicht leer sein
send.channel.not.null=Versandkanal darf nicht leer sein
send.channel.length.40=Versandkanal darf nicht l\u00E4nger als 40 Zeichen sein
send.type.not.null=Versandart darf nicht leer sein
send.mode.not.null=Kundenauswahlmethode darf nicht leer sein
subject.not.null=Betreff darf nicht leer sein
subject.length.100=Betreff darf nicht l\u00E4nger als 100 Zeichen sein
message.content.not.null=Nachrichteninhalt darf nicht leer sein
message.content.length.2000=Nachrichteninhalt darf nicht l\u00E4nger als 2000 Zeichen sein
send.time.not.null=Sendezeit darf nicht leer sein
send.time.week.not.null=Wochentag der Sendezeit darf nicht leer sein
send.type.value.error=Versandart falsch
send.mode.value.error=Versandart der Kundenauswahl falsch
send.mode.group.not.null=Kundenauswahlgruppe darf nicht leer sein
send.send.email.not.null=In den ausgew\u00E4hlten Kundendaten ist keine E-Mail-Adresse angegeben, es wird keine E-Mail gesendet
send.customer.count.limit=Die Anzahl der ausgew\u00E4hlten Kunden hat das Tageslimit erreicht, bitte <NAME_EMAIL>

# Benutzerplattformverwaltung
dept.id.length.40=Organisations-ID darf nicht l\u00E4nger als 40 Zeichen sein
dept.id.not.null=Organisation darf nicht leer sein
dept.name.length.80=Organisationsname darf nicht l\u00E4nger als 80 Zeichen sein
dept.name.not.null=Organisationsname darf nicht leer sein
dept.code.length.80=Organisationscode darf nicht l\u00E4nger als 80 Zeichen sein
dept.code.not.null=Organisationscode darf nicht leer sein
dept.parent.id.length.40=ID der \u00FCbergeordneten Organisation darf nicht l\u00E4nger als 40 Zeichen sein
dept.parent.id.not.null=ID der \u00FCbergeordneten Organisation darf nicht leer sein
dept.level.not.null=Organisationsebene darf nicht leer sein
dept.level.not.operate.not.3=Derzeit k\u00F6nnen nur Organisationen der dritten Ebene bearbeitet werden
dept.not.delete=Diese Organisation enth\u00E4lt Personeninformationen und kann nicht gel\u00F6scht werden
dept.remove.user.leader.not=Kundendienstleiter k\u00F6nnen nicht aus der Organisation entfernt werden
user.self.forbidden.error=Der aktuelle Benutzer kann sich nicht selbst deaktivieren
user.self.remove.error=Der aktuelle Benutzer kann sich nicht selbst l\u00F6schen
user.role.leader.admin.not=Administratoren k\u00F6nnen nicht als Manager festgelegt werden
user.role.leader.not=Diese Person ist bereits Manager
user.role.dept.not=Die ausgew\u00E4hlte Organisation und Rolle m\u00FCssen \u00FCbereinstimmen
user.connect.id.not.null=Connect-ID darf nicht leer sein
user.dept.leader.only.error=In dieser Organisation existiert bereits ein Manager
batch.update.ids.not.null=W\u00E4hlen Sie mindestens einen Benutzer aus
batch.add.dept.not=Bitte w\u00E4hlen Sie Benutzer aus, die keiner Organisation zugeordnet sind
batch.confirm.to.be.checked=Kann keine Personen enthalten, die nicht im Status "Wartend auf Genehmigung" sind
batch.update.dept.role.not=Administratoren und Manager k\u00F6nnen die Organisation nicht \u00E4ndern
batch.update.dept.not.role.not=Organisationen k\u00F6nnen nicht zentrums\u00FCbergreifend ge\u00E4ndert werden
send.invitation.email.list.not.null=E-Mail-Liste f\u00FCr den Versand von Einladungen darf nicht leer sein
send.invitation.number.not.null=Einladungscode darf nicht leer sein
send.invitation.number.expire=Einladungscode abgelaufen

# Hinweise zur Kundendaten-Gruppierung
customer.group.create.exit=Gruppenname existiert bereits
customer.group.update.notexit=Gruppe existiert nicht
customer.group.delete.success=Gruppe erfolgreich gel\u00F6scht
customer.group.delete.exit=Gruppe enth\u00E4lt Kunden, kann nicht gel\u00F6scht werden
customer.remove.group.success=Kunde erfolgreich aus Gruppe entfernt
customer.group.name.pattern=Gruppenname darf nur aus Gro\u00DF- und Kleinbuchstaben, Zahlen und chinesischen Schriftzeichen bestehen

# Kundendaten
customer.key.not.null=Kunden-ID darf nicht leer sein
customer.no.exit=Kundendaten existieren nicht
customer.save.success=Kundendaten erfolgreich gespeichert
customer.update.error=Aktualisierung der Kundendaten fehlgeschlagen
customer.email.exit=Kundendaten mit E-Mail {0} existieren
customer.telephone.exit=Kundendaten mit Telefon {0} existieren
customer.media.exit={0} existiert mit {1} als {2} von Kundendaten
customer.filter.id.not.null=Filter-ID darf nicht leer sein
customer.filter.name.not.null=Filtername darf nicht leer sein
customer.filter.list.not.null=Filterbedingungen d\u00FCrfen nicht leer sein
customer.filter.ext.code.not.null=Filterbedingungscode darf nicht leer sein
customer.filter.ext.value.not.null=Filterbedingungswert darf nicht leer sein
customer.channel.not.null=Kanal darf nicht leer sein
customer.group.not.null=Kundengruppe darf nicht leer sein

# AWS-Konto und Connect-Fall
error.same.account=Konto-ID existiert bereits
error.null.account=Dieses Konto existiert nicht
error.null.case=Dieser Fall existiert nicht
error.null.extInst=Kundenerweiterungsinformationen d\u00FCrfen nicht leer sein

# Hinweise zu Arbeitsaufzeichnungen
error.null.record=Diese Arbeitsaufzeichnung existiert nicht
error.null.record.transfer=Dieses Ticket wird bearbeitet und kann nicht \u00FCbertragen werden
error.null.agent.online=Kein Agent online, Ticket kann nicht \u00FCbertragen werden
error.null.status=Der Datensatzstatus ist gel\u00F6st und kann nicht ge\u00E4ndert werden
work.record.contact.exists=Kundenkontaktdatensatz existiert bereits
work.record.exists=Arbeitsaufzeichnung existiert bereits
send.email.limit.count=Die Anzahl der heute gesendeten E-Mails hat das Tageslimit erreicht, bitte <NAME_EMAIL>

# Kanalkonfiguration
channel.not.config=Kanalkonfiguration nicht gefunden
# Ticket

work.record.id=Datensatz-ID
work.record.channel.name=Kanaltyp
work.record.customer.name=Kundenname
work.record.customer.link.way=Kundenkontaktinformationen
work.record.settle.name=Agentenname
work.record.status=Status
work.record.create.time=Erstellungszeit
work.record.resolve.time=L\u00F6sungszeit
work.record.file.name=Arbeitsaufzeichnung

customer.channel.not.exist=Der von Ihnen eingegebene Quellkanal {0} existiert nicht, bitte best\u00E4tigen Sie dies
customer.group.not.exist=Der von Ihnen eingegebene Gruppenname {0} existiert nicht, bitte best\u00E4tigen Sie dies
customer.part.save.fail=Das Speichern der Kundeninformationen mit der E-Mail-Adresse {0} ist fehlgeschlagen, bitte \u00FCberpr\u00FCfen und erneut speichern

# Grundlegende Informationen zu Kundendaten
customer.name=Kundenname
customer.last.name=Nachname des Kunden
customer.email=E-Mail-Adresse
customer.birthday=Geburtsdatum
customer.gender=Geschlecht
customer.phone=Telefonnummer
customer.label=Kunden-Tag
customer.company.name=Firmenname
customer.post=Position
customer.group.name=Gruppenname
customer.channel.name=Quellkanal
customer.mailing.address=Postanschrift
customer.order.address=Rechnungsadresse
customer.delivery.address=Lieferadresse
customer.other.address=Andere Adresse
customer.remark=Bemerkung
customer.info=Kundendaten
customer.grade.vip=VIP-Kunde
customer.grade.ordinary=Normaler Kunde
customer.create.time=Erstellungsdatum
customer.label.exceed = Anzahl der Tags \u00FCberschritten
customer.batch.modify.success = Gruppen\u00E4nderung erfolgreich
customer.batch.modify.fail = Gruppen\u00E4nderung fehlgeschlagen

work.number.length.40=Personalnummer darf nicht l\u00E4nger als 40 Zeichen sein
work.number.already.exist=Diese Personalnummer ist bereits vergeben
phone.number.already.exist=Diese Telefonnummer ist bereits vergeben
old.password.input.wrong=Falsches altes Passwort eingegeben
new.password.can.not.equal.old.password=Neues Passwort darf nicht mit altem Passwort \u00FCbereinstimmen
password.update.success=Passwort erfolgreich ge\u00E4ndert
property.name.already.exist=Dieser Attributname existiert bereits
property.code.already.exist=Dieser Attributcode existiert bereits
response.time.can.not.be.zero=Reaktionszeit darf nicht Null sein
resolve.time.can.not.be.zero=L\u00F6sungszeit darf nicht Null sein
work.order.type.name.or.value.can.not.be.empty=Tickettypname oder Tickettypwert darf nicht leer sein
work.order.type.name.repeat=Tickettypname wiederholt sich
work.order.type.value.repeat=Tickettypwert wiederholt sich
save.one.work.order.type.data.at.least=Mindestens einen Tickettyp-Datensatz speichern
work.record.type.id.occupied.by.work.order=Dieser Tickettyp wird von einem Ticket verwendet und kann nicht gel\u00F6scht werden
work.update.status.termination=Aktueller Ticketstatus unterst\u00FCtzt keine Beendigung
work.update.status.resolution=Aktueller Ticketstatus unterst\u00FCtzt keine L\u00F6sung
work.update.status.resolution.solved=Ticketstatus aktualisiert auf Gel\u00F6st
work.update.status.resolution.terminated=Ticketstatus aktualisiert auf Beendet
work.update.status.resolution.transferred=Ticketstatus aktualisiert auf Weitergeleitet
work.update.status.Association=Ticket ist bereits verkn\u00FCpft, bitte nicht wiederholen, Ticketnummer:
work.update.status.level=Ticket wurde nicht angepasst
work.update.status.batch.termination=Die folgenden Tickets unterst\u00FCtzen keine L\u00F6sung, Ticketnummern:
work.cannot.assign=Aktuelles Ticket kann nicht zugewiesen werden
work.cannot.claim=Aktuelles Ticket kann nicht beansprucht werden
work.solve.error=Administrator kann Tickets nicht stapelweise bearbeiten
work.cannot.binding=Aktuelles Ticket kann nicht zugewiesen werden
user.last.name.not.null=Nachname darf nicht leer sein
user.last.name.length.80=Nachname darf nicht l\u00E4nger als 80 Zeichen sein
work.update.agent.fail= Derzeit sind keine anderen Agenten online, Weiterleitung ist nicht m\u00F6glich
work.update.not.effective.agent=In der Abteilung gibt es keine weiteren verf\u00FCgbaren Agenten, Weiterleitung ist nicht m\u00F6glich

work.reminder.no = Ungemahnt
work.reminder.yes = Gemahnt

work.status.undistributed = Nicht zugewiesen
work.status.agent = Agentenbearbeitung ausstehend
work.status.custom = Kundenbearbeitung ausstehend
work.status.solve = Gel\u00F6st
work.status.termination = Beendet
work.status.transfer = \u00DCbertragen


work.accept.type.artificial = Agenten-/Admin-Zuweisung
work.accept.type.claim = Manuelle Beanspruchung
work.accept.type.automatic = Automatische Ticketzuweisung

work.create.automatic = Automatisch erstellt
work.create.artificial = Manuell erstellt


work.beyond.unresolved = Zeit\u00FCberschreitung ungel\u00F6st
work.status.unresolved = Innerhalb der Zeit ungel\u00F6st
work.timeout.resolution = Zeit\u00FCberschreitung gel\u00F6st
work.time.solve = P\u00FCnktlich gel\u00F6st
work.automatic.solve = Automatisch bearbeitet


title.ticket.id = Ticket-ID
title.channel.name = Kanalname
title.priority = Priorit\u00E4t
title.service.objectives = SLA-Serviceziele
title.ticket.state = Status
title.custom.phone = Kundentelefon
title.agent.name = Agentenname
title.ticket.describe = Ticketbeschreibung



# aigc
aigc.chat.content.id.not.null= ContentId darf nicht leer sein
aigc.chat.content.prompt.id.not.null= PromptId darf nicht leer sein
aigc.chat.content.prompt.id.length.40= PromptId darf nicht l\u00E4nger als 40 Zeichen sein
aigc.chat.content.question.not.null= Anfrage darf nicht leer sein
aigc.chat.content.answer.not.null= Antwort darf nicht leer sein

option.data.can.not.be.empty= Optionsdaten d\u00FCrfen nicht leer sein
option.name.or.value.can.not.be.empty= Optionsname oder Optionswert d\u00FCrfen nicht leer sein
option.name.repeat= Optionsname wiederholt
option.value.repeat= Optionswert wiederholt

homepage.channel.ticket.status.timeout= Zeit\u00FCberschreitung
homepage.channel.ticket.status.processing= In Bearbeitung
homepage.channel.ticket.status.pending.assignment= Zuweisung ausstehend
homepage.channel.ticket.status.resolved= Gel\u00F6st

ai.agent.inner.count.zero= Das aktuelle interne AI-Agent-Guthaben ist 0, bitte laden Sie es rechtzeitig auf!
agent.copilot.count.zero= Das aktuelle Agent-Copilot-Guthaben ist 0, bitte laden Sie es rechtzeitig auf!
ai.agent.outer.count.zero= Das aktuelle externe AI-Agent-Guthaben ist 0, bitte laden Sie es rechtzeitig auf!
aiagent.test.flow.intent.mapping.fail=Beabsichtigter Treffer fehlgeschlagen

# knowledge \u77E5\u8BC6\u5E93
knowledge.document.index.max.count= Die maximale Anzahl an Dokumenten-Wissensdatenbanken wurde erreicht. Wenden Sie sich <NAME_EMAIL>
knowledge.document.index.create.not.success= Die Dokumenten-Wissensdatenbank wurde nicht erfolgreich erstellt und kann nicht aktualisiert werden.
knowledge.document.id.not.null= Bitte w\u00E4hlen Sie eine Wissensdatenbank aus
knowledge.document.id.length.40= Die ID der Wissensdatenbank darf maximal 40 Zeichen lang sein
knowledge.document.name.not.null= Der Name der Wissensdatenbank darf nicht leer sein
knowledge.document.name.length.1000= Der Name der Wissensdatenbank darf maximal 1000 Zeichen lang sein
knowledge.document.name.pattern= Der Name der Wissensdatenbank darf nur Buchstaben, Zahlen und Bindestriche enthalten.
knowledge.document.description.length.1000= Die Beschreibung der Wissensdatenbank darf maximal 1000 Zeichen lang sein
knowledge.document.description.pattern= Die Beschreibung der Wissensdatenbank entspricht nicht dem Format
knowledge.document.type.not.null= Der Typ der Wissensdatenbank darf nicht leer sein
knowledge.document.type.list.exists= Der Typ der Wissensdatenbank muss einer der angegebenen Typen sein
knowledge.document.sync.no.doc= Es sind keine Dokumente zum Synchronisieren vorhanden
knowledge.document.syncing.job= Dokumente werden synchronisiert. Bitte versuchen Sie es sp\u00E4ter erneut
knowledge.document.sync.success=Dokumente werden synchronisiert, bitte schauen Sie sp\u00E4ter nach
knowledge.document.sync.failure= Dokumentensynchronisation fehlgeschlagen
knowledge.document.file.id.not.null= Bitte w\u00E4hlen Sie ein Dokument aus
knowledge.document.file.suffix.error= Dieser Dokumenttyp wird nicht unterst\u00FCtzt
knowledge.document.crawler.rule.id.not.null= Bitte w\u00E4hlen Sie eine Web-Crawling-Regel aus.
knowledge.document.crawler.rule.id.length.40= Die ID der Web-Crawling-Regel darf maximal 40 Zeichen lang sein
knowledge.document.crawler.rule.name.not.null= Der Name der Web-Crawling-Regel darf nicht leer sein
knowledge.document.crawler.rule.name.length.80= Der Name der Web-Crawling-Regel darf maximal 80 Zeichen lang sein
knowledge.document.crawler.rule.url.not.null= Die Web-Crawling-URL darf nicht leer sein
knowledge.document.crawler.rule.frequency.not.null= Die Web-Crawling-Frequenz darf nicht leer sein
knowledge.document.crawler.rule.update.not.null= Die Web-Crawling-Datenaktualisierungsregel darf nicht leer sein
knowledge.document.crawler.rule.deep.not.null= Die Web-Crawling-Tiefe darf nicht leer sein
knowledge.document.crawler.rule.main.url.not.null= Beschr\u00E4nkung auf das Crawlen der aktuellen Hauptdomain-Webseite darf nicht leer sein
knowledge.document.crawler.rule.main.url.list.exists= Beschr\u00E4nkung auf das Crawlen der aktuellen Hauptdomain-Webseite muss den angegebenen Typ \u00FCbergeben (0,1)
knowledge.document.crawler.rule.update.list.exists= Die Web-Crawling-Datenaktualisierungsregel muss den angegebenen Typ \u00FCbergeben (1,2)
knowledge.document.crawler.rule.deep.list.exists= Die Web-Crawling-Tiefe muss den angegebenen Wert \u00FCbergeben (1-5)
knowledge.document.crawler.rule.frequency.list.exists= Die Web-Crawling-Frequenz muss den angegebenen Typ \u00FCbergeben (1,2)
knowledge.document.crawler.rule.prefix.list.exists = Beschr\u00E4nken Sie das Krabbeln des aktuellen Webseitenpr\u00E4fixes muss angegebener Typ sein (0,1)
knowledge.document.crawler.rule.prefix.not.null = Beschr\u00E4nken Sie das Krabbeln, dass das aktuelle Webseitenpr\u00E4fix nicht leer sein kann
knowledge.document.crawler.rule.webpage.list.exists = Die maximale Anzahl zu krabbelnder Webseiten muss zwischen 1 und 100.000 liegen
knowledge.document.crawler.rule.webpage.not.null = Maximale Anzahl zu krabbelnder Webseiten darf nicht Null sein
knowledge.document.crawler.rule.content.type.list.exists = Zu krabbelnder Inhaltstyp muss angegebener Typ sein (1-4)
knowledge.document.crawler.rule.content.type.not.null = Zu krabbelnder Inhaltstyp kann nicht Null sein
knowledge.document.crawler.rule.update.not= Der aktuelle Status ist Krabbeln, eine \u00C4nderung ist vor\u00FCbergehend nicht m\u00F6glich
knowledge.document.crawler.rule.delete.not= Der aktuelle Status ist Krabbeln, l\u00F6schen ist vor\u00FCbergehend nicht m\u00F6glich
knowledge.document.crawler.rule.recrawl.not= Der aktuelle Status ist Krabbeln, erneutes Krabbeln ist vor\u00FCbergehend nicht m\u00F6glich
knowledge.document.upload.exist= {0} Dokument existiert bereits, bitte nicht erneut hochladen
knowledge.document.upload.filename.special.character= Der Dateiname darf keine Sonderzeichen enthalten

knowledge.synonym.info.not.null= Die Eingabedaten f\u00FCr die Synonymregel d\u00FCrfen nicht leer sein
knowledge.synonym.info.not.regex= Die Eingabedaten f\u00FCr die Synonymregel entsprechen nicht den Formatanforderungen
knowledge.synonym.info.num.more.than.limit= Es k\u00F6nnen maximal 10 W\u00F6rter hinzugef\u00FCgt werden

knowledge.document.knowledge= Dokumenten-Wissensdatenbank
knowledge.question.answer.knowledge= Q&A-Wissensdatenbank

knowledge.synonym.info.no.success.kendra= Derzeit ist keine Dokumenten-Wissensdatenbank erfolgreich erstellt worden, daher wird die Synchronisierung von Synonymen nicht unterst\u00FCtzt

knowledge.synonym.info.count.limit= Terminologieregeln d\u00FCrfen 10.000 eintr\u00E4ge nicht \u00FCberschreiten.
knowledge.synonym.info.file.size.limit= Die dateigr\u00F6\u00DFe f\u00FCr terminologieregeln darf 5MB nicht \u00FCberschreiten.

no.support.this.language.query= Diese Sprache wird f\u00FCr die Abfrage nicht unterst\u00FCtzt

format.not.match.requirement= In der E-Mail-Konfiguration fehlen notwendige Parameter
channel.email.account.unique.message= E-Mail-Konto existiert bereits
channel.config.robot.exceeded= Die Anzahl der Roboter hat das Limit \u00FCberschritten
instance.web.channel.limit=Es existiert bereits ein Web-Chat-Kanal dieser Sprache unter diesem Domainnamen
instance.app.channel.limit=Es existiert bereits ein App-Chat-Kanal  dieser Sprache unter diesem Domainnamen
port.error.message= IMAP/SMTP-Portnummer darf nur Zahlen enthalten und nicht l\u00E4nger als 10 Zeichen sein
imap.smtp.error.message= IMAP/SMTP-Adresse darf nur Buchstaben, Zahlen und '.' enthalten
rab.error.message= Die unbekannte Antwort des Roboters darf maximal 200 Zeichen lang sein
channel.name.error.message= Kanalname darf maximal 80 Zeichen lang sein
channel.name.unique.message= Kanalname existiert bereits
chat.welcome.content.error.message= Willkommensnachricht darf maximal 200 Zeichen lang sein
chat.title.name.error.message= Chatfenster-Name darf maximal 40 Zeichen lang sein
rab.number.error.message= Die Anzahl der Roboter hat das Limit \u00FCberschritten
answer.questions.not.null = W\u00E4hlen Sie eine Sprache

no.search.answer= Keine Antwort gefunden

data.can.not.be.empty= Zu speichernde Daten d\u00FCrfen nicht leer sein
whatsapp.phone.number.already.exist= Diese Telefonnummer existiert bereits
contactId.data.is.invalid= Ihre contactId-Daten sind ung\u00FCltig
channel.config.setting.error= Ihre Systemkonfiguration weist Probleme auf. Bitte wenden Sie sich an den Administrator

marketing.email.template.name.can.not.be.empty= Vorlagenname darf nicht leer sein
marketing.email.template.name.exist= Vorlagenname existiert bereits
marketing.email.template.content.can.not.be.empty= Vorlageninhalt darf nicht leer sein
marketing.email.template.type.can.not.be.empty= Vorlagentyp darf nicht leer sein
marketing.email.template.generate.local.template.file.error= Fehler beim Generieren der lokalen E-Mail-Vorlagendatei. Bitte wenden Sie sich an den Administrator
marketing.email.template.name.length.limit= Vorlagenname darf maximal 100 Zeichen lang sein
marketing.email.template.description.content.length.limit= Vorlagenbeschreibung darf maximal 255 Zeichen lang sein
marketing.email.template.type.can.length.limit= Vorlagentyp darf maximal 200 Zeichen lang sein
marketing.email.template.can.not.be.delete= Nur selbst erstellte Vorlagen k\u00F6nnen gel\u00F6scht werden
marketing.email.template.can.not.be.update= Nur selbst erstellte Vorlagen k\u00F6nnen bearbeitet werden
marketing.email.template.data.not.exist= Daten existieren nicht


activity.node.start = Start
activity.node.end = Ende

marketing.event.eventId.not.null= Bitte w\u00E4hlen Sie ein Marketing-Event aus
marketing.event.eventId.length.40= Die event_id des Marketing-Events darf maximal 40 Zeichen lang sein
marketing.event.activityId.not.null= Bitte w\u00E4hlen Sie eine Marketing-Aktivit\u00E4t aus
marketing.event.activityId.length.40= Die activity_id der Marketing-Aktivit\u00E4t darf maximal 40 Zeichen lang sein
marketing.event.eventName.not.null= Der Name des Marketing-Events darf nicht leer sein
marketing.event.eventName.length.100= Der Name des Marketing-Events darf maximal 100 Zeichen lang sein
marketing.event.eventType.not.null= Der Event-Typ darf nicht leer sein
marketing.event.eventType.list.exists= Der Event-Typ muss aus den vorgegebenen Typen ausgew\u00E4hlt werden
marketing.event.custom.autoEventNotificationEmail.length.255= Die E-Mail-Adresse f\u00FCr benutzerdefinierte Marketing-Event-Benachrichtigungen darf maximal 255 Zeichen lang sein
marketing.event.marketingType.list.exists= Die Marketing-Methode muss aus den vorgegebenen Typen ausgew\u00E4hlt werden
marketing.event.planabComparing.list.exists= Der Aspekt des A/B-Tests muss aus den vorgegebenen Typen ausgew\u00E4hlt werden
marketing.event.channelId.not.null= Der Marketing-Kanal darf nicht leer sein
marketing.event.channelId.length.40= Die channel_id des Marketing-Kanals darf maximal 40 Zeichen lang sein
marketing.event.detail.typeCustomerId.not.null= typeCustomerId darf nicht leer sein
marketing.event.detail.typeCustomerId.length.40= Die typeCustomerId f\u00FCr die Kundenauswahl darf maximal 40 Zeichen lang sein
marketing.event.detail.eventDetailType.list.exists= Der A/B-Test-Marketing-Typ muss aus den vorgegebenen Typen ausgew\u00E4hlt werden
marketing.event.detail.aiMarketingDescription.length.255= Die AI-Marketing-Zusammenfassung darf maximal 255 Zeichen lang sein
marketing.event.detail.aiUserPortraitGeneration.length.255= Die AI-generierte Kunden-Persona darf maximal 255 Zeichen lang sein
marketing.event.detail.customerLabels.length.255= Die automatisch f\u00FCr Kunden gesetzten Labels d\u00FCrfen maximal 255 Zeichen lang sein
marketing.event.detail.subdivisionMode.not.null= Die Methode zur Kundenauswahl darf nicht leer sein
marketing.event.detail.subdivisionMode.list.exists= Die Methode zur Kundenauswahl muss aus den vorgegebenen Typen ausgew\u00E4hlt werden
marketing.event.detail.subdivisionId.not.null= Bitte w\u00E4hlen Sie ein Kundensegment aus
marketing.event.detail.subdivisionId.length.40= Die subdivision_id des Kundensegments im Marketing-Event darf maximal 40 Zeichen lang sein
marketing.event.detail.typeContentId.not.null= Die typeContentId im Marketing-Event darf nicht leer sein
marketing.event.detail.typeContentId.length.40= Die typeContentId f\u00FCr den Versand von Inhalten im Marketing-Event darf maximal 40 Zeichen lang sein
marketing.event.detail.templateId.length.40= Die templateId der Vorlage im Marketing-Event darf maximal 40 Zeichen lang sein
marketing.event.detail.content.not.null= Der Versandinhalt darf nicht leer sein
marketing.event.detail.typeTimeId.not.null= Die typeTimeId im Marketing-Event darf nicht leer sein
marketing.event.detail.typeTimeId.length.40= Die typeTimeId f\u00FCr die Versandzeit im Marketing-Event darf maximal 40 Zeichen lang sein
marketing.event.detail.sendType.not.null= Der Versandtyp darf nicht leer sein
marketing.event.detail.sendType.list.exists= Der Versandtyp muss aus den vorgegebenen Typen ausgew\u00E4hlt werden
marketing.event.detail.sendDate.length.40= Das Versanddatum darf maximal 40 Zeichen lang sein
marketing.event.detail.sendTime.length.40= Die Versandzeit darf maximal 40 Zeichen lang sein
marketing.event.detail.sendTime.format= Das Format der Versandzeit ist hh:mm:ss
marketing.event.detail.customSendTime.format= Das Format der benutzerdefinierten Versandzeit ist yyyy-MM-dd hh:mm:ss
marketing.event.detail.useReceiverTimezone.list.exists= Ob die Zeitzone des Empf\u00E4ngers verwendet werden soll, muss mit Ja oder Nein beantwortet werden
marketing.event.detail.defaultTimezone.length.40= Wenn die Zeitzone des Empf\u00E4ngers nicht erkannt werden kann, darf die Standardzeitzone maximal 40 Zeichen lang sein
marketing.event.detail.sendDate.format= Das Format des Versanddatums ist yyyy-MM-dd
marketing.event.detail.sendDate.date.format= Wenn der Versandtyp "monatlich" ist, muss das Versanddatum zwischen 1 und 31 liegen
marketing.event.detail.sendDate.week.format= Wenn der Versandtyp "w\u00F6chentlich" ist, muss er zwischen Montag und Sonntag liegen
marketing.event.start.not.status.draft= Marketing-Events im Status "Entwurf" k\u00F6nnen nicht gestartet werden
marketing.event.start.not.status.sending= Marketing-Events im Status "Wird gesendet" oder "L\u00E4uft" k\u00F6nnen nicht gestartet werden
marketing.event.start.not.status.finish= Marketing-Events im Status "Beendet" k\u00F6nnen nicht gestartet werden
marketing.event.pause.not.status.draft= Marketing-Events im Status "Entwurf" k\u00F6nnen nicht pausiert werden
marketing.event.pause.not.status.not.start= Marketing-Events im Status "Nicht gestartet" k\u00F6nnen nicht pausiert werden
marketing.event.pause.not.status.pause= Marketing-Events im Status "Pausiert" k\u00F6nnen nicht pausiert werden
marketing.event.pause.not.status.finish= Marketing-Events im Status "Beendet" k\u00F6nnen nicht pausiert werden
marketing.event.activity.status.not.start= Die aktuelle Aktivit\u00E4t wurde nicht gestartet
marketing.event.activity.status.pause= Die aktuelle Aktivit\u00E4t ist pausiert
marketing.event.activity.status.finish= Die aktuelle Aktivit\u00E4t ist beendet

marketing.event.customer.list= Kundenliste
marketing.activity.name= Aktivit\u00E4tsname
marketing.event.channel.type= Marketing-Kanaltyp
marketing.event.send.channel= Versandkanal
marketing.event.name= Marketing-Event
marketing.event.marketing.type= Marketing-Methode
marketing.event.event.detail.type= A/B-Test-Typ
marketing.event.batch.num= Marketing-Event-Batch
marketing.event.customer.name= Kundenname
marketing.event.customer.link.way= Kundenkontaktinformationen
marketing.event.status= Status
marketing.event.error.reason= Fehlerursache

marketing.event.channel.email= E-Mail
marketing.event.channel.facebook= Facebook
marketing.event.channel.whatsapp= WhatsApp
marketing.event.channel.phone= Telefon
marketing.event.channel.web.chat= Web-Chat
marketing.event.channel.app.chat= App-Chat
marketing.event.channel.web.video= Web-Videoanruf
marketing.event.channel.app.video= App-Videoanruf

marketing.event.marketing.type1= Standardtest
marketing.event.marketing.type2= A/B-Test

marketing.event.marketing.plan.a= Plan A
marketing.event.marketing.plan.b= Plan B

marketing.event.marketing.status.delivery= Zugestellt
marketing.event.marketing.status.open= Ge\u00F6ffnet
marketing.event.marketing.status.click= Geklickt
marketing.event.marketing.status.complaint= Beschwerde
marketing.event.marketing.status.unsubscription= Abbestellt
marketing.event.marketing.status.bounce= Unzustellbar
marketing.event.marketing.status.delivery.delay= Zustellung verz\u00F6gert
marketing.event.marketing.status.reject= Abgelehnt
marketing.event.marketing.status.rendering.failure= Fehler beim Rendern

activity.not.delete = Die Kampange enth\u00E4lt Marketing-Ereignisse und kann nicht gel\u00F6scht werden
activity.name.exist = Kampagnenname existiert bereit, hinzuf\u00FCgen fehlgeschlagen
activity.update.status = Kampagnenstatus muss aus spezifierten Typen ausgew\u00E4hlt werden
activity.name.not.null = Kampagnenname darf nicht Null sein
activity.target.not.null = Kampagneziel darf nicht Null sein
activity.type.not.null = Kampagnetyp darf nicht Null sein
activity.start.time.not.null = Kampagnestartzeit darf nicht Null sein
activity.end.time.not.null = Kampagneendzeit darf nicht Null sein
marketing.event.test.send.email.content.empty=E-Mail-Versandinhalt nicht festgelegt

marketing.event.email.status.yes=Ja
marketing.event.email.status.no=Nein


company.already.register = Der von Ihnen im Marketplace abonnierte Vertrag wurde bereits registriert und wird derzeit von uns gepr\u00FCft. Bitte senden Sie keine doppelten Registrierungsinformationen!

# Statistik
contact.id = Kontakt-ID
call.time = Agent-Antwortzeit
end.time = Auflegzeit
acw.time = ACW-Endzeit
total.time = Gesamte Gespr\u00E4chszeit
interaction.time = Interaktionszeit
queue.wait.time = Wartezeit in der Warteschlange
call.channel = Kanal
reception.agent = Empfangender Agent
agent.group = Agentengruppe
queue.name = Warteschlange
acw.duration = ACW-Dauer
work.order.number = Zugeh\u00F6rige Ticketnummer
on.hold.time = Agent-OnHold-Zeit
on.hold.number = Agent-OnHold-Anzahl
is.switch = Ist Weiterleitung
hanging.type = Auflegetyp 
system.phone = Systemtelefon
satisfaction.rating = Zufriedenheitsbewertung

start.time = Startzeit
in.coming.out.coming = Eingehend/Ausgehend
customer.phone2 = Kundentelefon
initial.contact.id = Urspr\u00FCngliche Kontakt-ID
previous.contact.id = Vorherige Kontakt-ID
next.contact.id = N\u00E4chste Kontakt-ID

agent.name = Agentenname
accumulated.online.duration = Kumulierte Online-Zeit
accumulated.idle.duration = Kumulierte Leerlaufzeit
accumulated.reception.duration = Kumulierte Empfangszeit
task.time.utilization.rate = Arbeitszeitauslastung
unresponsive.quantity = Anzahl nicht beantworteter Anrufe
reception.contacts.quantity = Anzahl empfangener Kontakte
response.rate = Antwortrate
avg.working.hours.after.contact = Durchschnittliche Nachbearbeitungszeit pro Kontakt
avg.customer.retention.time = Durchschnittliche Kundenhaltezeit

#queue.name = Warteschlangenname
#avg.working.hours.after.contact = Durchschnittliche Nachbearbeitungszeit pro Kontakt
avg.agent.interaction.time = Durchschnittliche Agenteninteraktionszeit
#avg.customer.retention.time = Durchschnittliche Kundenhaltezeit
avg.queue.abandonment.time = Durchschnittliche Abbruchzeit in der Warteschlange
avg.queue.waiting.time = Durchschnittliche Wartezeit in der Warteschlange
abandon.contact.quantity = Anzahl abgebrochener Kontakte
queued.contacts.quantity = Anzahl Kontakte in der Warteschlange

real.work.ticket= Manuelles Ticket
robot.work.ticket= Roboter-Ticket
resolve.ticket.count= Anzahl gel\u00F6ster Tickets
un.resolve.ticket.count= Anzahl ungel\u00F6ster Tickets
waiting.for.reply.ticket.count= Anzahl Tickets, die auf Kundenantwort warten

agenty.name= Agentenname
work.order.count= Gesamtanzahl Tickets
work.order.avg.resolution.time= Durchschnittliche Ticketl\u00F6sungszeit
rank= Rang
#customer.name= Kundenname
customer.work.order.count= Anzahl der Tickets des Kunden
#satisfaction.rating= Zufriedenheitsbewertung

#web.chat.count= Anzahl Web-Chat-Tickets
#phone.count= Anzahl Telefon-Tickets
#app.chat.count= Anzahl APP-Chat-Tickets
#email.count= Anzahl E-Mail-Tickets
#whatsapp.count= Anzahl WhatsApp-Tickets
#web.online.video.count= Anzahl Web-Online-Video-Tickets

zero.to.three.hour= 0-1 Stunde
one.to.three.hour= 1-3 Stunden
three.to.eight.hour= 3-8 Stunden
over.eight.hour= \u00DCber 8 Stunden

statistic.work.record.sheet.name= Statistikbericht
statistic.export.agent.group.name= Agentengruppenname
statistic.export.agent.name= Agentenname
statistic.export.work.order.total.count= Gesamtanzahl Tickets
statistic.export.work.order.percent= Prozentsatz
statistic.export.work.order.feedback.rate= Bewertungsrate
statistic.export.work.order.classification= Klassifizierung
statistic.export.time.range= Zeitraum

statistic.export.work.record.filename1= Von Agenten bearbeitete Gesamtzahl der Tickets
statistic.export.work.record.filename2= Ticketanzahl nach Kanal
statistic.export.work.record.filename3= Ticketstatusverteilung
statistic.export.work.record.filename4= Tickettypenverteilung
statistic.export.work.record.filename5= Ticketpriorit\u00E4tsverteilung
statistic.export.work.record.filename6= Durchschnittliche Ticketl\u00F6sungszeit
statistic.export.work.record.filename7= Verteilung der Ticketbearbeitungszeit
statistic.export.work.record.filename8= SLA-Bericht
statistic.export.work.record.filename9= Zufriedenheitsbericht_Agentendimension
statistic.export.work.record.filename10= Zufriedenheitsbericht_Tickettypdimension
statistic.export.work.record.filename11= Zufriedenheitsbericht_Kanaldimension
statistic.export.work.record.filename12= Roboter-Ticketbericht
statistic.export.work.record.filename13= Anzahl der Roboter-Tickets nach Kanal
statistic.export.work.record.filename14= Anteil der Roboter-Tickets
statistic.export.work.record.filename15= Trend der Ticketbearbeitungsmenge jedes Agenten
statistic.export.work.record.filename16= Trend der durchschnittlichen Zufriedenheitsbewertung jedes Agenten
statistic.export.work.record.filename17= Trend der Ticketbearbeitungszeit jedes Agenten
statistic.export.work.record.filename18= Feedbackrate jedes Agenten
statistic.export.work.record.filename19= Top 10 Kunden mit den meisten Tickets abfragen

statIndex.export.work.record.filename1= Kontaktdetails
statIndex.export.work.record.filename2= Historische Arbeitskennzahlen der Agenten
statIndex.export.work.record.filename3= Historische Warteschlangenkennzahlen

connect.alias= Kontaktpfad

knowledge.space.limit.warn= Ihr Wissensdatenbankspeicherplatz hat die Obergrenze erreicht. Bitte wenden Sie sich an den Administrator.
#connect.alias= Kontaktpfad

# Details zur Selbsteinsch\u00E4tzung
evaluation.type= Bewertungstyp
knowledge.name= Name der Wissensdatenbank
channel.type= Kanaltyp
channel.name= Quellkanal
questions= Kundenfragen
robot.answer= Erhaltene Antworten
word.record.code= Ticketnummer
average.score= Durchschnittliche Genauigkeitsbewertung
loyalty= Bewertung der Faktentreue
answer.relevance= Antwortrelevanz
context.accuracy= Kontextgenauigkeit
semantic.similarity.answers= Semantische \u00C4hnlichkeit der Antworten
correct.answer= Korrektheit der Antwort
custom.name= Fragender Kunde
custom.question.time= Interaktionszeit

statIndex.export.self.assessment.filename1= Detailbericht zur Selbsteinsch\u00E4tzung
self.assessment.scoreFile.time.row= Zeit
self.assessment.one.score.filename2= Trenddiagramm der Genauigkeitsbewertung der Selbsteinsch\u00E4tzung
self.assessment.multi.scores.filename3= Trenddiagramm der Genauigkeitsbewertung verschiedener Dimensionen der Selbsteinsch\u00E4tzung

manual.evaluation.type= Manuelle Bewertung
automatic.evaluation.type= Automatische Bewertung

channel.diff.id.unique.message= Konto-ID existiert bereits

statIndex.export.like.dislike.filename= Detailliste "Gef\u00E4llt mir"/"Gef\u00E4llt mir nicht"
interaction.type= Interaktionstyp
liked= Gef\u00E4llt mir
disliked= Gef\u00E4llt mir nicht
no.operation= Keine Aktion
custom.voice.column.like= Gef\u00E4llt mir
custom.voice.column.dislike= Gef\u00E4llt mir nicht
custom.voice.filename1= Trenddiagramm der Interaktionsdaten "Gef\u00E4llt mir"/"Gef\u00E4llt mir nicht"
custom.voice.filename2= Anzahl der Interaktionen "Gef\u00E4llt mir"/"Gef\u00E4llt mir nicht" nach Kanal

answer.type = Antworttyp
answer.type1 = Normale FAQ-Antwort
answer.type2 = RAG-Wissensmangel
answer.type3 = RAG wei\u00DF nicht
answer.type4 = Normale RAG-Antwort

channel.facebook.id.unique.message= Facebook-Seite existiert bereits
channel.instagram.id.unique.message= Instagram-Konto existiert bereits
channel.line.id.unique.message= Line-Konto existiert bereits
channel.wechat.biz.id.unique.message= WeChat Work-Konto existiert bereits
channel.wechat.official.account.id.unique.message= WeChat Official Account existiert bereits

channel.empower.fail = Autorisierung fehlgeschlagen, bitte kontaktieren Sie den Administrator

agent.access.channel= Agentenzugriffskanal darf nicht leer sein
receive.ticket.type.not.null= Chat-Ticket-Empfangsmodus darf nicht leer sein

#Kunden-Tags
customer.tag.save.one.data.at.least= Es muss mindestens ein Tag-Datensatz gespeichert werden
customer.tag.type.content.or.color.can.not.be.empty= Tag-Inhalt oder Tag-Farbe d\u00FCrfen nicht leer sein
customer.tag.content.repeat= Tag-Inhalt ist doppelt vorhanden
customer.tag.content.length.limit= Tag-Inhalt darf nicht l\u00E4nger als 200 Zeichen sein
customer.tag.content.already.exist = Der von Ihnen eingegebene Tag-Inhalt {0} ist bereits vorhanden, bitte best\u00E4tigen Sie
customer.tag.operate.one.data.at.least= Es muss mindestens ein Tag-Datensatz bearbeitet werden
customer.tag.category.content.can.not.be.empty= Tag-Kategorieinhalt darf nicht leer sein
customer.tag.category.content.length.limit= Tag-Kategorieinhalt darf nicht l\u00E4nger als 200 Zeichen sein
customer.tag.category.content.repeat= Tag-Kategorie existiert bereits, bitte nicht doppelt hinzuf\u00FCgen
customer.tag.category.in.use.can.not.delete= Diese Kategorie wird bereits verwendet und kann nicht gel\u00F6scht werden
customer.tag.batch.add.result= Sie haben dieses Mal Tags f\u00FCr {0} Kunden hinzugef\u00FCgt, erfolgreiche Kunden: {1}, fehlgeschlagene Kunden: {2}.
customer.tag.scope.select.one.at.least= W\u00E4hlen Sie mindestens einen Anwendungsbereich f\u00FCr das Tag aus

#Hotline-Metriken
hotline.missed.call= Verpasste Anrufe
hotline.ivr.abandon= IVR-Abbruch
hotline.queue.abandon= Warteschlangenabbruch
hotline.non.service.time.inbound= Eingehende Anrufe au\u00DFerhalb der Servicezeiten
hotline.non.service.time.calling= Gespr\u00E4ch l\u00E4uft
hotline.non.service.time.queue= In der Warteschlange
hotline.non.service.time.inbound.num= Anzahl eingehender Anrufe
hotline.non.service.time.outbound.num= Anzahl ausgehender Anrufe

#AiAgent-bezogen
intent.category.delete.failure= Unter der aktuellen Intent-Kategorie existieren Intents. L\u00F6schen nicht m\u00F6glich. Bitte l\u00F6schen Sie zuerst die Intents unter der aktuellen Kategorie.
intent.delete.failure= Unter dem aktuellen Intent existieren Smart Agents. L\u00F6schen des Intents nicht m\u00F6glich.
agent.binding.failure= Pro Kanaltyp und Intent kann nur ein Smart Agent existieren.
intent.name.duplicate= Intent-Name darf nicht doppelt vorkommen.
intent.script.max.limit= Ein Intent kann maximal 3 Dialogskripte enthalten
intent.attribute.max.limit= Ein Intent kann maximal 4 Variablen enthalten.
agent.not.found= Entsprechende Smart-Agent-Informationen nicht gefunden.
agent.name.duplicate= Smart-Agent-Name darf nicht doppelt vorkommen
agent.create.beyond= Die Anzahl der erstellten Smart Agents \u00FCberschreitet das Limit.
intent.create.beyond= Die Anzahl der erstellten Intents \u00FCberschreitet das Limit
intent.update.script.type=Die Absicht wurde an einen intelligenten Agenten gebunden und kann den Skripttyp nicht \u00E4ndern
agent.voice.binding.phone=Die Nummer {0} ist bereits mit einem anderen Agenten verkn\u00FCpft. Bitte versuchen Sie es mit einer anderen Nummer.

agent.create.user.chanel.fail = Die verf\u00FCgbaren Pl\u00E4tze f\u00FCr Channel {0} sind voll. Bitte kontaktieren Sie den ConnectNow-Administrator, um ein Upgrade durchzuf\u00FChren.
agent.agent.status.creat.fail = Der aktuelle Name {0} ist bereits vorhanden, bitte benennen Sie ihn um
agent.agent.sal.rul.creat.fail = Der aktuelle Regelname {0} ist bereits vorhanden, bitte benennen Sie ihn um
agent.agent.sal.rul.channel.record.creat.fail = Der aktuelle Kanaltyp {0} und der Tickettyp {1} sind bereits vorhanden, bitte w\u00E4hlen Sie erneut aus
agent.agent.sal.rul.channel.creat.fail = Der aktuelle Kanaltyp {0} ist bereits vorhanden, bitte w\u00E4hlen Sie erneut aus
agent.agent.sal.rul.record.creat.fail = Der aktuelle Tickettyp {0} ist bereits vorhanden, bitte w\u00E4hlen Sie erneut aus
agent.agent.dept.max.active.chat.num = Die maximale Anzahl aktiver Chats pro Sitzplatz darf 100 nicht \u00FCberschreiten

#Abrechnung
ai.agent.outer.usage.limit= Unzureichendes Guthaben: Die verbleibenden AIGC-Aufrufe Ihres externen Smart Agents betragen weniger als {0}. Bitte laden Sie Ihr Konto rechtzeitig auf.
ai.copilot.usage.limit= Unzureichendes Guthaben: Die verbleibenden AIGC-Aufrufe Ihres Agentenassistenten betragen weniger als {0}. Bitte laden Sie Ihr Konto rechtzeitig auf.
ai.robot.phone.usage.limit= Unzureichendes Guthaben: Die verbleibenden Minuten Ihres Telefonroboters betragen weniger als {0} Minuten. Bitte laden Sie Ihr Konto rechtzeitig auf.
ai.phone.expenses.usage.limit=Unzureichendes Guthaben: Ihr aktuelles Gespr\u00E4chsguthaben betr\u00E4gt weniger als {0}. Bitte laden Sie es rechtzeitig auf.
ai.agent.usage.limit=Ungen\u00FCgende deckung: der reste ihres derzeitigen geheimdienstk\u00F6rpers AIGC kann nicht oft genug ausgegeben werden um rechtzeitig ausgez\u00E4hlt zu werden

channel.company.pay.version.add.limit= Die aktuelle Version Ihres Unternehmens unterst\u00FCtzt nur das Hinzuf\u00FCgen von {0} Kanaltypen

#\u667A\u80FD\u586B\u5355
ticket.smart.fill.ticket.type.can.not.be.empty=Bitte w\u00E4hlen sie einen tickettyp aus
ticket.smart.fill.configure.properties.can.not.be.empty=Konfigurationsfeld darf nicht leer sein
ticket.smart.fill.attr.name.can.not.be.empty=Feldname darf nicht leer sein
ticket.smart.fill.attr.value.example.can.not.be.empty=Beispielwert darf nicht leer sein
ticket.smart.fill.attr.describe.can.not.be.empty=Beschreibung darf nicht leer sein
ticket.smart.fill.attr.name.repeat=Feldname existiert bereits
ticket.smart.fill.language.and.type.union.exist=Kombination aus sprachcode und tickettyp existiert bereits
ticket.smart.fill=Regel f\u00FCr intelligente ticketausf\u00FCllung f\u00FCr diesen tickettyp nicht gefunden. Kontaktieren sie den admin, um eine unter systemeinstellungen > Intelligente ticketausf\u00FCllung hinzuzuf\u00FCgen.

work.message.reminding.add.fail=Dieser nachrichteninhalt oder diese erinnerungszeit existiert bereits. Bitte \u00E4ndern und erneut versuchen.
work.message.reminding.update.fail=Dieser nachrichteninhalt oder diese erinnerungszeit existiert bereits. Bitte \u00E4ndern sie es.

work.translate.ok=\u00DCbersetzungsaufgabe \u00FCbermittelt. Bitte aktualisieren sie die seite sp\u00E4ter, um die ergebnisse anzuzeigen.



# \u5E73\u53F0\u7528\u6237\u7BA1\u7406 \u521B\u5EFA\u7528\u6237\u548C\u66F4\u65B0\u7528\u6237 \u6839\u636E\u4E0D\u540C\u89D2\u8272\u9650\u5236\u56E2\u961F\u7EA7\u522B\u9009\u62E9
#\u5BA2\u670D\u4EBA\u5458\uFF1A
user.platform.call.center.limit=F\u00FCr die Erstellung k\u00F6nnen nur Teams der dritten Ebene unter Contact Center ausgew\u00E4hlt werden
#\u8FD0\u8425\u4EBA\u5458\uFF1A
user.platform.marketing.center.limit=Bei der Erstellung k\u00F6nnen nur Teams der dritten Ebene unter dem Marketing Center ausgew\u00E4hlt werden
#\u7BA1\u7406\u5458\uFF1A
user.platform.company.level.limit=Administratoren k\u00F6nnen nur unter Organisationen der ersten Ebene des Unternehmens erstellt werden

#TikTok\u6E20\u9053
tiktok.not.exist.country.code.app.auth.info=Die lizenzinformationen f\u00FCr die aktuelle region sind nicht vorhanden, bitte \u00FCberpr\u00FCfen sie diese
tiktok.use.auth.info.get.token.error=Fehler beim abrufen des tokens bei der autorisierung
tiktok.use.auth.info.get.shop.info.error=Fehler beim abrufen der shop-informationen nach der autorisierung
tiktok.channel.create.shop.info.must.select=Ein shop muss ausgew\u00E4hlt werden
tiktok.channel.create.shop.info.must.only.one=Dieser shop wird bereits verwendet, bitte w\u00E4hlen sie einen anderen aus
tiktok.channel.create.shop.info.auth.code.can.not.be.empty=Bitte geben sie den autorisierungscode an
discord.channel.token.invalid=Der angegebene token ist ung\u00FCltig

#\u5BA2\u6237\u5934\u50CF\u989C\u8272\u8BBE\u7F6E
customer.photo.color.setting.info.can.not.be.empty=Die einstellungen f\u00FCr die farbe des kunden-avatars d\u00FCrfen nicht leer sein
customer.photo.color.setting.rule.count.limit=Es k\u00F6nnen nur {0} regeln f\u00FCr die farbe des kundenavatars hinzugef\u00FCgt werden
customer.photo.color.in.use=Die ausgew\u00E4hlte farbe wird bereits von einer anderen regel verwendet
customer.photo.color.same.tag.in.use=Der tag '{1}' unter der kategorie '{0}' existiert bereits in einer anderen farbregel f\u00FCr den avatar


batch.faq.ok=Der upload wird verarbeitet, bitte aktualisieren sie die seite sp\u00E4ter

batch.faq.fail=Diese datei wird bereits hochgeladen, bitte aktualisieren sie die seite sp\u00E4ter, um sie anzuzeigen

discord.channel.application.id.exist=Diese app-id existiert bereits
discord.channel.bot.token.exist=Das bot-token ist bereits vorhanden

# AI\u667A\u80FD\u8D28\u68C0
call.center.ticket.not.chat.history=Der aktuelle Ticketverlauf ist zu kurz und erf\u00FCllt nicht die Mindestanforderungen f\u00FCr die intelligente Qualit\u00E4tspr\u00FCfung